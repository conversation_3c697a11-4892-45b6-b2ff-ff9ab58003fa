const CoalesceError = require("@coalesce/coalesceError");
const { ComponentRequestError, sendRequest } = require("@coalesce/request");
const { ConnectionManager, EndpointManager } = require("@coalesce/utils");
const { ENDPOINTS_ETCD_KEY } = require("@coalesce/env");
const { getHotConfig } = require("../../../server/HotConfig");

class CrediverseConnectionPoolException extends CoalesceError {
  constructor(...params) {
    super(CrediverseConnectionPoolException, ...params);
  }
}

const DEFAULT_REQUEST_TIMEOUT = 30000;
const REFRESH_BEFORE_EXPIRY_MS = 20000;

const HTTP_400_BAD_REQUEST = 400;
const HTTP_401_UNAUTHORIZED = 401;

const getFactory = (endpoint) => {
  return {
    create: () => {
      return new Promise(async (resolve, reject) => {
        try {
          console.info("Starting Crediverse API OAuth request");

          const { hostname, port } = endpoint;
          const { client_id, client_secret, username, password } = endpoint.auth || {};

          const timeout = parseInt(endpoint.timeout, 10) || DEFAULT_REQUEST_TIMEOUT;

          const url = `http://${client_id}:${client_secret}@${hostname}:${port}/oauth/token`;
          const body =
            "grant_type=password&" + //
            `username=${encodeURIComponent(username)}&` +
            `password=${encodeURIComponent(password)}`;
          const creds = Buffer.from(`${client_id}:${client_secret}`).toString("base64");
          const headers = {
            Authorization: `Basic ${creds}`,
            "Content-Type": "application/x-www-form-urlencoded",
          };

          const requestData = { url, body, headers, timeout };

          console.debug(`Headers::: Content-Type: ${headers["Content-Type"]}`);
          console.debug("Headers::: Authorization: Basic <base64-of---client_id:client_secret>");
          console.debug("Body::: 'grant_type=password&username=<username>&password=<password>'.");
          console.debug("Where <username> and <password> are filled in using the chosen endpoint");

          let response;

          try {
            response = await sendRequest(requestData);
          } catch (requestError) {
            const { status } = requestError;

            if ([HTTP_400_BAD_REQUEST, HTTP_401_UNAUTHORIZED].includes(status)) {
              let errorText =
                status === HTTP_400_BAD_REQUEST
                  ? "Unable to authenticate, your account may be locked out?"
                  : "Please check the credentials provided?";
              requestError = new CrediverseConnectionPoolException(
                requestError,
                `Crediverse /oauth/token request failed. ${errorText}`
              );
            }

            throw requestError;
          }

          const data = response.data || {};

          if (!data.access_token || !parseInt(data.expires_in, 10)) {
            let invalidTokenError = new CrediverseConnectionPoolException(
              null,
              "Invalid/Missing token or expiry information in oauth response, did oauth fail?"
            );
            console.error(invalidTokenError);
            console.debug("Crediverse /oauth/token request failed, axios response:", response);
            throw invalidTokenError;
          }

          const now = new Date();
          const expires_in_ms = Number(data.expires_in) * 1000;
          data.accessTokenMarginalExpiry = new Date(now.getTime() + expires_in_ms - REFRESH_BEFORE_EXPIRY_MS);

          console.info("New Crediverse oauth token successfully retrieved. Token data: ", data);

          resolve(data);
        } catch (err) {
          if (err instanceof CrediverseConnectionPoolException) {
            reject(err);
          } else {
            let factoryError = new CrediverseConnectionPoolException(err, "Failed to login to Crediverse API");
            reject(factoryError);
          }
        }
      });
    },
    validate: (tokenData) => {
      const now = new Date();
      if (tokenData.accessTokenMarginalExpiry < now) {
        console.warn(
          "Crediverse token will expire too soon or has already expired. " +
            "We are invalidating the pool connection and a new oauth token request will be made!",
          tokenData
        );
        return Promise.resolve(false);
      }

      return Promise.resolve(true);
    },
    destroy: (tokenData) => {
      return Promise.resolve({ destroyedTokenData: tokenData });
    },
  };
};

let activeEndpointName = "";

let connectionManager;
let poolInitialized = false;

const getActiveConnection = async (ctx) => {
  if (poolInitialized === false) {
    const crediverseEndpointManager = new EndpointManager("crediverse");
    activeEndpointName = crediverseEndpointManager.getEndpointNameFromContext(ctx);
    const endpoint = crediverseEndpointManager.getEndpointFromContext(ctx);

    connectionManager = new ConnectionManager({ acquireTimeoutMillis: endpoint.timeout });
    const factory = getFactory(ctx.connectors.crediverse[activeEndpointName]);
    connectionManager.initializeFactory(factory); // <= not async, only RE-init is async

    const key = ENDPOINTS_ETCD_KEY;
    console.info(`Crediverse Library - Creating HotConfig watcher for key: '${key}'`);
    await getHotConfig().createAndWatch(key, null, reinitializeFactory);

    poolInitialized = true;
  }

  return connectionManager;
};

const reinitializeFactory = async (updatedConnectorsString) => {
  const connectors = JSON.parse(updatedConnectorsString);
  const crediverseEndpoints = connectors.crediverse;

  /**
   * FIXME: We use only the FIRST connection in the array
   *  Once we have connection pooling... we must update this to use all connections 
   */
  const endpoint = crediverseEndpoints[activeEndpointName][0];
  /**
   *
   */

  const factory = getFactory(endpoint);
  await connectionManager.reinitializeFactory(factory, { acquireTimeoutMillis: endpoint.timeout });

  return connectionManager;
};

module.exports = { getActiveConnection, CrediverseConnectionPoolException };
