require('./logger');
const componentManager = require('./componentManager');
const menu = require('./menu');
const Context = require('./Context');
const { getLanguageMap } = require("@coalesce/utils");
const InternalServerError = require('./internalServerError');
const endpointConnectionIndexer = require('../common/@coalesce/EndpointConnectionIndexer');

let componentManagerLoaded = false;

class Session {
  constructor(config) {
    if (!componentManagerLoaded) {
      componentManager.load();
      componentManagerLoaded = true;
    }

    const cfg = typeof config === 'string' ? fromJson(config) : config;

    this.ctx = new Context();
    this.config = cfg.menu_code;
    this.script = new menu.Script(this.config);
    this.cmd = null;
    this.breakpoints = [];
    this.componentManager = componentManager.newInstance();
    this.ctx.variables.componentManager = this.componentManager;
    this.ctx.variables.langmap = getLanguageMap();

    this.componentManager.loadPreviewer();

    // todo: load from request
    //this.components = loadComponents(kComponentsDir);
    if (cfg.components) this.componentManager.loadFromConfig(cfg.components);

    if (cfg.connectors) this.componentManager.loadConnectorsFromConfig(cfg.connectors);

    // this.ctx.connectors = endpointConnectionIndexer.flattenConnectionArray(this.componentManager.connectors);
    this.ctx.connectors = this.componentManager.connectors;

    this.script.dump();
  }

  reset() {
    this.ctx.reset();
    this.cmd = null;
  }

  async processContinue(r) {
    this.setVariables(r.vars_set);
    this.clearVariables(r.vars_clear);
    return this.continue(r);
  }

  executeOneshot(request) {
    let match = request.input.split('*');
    match.shift();
    match.shift();
    request.gotolabel = null;
    let result = this.script.matchOneshotRequest('*' + match.join('*'));
    if (result.matched) request.gotolabel = result.gotolabel
    return result;
  }

  async processContinueRequest(r) {
    let result = null;
    let oneshot = this.executeOneshot(r)
    if (r.input && !oneshot.matched) {
      if (r.input[0] === '*' && r.input[r.input.length - 1] === '#') {
        this.ctx.USSDServiceCode = r.input.split('*')[1].replace(/#$/, '');
        let ussdString = r.input.substr(1, [r.input.length - 2]);
        let ussdCommands = ussdString.split('*');
        let ussdCode = ussdCommands[0];
        if (ussdCommands.length > 1) {
          let req = Object.assign({}, r);
          req.input = '*' + ussdCommands[0] + '#';
          this.ctx.input = req.input;
          result = await this.processContinue(req);
          for (let i = 1; i < ussdCommands.length; i++) {
						if (result)result[1] = '';
            req.input = ussdCommands[i];
            this.ctx.input = req.input;
            result = await this.processContinue(req);
            if (!result.response && i < ussdCommands.length - 1) {
              return {
                done: true,
                line: this.ctx.sp,
                output: 'Invalid USSD code',
                response: false,
              };
            }
          }
        } else {
          this.ctx.input = r.input;
          result = await this.processContinue(r);
        }
      } else {
        this.ctx.input = r.input;
        result = await this.processContinue(r);
      }
    } else {
      this.ctx.input = r.input;
      this.ctx.setvars(oneshot.ussdVars);
      result = await this.processContinue(r);
    }

    return result;
  }

  async processRequest(r) {
    //console.debug('processRequest params: ', r);

    if (!r.command) {
      return this.continue();
    }

    //console.debug('breakpoints?', this.breakpoints);

    console.debug(`Executing ${r.command} debug command with input '${r.input}', vars_set ${r.vars_set}, vars_clear [${r.vars_clear}]`);

    switch (r.command) {
      case 'run':
        {
          const bpts = r.breakpoints;
          if (bpts && Array.isArray(bpts)) this.breakpoints = bpts;

          return this.continue();
        }
        break;

      case 'CONTINUE':
        return this.processContinueRequest(r);

      case 'STEP':
        this.ctx.input = r.input;
        this.setVariables(r.vars_set);
        this.clearVariables(r.vars_clear);
        return this.step();

      case 'BREAKPOINT':
        if (r.input) this.addBreakpoint(r.input);
        break;

      case 'DELETE':
        if (r.input) this.removeBreakpoint(r.input);
        break;
    }

    return this.makeResponse({ response: false });
  }

  createContextRequest(ctx, r) {
    let newCtx = Object.assign(ctx, { request: {} });

    /* ================================ */
    /* This section might be redundant :/ */
    if (r.input[0] === '*' && r.input[r.input.length - 1] === '#') {
      ctx.currentUssdCode = r.input.substr(1, r.input.length - 2);
      newCtx.request.USSDRequestString = '';
    } else {
      if (r.input[0] === '*') r.input = r.input.substr(1, r.input.length - 1);
      if (r.input[r.input.length - 1] === '#') r.input = r.input.substr(0, r.input - 1);
      newCtx.request.USSDRequestString = r.input;
    }
    /* ================================ */

    newCtx.request.USSDServiceCode = ctx.USSDServiceCode;
    newCtx.request.MSISDN = ctx.variables.MSISDN;
    newCtx.request.IMSI = ctx.variables.IMSI;
    newCtx.request.response = ctx.waiting;
    newCtx.request.TransactionId = Math.floor(100000000 + Math.random() * 900000000);

    return newCtx;
  }

  // private
  async continue(r) {
    this.ctx.output = '';

    if (this.breakpoints.length == 0) {
      return this.continueNoBreak(r);
    }

    while (this.script.stepOver(this.ctx)) {
      if (this.ctx.waiting) {
        const componentName = this.script.isComponentCall(this.ctx);
        if (componentName) {
          let result = await this.componentManager.execute(this.createContextRequest(this.ctx, r), componentName);
          if (!result.success) {
            return new InternalServerError();
          }
          continue;
        }

        return this.makeResponse();
        //return this.makeResponse({ response:false });
      }

      for (let i = 0; i < this.breakpoints.length; ++i) {
        if (this.breakpoints[i].line == this.ctx.sp) {
          return this.makeResponse({ response: false, node_id: this.ctx.node_id });
        }
      }
    }

    return this.makeResponse({ done: true });
  }

  async continueNoBreak(r) {
    if (typeof r.gotolabel === 'string') {
      this.ctx.sp = this.script.labels.get(r.gotolabel);
    }
    if (typeof this.ctx.componentReturn !== 'undefined' && this.ctx.componentReturn) {
      const componentName = this.script.isComponentCall(this.ctx);
      if (componentName) {
        let result = await this.componentManager.execute(this.createContextRequest(this.ctx, r), componentName);
        if (!result.success) {
          return new InternalServerError();
        }
        this.ctx.output = result.ctx.output;
        this.ctx.waiting = result.ctx.respond;
        this.ctx.componentReturn = result.ctx.respond;
        if (result.done) {
          this.ctx.sp++;
          this.ctx.waiting = false;
          this.ctx.input = '';
        } else return this.makeResponse({ done: false });
      }
    }

    while (true) {
      const done = !this.script.step(this.ctx);

      const componentName = this.script.isComponentCall(this.ctx);
      if (componentName) {
        let result = await this.componentManager.execute(this.createContextRequest(this.ctx, r), componentName);
        if (!result.success) {
          return new InternalServerError();
        }
        this.ctx.output += result.ctx.output;
        this.ctx.waiting = result.ctx.respond;
        this.ctx.componentReturn = result.ctx.respond;
        if (result.done) {
          this.ctx.sp++;
          this.ctx.waiting = false;
          continue;
        }
      }

      return this.makeResponse({ done });
    }

    return {};
  }

  async step() {
    this.ctx.output = '';
    const done = !this.script.stepOver(this.ctx);
    return this.makeResponse({ done, node_id: this.ctx.node_id });
  }

  addBreakpoint(id) {
    const i = this.script.findStep(id);
    if (i < 0) return false;

    this.breakpoints.push({ line: i, id });
    return true;
  }

  removeBreakpoint(id) {
    for (let i = 0; i < this.breakpoints.length; ++i) {
      const bp = this.breakpoints[i];
      if (bp.id === id) {
        this.breakpoints.splice(i, 1);
        break;
      }
    }
  }

  isValidMenuConfig() {
    return this.script.isValid();
  }

  makeResponse(r) {
    const resp = Object.assign({ done: false, output: this.ctx.output, vars: this.ctx.variables, line: this.ctx.sp, response: this.ctx.waiting }, r);
    console.debug('Session makeResponse() Response:', resp);
    return resp;
  }

  setVariable(name, value) {
    this.ctx.variables[name] = value;
  }

  clearVariable(name) {
    delete this.ctx.variables[name];
  }

  setVariables(varsStr) {
    if (!varsStr) {
      return;
    }

    const vars = JSON.parse(varsStr);
    for (const name in vars) {
      this.setVariable(name, vars[name]);
    }
  }

  clearVariables(vars) {
    for (const v of vars) {
      this.clearVariable(v);
    }
  }
}

function fromJson(config) {
  const namespace = process.env.NAMESPACE ? process.env.NAMESPACE : process.env.STAGING_NAMESPACE ? process.env.STAGING_NAMESPACE : 'default';
  return JSON.parse(config.replace(/%NAMESPACE%/g, namespace));
}

module.exports = Session;
