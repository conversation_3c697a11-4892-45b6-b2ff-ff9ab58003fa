function createDeployRequest(name, image, version, prod) {
  const port = 5000;
  const HTTP_SERVER_PORT = 8084;
  const namespace = `cs-core-staging`;

  return {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: { name, namespace },
    spec: {
      selector: { matchLabels: { run: name } },
      replicas: 1,
      //strategy: { type: 'Recreate' },
      strategy: { type: 'RollingUpdate', rollingUpdate: { maxSurge: 1, maxUnavailable: 0 } },
      template: {
        metadata: { labels: { run: name, version: version }, creationTimestamp: new Date().toISOString() },
        spec: {
          containers: [
            {
              name,
              image,
              imagePullPolicy: 'Always',
              livenessProbe: {
                failureThreshold: 3,
                httpGet: {
                  path: "/",
                  port: 5000,
                  scheme: "HTTP"
                },
                initialDelaySeconds: 15,
                periodSeconds: 10,
                successThreshold: 1,
                timeoutSeconds: 15
              },
              ports: [
                { containerPort: 5000, name: 'hux' }
              ],
              readinessProbe: {
                failureThreshold: 3,
                httpGet: {
                  path: "/",
                  port: 5000,
                  scheme: "HTTP"
                },
                initialDelaySeconds: 5,
                periodSeconds: 10,
                successThreshold: 1,
                timeoutSeconds: 3
              },
              env: [
                {
                  name: 'NAMESPACE',
                  value: namespace
                },
                {
                  name: 'CS_ENVIRONMENT_NAME',
                  value: process.env.CS_ENVIRONMENT_NAME || 'staging'
                },
                {
                  name: 'CS_CUSTOMER_NAME',
                  value: process.env.CS_CUSTOMER_NAME || 'cs'
                },
                {
                  name: 'CS_MENU_ENVIRONMENT',
                  value: 'previewer'
                },
                {
                  name: 'CS_CONFIG_PEERS',
                  value: process.env.CS_CONFIG_PEERS || 'http://internal.coalescelab.com:2379'
                },
                {
                  name: 'ELASTIC_URL',
                  value: process.env.CS_ELASTIC_URL || 'http://elasticsearch.internal.coalescelab.com:9200'
                },
                {
                  name: 'ELASTIC_USERNAME',
                  value: process.env.CS_ELASTIC_USERNAME || 'elastic'
                },
                {
                  name: 'ELASTIC_PASSWORD',
                  value: process.env.CS_ELASTIC_PASSWORD || 'changeme'
                },
                {
                  name: 'STAGING_NAMESPACE',
                  value: process.env.CS_STAGING_NAMESPACE || 'cs-menu-staging-staging'
                },
                {
                  name: 'PROD_NAMESPACE',
                  value: process.env.CS_PROD_NAMESPACE || 'cs-menu-staging-prod'
                }
              ],
              envFrom: [
                { configMapRef: { name: "language-map" } }
              ]
            }
          ]
        }
      }
    }
  };
}

//const image = 'docker pull ghcr.io/concurrent-systems/coalesce-menuserver';
//const image = 'ghcr.io/concurrent-systems/coalesce-menuserver';
const image = 'us-east1-docker.pkg.dev/coalesce-studio/concurrent-test/coalesce/coalesce-menuserver';
//const version = 'build-14';
const version = 'latest';
let json = createDeployRequest('coalesce-menu-server', image, version, false);
console.log(JSON.stringify(json, null, 2));