

# USSD Menu Service Setup Guide

This document provides instructions on how to set up and use the USSD Menu Services.

---

## Running the Services

To run all the services, execute the following command:

```bash
docker compose up --build -d
```

### First-Time Setup

If this is your first time running the `docker compose build` command, follow these steps:

1. Wait for **20 seconds** after all services are running successfully.
2. Stop all services.
3. Run the `docker compose up -d` command.

This restart is required during the first run to ensure proper creation of database tables and ETCD configurations.

---

## Accessing the GUI

Once all services are running successfully, the GUI can be accessed at:

- **URL**: [http://localhost:5000](http://localhost:5000)
- **Credentials**:
  - **Email**: `<EMAIL>`
  - **Password**: `er1c$$on`

The GUI allows you to launch menus for **Preproduction** and **Production** environments.

---

## Sending USSD Menu Requests
Once you launched the module and assign the USSD code to it, then you can send the requests to the menu server.

### Preproduction Menu Server

- **URL**: [http://localhost:5007/RPC2](http://localhost:5007/RPC2)

### Production Menu Server

- **URL**: [http://localhost:5003/RPC2](http://localhost:5003/RPC2)

### Request Format

To send a USSD menu request, use an **HTTP POST** request with an XML body.

---

## Sample XML Request

```xml
<?xml version="1.0" encoding="utf-8"?>
<methodCall>
  <methodName>handleUSSDRequest</methodName>
  <params>
    <param>
      <value>
        <struct>
          <member>
            <name>TransactionId</name>
            <value><string>1398590536</string></value>
          </member>
          <member>
            <name>TransactionTime</name>
            <value><dateTime.iso8601>20250110T13:10:19</dateTime.iso8601></value>
          </member>
          <member>
            <name>MSISDN</name>
            <value><string>22899396764</string></value>
          </member>
          <member>
            <name>USSDServiceCode</name>
            <value><string>123</string></value>
          </member>
          <member>
            <name>USSDRequestString</name>
            <value><string>1</string></value>
          </member>
        </struct>
      </value>
    </param>
  </params>
</methodCall>
```

- **MSISDN**: The MSIDN number
- **USSDServiceCode**: The USSD code to which the request is sent.
- **USSDRequestString**: The user input command.

---
