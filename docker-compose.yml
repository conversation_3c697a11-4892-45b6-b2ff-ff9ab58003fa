services:
  mariadb:
    # MariaDB database for the GUI and user management service
    image: mariadb:10.5
    container_name: coalesce-mariadb
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MY<PERSON><PERSON>_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    #TODO: Remove ports maping in production
    ports:
      - "3306:3306" # Expose MariaDB to the host machine: @todo, remove this line in production
    volumes:
      - ${DB_DATA_PATH}:/var/lib/mysql
      - ./dockerData/init:/docker-entrypoint-initdb.d
    networks:
      - coalesce-network

  redis:
    image: redis:6.2-alpine
    container_name: coalesce-redis
    restart: always
    environment:
      ALLOW_EMPTY_PASSWORD: yes
    #TODO: Remove ports maping in production
    ports:
      - "6381:6379"
    volumes:
      - ${REDIS_DATA_PATH}:/data
      - ./dockerData/conf/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: [ "redis-server", "/usr/local/etc/redis/redis.conf" ]
    networks:
      - coalesce-network

  gui:
    # Both front end and backend
    build:
      context: ./coalesce-gui # Path to the root of coalesce-gui
      dockerfile: Dockerfile # Dockerfile to use
    container_name: coalesce-gui
    restart: always
    environment:
      USER_MANG_GRPC_KEY: ${USER_MANG_GPRC_KEY} # User management: gRPC server key
      USER_MANG_GRPC_HOST: ${USER_MANG_GRPC_HOST} # User management: Hostname of the user management service
      USER_MANG_GRPC_PORT: ${USER_MANG_GRPC_PORT} # User management: gRPC server port
      DATABASE_HOST: ${MYSQL_HOST}
      DATABASE_USER: ${MYSQL_USER}
      DATABASE_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      DATABASE_NAME: ${MYSQL_DATABASE}
      NODE_ENV: development
      CS_CONFIG_PEERS: "http://${ETCD_HOST}:2379" # ETCD config urls, can be comma separated if using cluster
      CS_CUSTOMER_NAME: ${CUSTOMER_NAME} # Customer name for Etcd key prefix etc
      CS_ENVIRONMENT_NAME: ${ENVIRONMENT_NAME} # Envoirnment name for Etcd key prefix etc
      MENU_SERVER_HOST: ${MENU_PREVIEWER_SERVER_HOST} # Menu Previewer server hostname
      MENU_SERVER_PORT: ${MENU_PREVIEWER_SERVER_GRPC_PORT} # Menu Previewer server GPRC port
      REDIS_HOST: ${REDIS_HOST} # Redis hostname
      REDIS_PORT: 6379 # Redis port
      LOG_INSTANCE_NAME: ${LOG_INSTANCE_NAME}
    ports:
      - "5000:5000" # Expose GUI app on port 5000 it will be the front end and the backend
      - "8081:8081" # Expose GUI Front end development on port 8081, if we run the npm run client command
    depends_on:
      - mariadb
      - user_management
      - etcd
      - menu-server-previewer
    networks:
      - coalesce-network
    volumes:
      - ./coalesce-gui:/app # Map the source code for live updates
      - /app/node_modules # Avoid overwriting node_modules folder in the container
      - ${LOG_LEVEL_FILE_HOST_PATH}:/log_level # Map the log_level
    command: ["npm", "run", "start"] # Production command
    #command: [ "sleep", "infinity" ] # Keep the container running, @todo: Comment this line to run the GUI in development mode

  user_management:
    # User management service for gui
    build:
      context: ./user-management # Path to the root of user-management
      dockerfile: Dockerfile # Dockerfile to use
    container_name: coalesce-user-management
    restart: always
    environment:
      NODE_ENV: development
      GRPC_PORT: ${USER_MANG_GRPC_PORT} # gRPC server port
      PORT: ${USER_MANG_SERVER_PORT} # Nodejs express server port
      GRPC_KEY: ${USER_MANG_GPRC_KEY} # gRPC server key
      GRPC_REMEMBER_KEY: ${USER_MANG_GRPC_REMEMBER_KEY} # GPRC Key for remember me cookie
      DB_URL: "mysql://${MYSQL_USER}:${MYSQL_PASSWORD}@${MYSQL_HOST}:3306/users" # Database URL with credentials
      LOG_INSTANCE_NAME: ${LOG_INSTANCE_NAME}
    ports:
      - "${USER_MANG_GRPC_PORT}:${USER_MANG_GRPC_PORT}" # Expose gRPC server port.
    depends_on:
      - mariadb
      - etcd
    networks:
      - coalesce-network
    volumes:
      - ./user-management:/app # Map the source code for live updates
      - /app/node_modules # Avoid overwriting node_modules folder in the container.
      - ${LOG_LEVEL_FILE_HOST_PATH}:/log_level # Map the log_level
    command: [ "npm", "run", "start" ] # Production command
    #command: ["sleep", "infinity"] # Development Command: Keep the container running, then start the app manually by running 'npm run start' inside the container

  menu-server:
    # Production Menu server, to serve the production launched menu
    build:
      context: ./coalesce-menu-server # Path to the root of coalesce-menu-server
      dockerfile: Dockerfile # Dockerfile to use
    container_name: coalesce-menu-server
    restart: always
    environment:
      NODE_ENV: development
      REDIS_NAMESPACE: ${REDIS_NAMESPACE} # Redis namespace
      PORT: ${MENU_SERVER_PORT} # Nodejs express server port
      DEBUGGER_PORT: 5000 # gRPC server port, any of the service or outside call to this port is calling, so we are using any random port
      CS_MENU_ENVIRONMENT: "prod" # Module name for the menu server. Possible values: previewer, preprod, prod
      CS_CUSTOMER_NAME: ${CUSTOMER_NAME} # Customer name for Etcd key prefix etc
      CS_ENVIRONMENT_NAME: ${ENVIRONMENT_NAME} # Envoirnment name for Etcd key prefix etc
      CS_CONFIG_PEERS: "http://${ETCD_HOST}:2379" # ETCD config urls, can be comma separated if using cluster
      REDIS_HOSTS: ${REDIS_HOSTS} # Json array of objects with host and port properties
      MENU_SERVER_SESSION_TTL: ${MENU_SERVER_SESSION_TTL}
      LOG_INSTANCE_NAME: ${LOG_INSTANCE_NAME}
      LANG_MAP: ${LANG_MAP}
    ports:
      - "${MENU_SERVER_PORT}:${MENU_SERVER_PORT}" # Nodejs express server port for handling the ussd xml requests as post requests
    depends_on:
      - mariadb
      - etcd
      - gui # We need to wait for the GUI to start which will create the database tables if they don't exist
    networks:
      - coalesce-network
      - redis-cluster-network
    volumes:
      - ./coalesce-menu-server:/app # Map the source code for live updates
      - /app/node_modules # Avoid overwriting node_modules folder in the container
      - ${LOG_LEVEL_FILE_HOST_PATH}:/log_level # Map the log_level
    command: ["npm", "run", "start"] # Production command
    #command: [ "sleep", "infinity" ] # Development Command: Keep the container running, then start the app manually by running 'npm run start' inside the container

  menu-server-preprod:
    # Preprod Menu server, to serve the preprod launched menu
    build:
      context: ./coalesce-menu-server # Path to the root of coalesce-menu-server
      dockerfile: Dockerfile # Dockerfile to use
    container_name: coalesce-menu-server-preprod
    restart: always
    environment:
      NODE_ENV: development
      REDIS_NAMESPACE: ${REDIS_NAMESPACE} # Redis namespace
      PORT: ${MENU_PREPROD_SERVER_PORT} # Nodejs express server port
      DEBUGGER_PORT: 5000 # gRPC server port, any of the service or outside call to this port is calling, so we are using any random port
      CS_MENU_ENVIRONMENT: "preprod" # Module name for the menu server. Possible values: previewer, preprod, prod
      CS_CUSTOMER_NAME: ${CUSTOMER_NAME} # Customer name for Etcd key prefix etc
      CS_ENVIRONMENT_NAME: ${ENVIRONMENT_NAME} # Envoirnment name for Etcd key prefix etc
      CS_CONFIG_PEERS: "http://${ETCD_HOST}:2379" # ETCD config urls, can be comma separated if using cluster
      REDIS_HOSTS: ${REDIS_HOSTS} # Json array of objects with host and port properties
      MENU_SERVER_SESSION_TTL: ${MENU_PREPROD_SERVER_SESSION_TTL}
      LOG_INSTANCE_NAME: ${LOG_INSTANCE_NAME}
    ports:
      - "${MENU_PREPROD_SERVER_PORT}:${MENU_PREPROD_SERVER_PORT}" # Nodejs express server port for handling the ussd xml requests as post requests
    depends_on:
      - mariadb
      - etcd
      - gui # We need to wait for the GUI to start which will create the database tables if they don't exist
    networks:
      - coalesce-network
      - redis-cluster-network
    volumes:
      - ./coalesce-menu-server:/app # Map the source code for live updates
      - /app/node_modules # Avoid overwriting node_modules folder in the container
      - ${LOG_LEVEL_FILE_HOST_PATH}:/log_level # Map the log_level
    command: ["npm", "run", "start"] # Production command
    #command: [ "sleep", "infinity" ] # Development Command: Keep the container running, then start the app manually by running 'npm run start' inside the container

  menu-server-previewer:
    # Menu previewer server, used for the gui module previewer
    build:
      context: ./coalesce-menu-server # Path to the root of coalesce-menu-server
      dockerfile: Dockerfile # Dockerfile to use
    container_name: coalesce-menu-server-previewer
    restart: always
    environment:
      NODE_ENV: development
      REDIS_NAMESPACE: ${REDIS_NAMESPACE} # Redis namespace
      PORT: 4000 # Nodejs express server port, any of the service or outside call to this port is calling, so we are using any random port
      DEBUGGER_PORT: ${MENU_PREVIEWER_SERVER_GRPC_PORT} # gRPC server port
      CS_MENU_ENVIRONMENT: "previewer" # Module name for the menu server. Possible values: previewer, preprod, prod
      CS_CUSTOMER_NAME: ${CUSTOMER_NAME} # Customer name for Etcd key prefix etc
      CS_ENVIRONMENT_NAME: ${ENVIRONMENT_NAME} # Envoirnment name for Etcd key prefix etc
      CS_CONFIG_PEERS: "http://${ETCD_HOST}:2379" # ETCD config urls, can be comma separated if using cluster
      REDIS_HOSTS: ${REDIS_HOSTS} # Json array of objects with host and port properties
      LOG_INSTANCE_NAME: ${LOG_INSTANCE_NAME}
    depends_on:
      - mariadb
      - etcd
    networks:
      - coalesce-network
      - redis-cluster-network
    volumes:
      - ./coalesce-menu-server:/app # Map the source code for live updates
      - /app/node_modules # Avoid overwriting node_modules folder in the container
      - ${LOG_LEVEL_FILE_HOST_PATH}:/log_level # Map the log_level
    command: ["npm", "run", "start"] # Production command
    #command: [ "sleep", "infinity" ] # Development Command: Keep the container running, then start the app manually by running 'npm run start' inside the container

  etcd:
    # ETCD key-value store
    image: bitnami/etcd:3.5.16
    container_name: coalesce-etcd
    environment:
      ALLOW_NONE_AUTHENTICATION: yes
      ETCD_ADVERTISE_CLIENT_URLS: "http://0.0.0.0:2379"
      ETCD_LISTEN_CLIENT_URLS: "http://0.0.0.0:2379"
    ports:
      #TODO: Remove these ports in production
      - "2379:2379" # Expose ETCD API
      - "2380:2380" # Expose ETCD peer communication
    volumes:
      - ${ETCD_DATA_PATH}:/bitnami/etcd
    networks:
      - coalesce-network
      


volumes:
  mariadb_data:
  redis-data: # Added persistent volume for Redis
  etcd_data:
    # Add persistent volume for ETCD
    driver: local

networks:
  coalesce-network:
    driver: bridge

  redis-cluster-network:  # shared external network with Redis cluster project
    external: true
    name: redis-cluster-network