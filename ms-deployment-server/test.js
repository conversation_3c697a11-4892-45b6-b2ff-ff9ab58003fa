const request = require('request')
const fs = require('fs')

const url = 'https://127.0.0.1:16443/apis/build.knative.dev/v1alpha1/namespaces/default/builds/knative-build-and-deploy-1cb6b080-c7df-11e9-b467-9b00e5476692';

const token = '**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

function sendRequest(args) {
	return new Promise((resolve, reject) => {
		request.get(args, (error, response, data) => {
			if (error)
				reject(error);
			else {
				//console.log('DATA: ' + data);
				resolve(data);
			}
		});
	})
	.catch((err) => {
		console.error(err)
		return Promise.reject(new Error(err.message))
	});
}

async function test() {
	try {
		const r = await sendRequest({ url, headers: { 'Authorization': `Bearer ${token}` }, insecure: true, rejectUnauthorized: false });
		console.log(r);
	}
	catch(err) {
		console.error(err)
	}
}

test();
