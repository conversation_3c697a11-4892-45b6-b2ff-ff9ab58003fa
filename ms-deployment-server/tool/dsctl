#!/usr/bin/env node

const fs = require('fs');

const protoPath = __dirname + '/../proto/deployment.proto'
const grpc = require('grpc')
const protoLoader = require('@grpc/proto-loader')

const port = process.env.PORT || 5000
const packageDefinition = protoLoader.loadSync(
	protoPath,
	{ keepCase: true, longs: Number, enums: String, defaults: false, oneofs: true })

const protoDescriptor = grpc.loadPackageDefinition(packageDefinition)

const yargs = require('yargs')
	.usage('Usage: $0 <command> [options]')

	.command(['route <add|delete|update|list|replace>', 'r'], 'Manipulate routing table', function(yargs) {
		yargs.usage('usage: $0 route <item> [options]')
		.command(['add', 'a'], 'add new routings')
		.command(['delete', 'd'], 'delete routings')
		.command(['update', 'u'], 'update routings')
		.command(['list', 'l'], 'list routings')
		.command(['replace', 'r'], 'replace a routing aasigned to service with another one')
		.option('s', {  alias: 'service', demand: true, describe: 'Service name', type: 'string' })
		.option('c', {  alias: 'code', demand: false, describe: 'Ussd code', type: 'string' })
	})
	.command(['langs <get|update>', 'l'], 'list/update languages', function(yargs) {
		yargs.usage('usage: $0 langs get')
		.command('get', 'get languages')
		.command('update', 'update languages', function(yargs) {
			yargs.usage('usage: $0 langs update fra,eng');
			//console.log(yargs.argv);
			if (yargs.argv._.length < 3) {
				console.error(`usage: ${yargs.argv['$0']} langs update fra,eng`);
			 	process.exit(0);
			}
		})
	})
	.command(['service <status|create|delete>', 'svc', 's'], 'Manipulate services', function(yargs) {
		yargs.usage('usage: $0 service status [name]')
		.command(['delete', 'd'], '[NAME]', function(yargs) {
			if (yargs.argv._.length < 3) {
				console.error(`usage: ${yargs.argv['$0']} service delete [NAME]`);
			 	process.exit(0);
			}
		})
		.command(['status', 's'], '[NAME]')
		.command(['create', 'c'], '[NAME] [ARGS]', function(yargs) {

			// yargs
			// 	.option('m', {  alias: 'menu', demand: true, describe: 'Menu config JSON', type: 'string' })
			// 	.option('u', {  alias: 'code', demand: false, describe: 'Ussd code', type: 'string' })
			// 	.option('v', {  alias: 'version', demand: false, describe: 'Version', type: 'string', default: 'v1.0' })
			// 	;

			// if (yargs.argv._.length < 3) {
			// 	console.error(`usage: ${yargs.argv['$0']} service create [NAME] [ARGS]`);
			// 	yargs.showHelp();
			//  	process.exit(0);
			// }
		})
	})
	.option( 'h', { alias: 'host', demand: false, describe: 'Host address', type: 'string', default: '127.0.0.1' } )
	.option( 'p', { alias: 'port', demand: false, describe: 'Host port', type: 'number', default: port } )
	.help('help')
	.example('$0 route add -s test -c *100*1# -h *************** -p 5000')
	.example('$0 langs get')
	.wrap(null)
	.version('1.0.0')
	.epilog('\251 2020. Concurrent Systems')
	;

const usage = yargs.showHelp;

const argv = yargs.argv;
//console.log(argv);

const client = new protoDescriptor.Deployment(argv.host + ':' + argv.port, grpc.credentials.createInsecure());

if (argv._.length == 0) {
	usage();
	return;
}

// if (argv._.length > 1 || commands.indexOf(argv._[0]) === -1) {
// 	console.error('ERROR: Invalid command\n');
// 	usage();
// 	return;
// }

//console.debug(argv);

const cmd = argv._[0];

switch (cmd) {

	case 'route': 
	case 'rt':
	case 'r':
	{
		const subcmd = argv._[1];

		switch (subcmd) {

			case 'add': 
			case 'a': 
			{
				if (!argv.c) {
					console.error('Short code is required with \'add\' command');
					usage();
					break;
				}
		
				const codes = Array.isArray(argv.c) ? argv.c : [argv.c];
				console.debug('Adding short codes:', codes);
		
				client.updateCodes({ service: argv.s, add: codes }, (err, r) => {
					if (err) {
						console.log('Encountered error:', err);
						return;
					}
		
					if (r.error) {
						console.log(r.error);
						return;
					}
		
					if (r.service.codes)
						console.log(r.service.name, ':', r.service.codes.join(','));
				});
			}
			break;
		
			case 'delete': 
			case 'd': 
			{
				if (!argv.c) {
					console.error('Short code is required with \'delete\' command');
					usage();
					break;
				}
		
				const codes = Array.isArray(argv.c) ? argv.c : [argv.c];
				console.debug('Adding short codes:', codes);
		
				client.updateCodes({ service: argv.s, remove: codes }, (err, r) => {
					if (err) {
						console.log('Encountered error:', err);
						return;
					}
		
					if (r.error) {
						console.log(r.error);
						return;
					}
		
		
					if (r.service.codes)
						console.log(r.service.name, ':', r.service.codes.join(','));
				});
			}
			break;
		
			case 'update': 
			case 'u': 
			{
				if (!argv.c) {
					console.error('Short code is required with \'update\' command');
					usage();
					break;
				}
		
				const codes = Array.isArray(argv.c) ? argv.c : [argv.c];
				console.debug('Adding short codes:', codes);
		
				client.updateCodes({ service: argv.s, update: codes }, (err, r) => {
					if (err) {
						console.log('Encountered error:', err);
						return;
					}
		
					if (r.error) {
						console.log(r.error);
						return;
					}
		
					if (r.service.codes)
						console.log(r.service.name, ':', r.service.codes.join(','));
				});
			}
			break;
		
			case 'replace': 
			case 'r': 
			{
				if (!argv.c) {
					console.error('Short code is required with \'replace\' command');
					usage();
					break;
				}
		
				let codes = Array.isArray(argv.c) ? argv.c : [argv.c];
				codes = codes.map(i => { const t = i.split(':'); return { code: t[0], newcode: t[1]}; });
		
				client.updateCodes({ service: argv.s, replace: codes }, (err, r) => {
					if (err) {
						console.log('Encountered error:', err);
						return;
					}
		
					if (r.error) {
						console.log(r.error);
						return;
					}
		
					if (r.service.codes)
						console.log(r.service.name, ':', r.service.codes.join(','));
				});
			}
			break;
		
			case 'list':
			case 'l':
			{
				
				const name = argv.s;
				if (name == 'all') {
					client.listServiceStatus({}, (err, r) => {
						if (err) {
							console.log('Encountered error:', err);
							return;
						}
		
						if (!r.services) {
							console.log('No services');
							return;
						}

						for (const svc of r.services) {
							const s = svc.service;

							if (s.codes)
								console.log(s.name, ':', s.codes.join(','));
						}
					});
				} else  {
		
					client.serviceStatus({ name: argv.s }, (err, r) => {
						if (err) {
							console.log('Encountered error:', err);
							return;
						}
		
						const s = r.service;
						console.log(s.name, ':', s.codes ? s.codes.join(',') : '');
					});
				}
			}
			break;

		}

	}
	break;

	case 'langs': 
	case 'l':
	{

		const subcmd = argv._[1];
		//console.log(subcmd);

		switch (subcmd) {

			case 'get':
			//console.log('Getting languages');

			client.getLanguages({}, (err, r) => {
				if (err) {
					console.log('Encountered error:', err);
					return;
				}

				console.log(r.langs);
			});

			break;

			case 'update': 
			case 'u':
			//console.log('Updating languages');

			const langs = argv._[2].split(',');
			client.updateLanguages({ langs }, (err, r) => {
				if (err) {
					console.log('Encountered error:', err);
					return;
				}

				if (r.error) 
					console.error(r.error);
			});

			break;
		}
	}
	break;

	case 'service': 
	case 'svc': 
	case 's':
	{
		const subcmd = argv._[1];

		switch (subcmd) {

			case 'create':
			case 'c':

			console.debug('Sending build and deploy request');

			// const menu = fs.readFileSync(argv.menu).toString();
			// console.log(menu);

			client.buildAndDeploy({
				name: 'all-network-bundles'
				, prod: true
				, source: { 
					url: 'http://administrator:<EMAIL>:5005/administrator/all-network-bundle.git',
					revision: '264',
					sha: '136aa6'
				},
				image: {
					name: 'all-network-bundle',
 					tag: 'latest',
 					always_pull: true
				},
				version: '2.0'
			}, (err, r) => {
				if (err) {
					console.log('Encountered error:', err);
					return;
				}

				if (r.error) {
					console.error(r.error);
					return;
				}

				console.log('Service built successfully');
			});

			break;

			case 'delete':
			case 'd':
			{

				const name = argv._[2];

				//console.log('Deleting service', name);
			
				client.deleteService({ name }, (err, r) => {
					if (err) {
						console.log('Encountered error:', err);
						return;
					}

					if (r.error) {
						console.error(r.error);
						return;
					}

					console.log('Service deleted successfully');
				});
			}
			break;

			case 'status':
			case 's': {

				const name = argv._[2];
				if (name) {
					client.serviceStatus({ name }, (err, r) => {
						if (err) {
							console.log('Encountered error:', err);
							return;
						}

						if (r.error) {
							console.log(r.error);
							return;
						}
		
						console.log('\State:', r.state);
						console.log('\Created:', new Date(r.created).toISOString());
						console.log('\Production:', r.prod);
						console.log('\Codes:', r.codes.join(','));
					});
				}
				else {
					client.listServiceStatus({}, (err, r) => {
						if (err) {
							console.log('Encountered error:', err);
							return;
						}
		
						if (!r.services) {
							console.log('No services');
							return;
						}

						for (const svc of r.services) {

							const s = svc.service;

							console.log('SERVICE:', s.name);
							console.log('\tState:', s.state);
							console.log('\tCreated:', new Date(s.created).toISOString());
							console.log('\tProduction:', s.prod);
							console.log('\tCodes:', s.codes ? s.codes.join(',') : '');
						}
					});
				}
			}
			break;

		}
	}
	break;

	default:
	console.error('Invalid command:', cmd);
	usage();
	break;

}
