syntax = "proto3";

service Deployment {
	rpc buildAndDeploy    (BuildRequest)           returns (Response);
	rpc buildStatus       (BuildStatusRequest)     returns (Response);
	rpc serviceStatus     (ServiceStatusRequest)   returns (Response);
	rpc listServiceStatus (NoParams)               returns (ListResponse);
	rpc updateCodes       (UpdateCodesRequest)     returns (Response);
	rpc deleteService     (ServiceInfo)            returns (Response);
	rpc getLanguages      (GetLanguagesRequest)    returns (LanguagesList);
	rpc updateLanguages   (UpdateLanguagesRequest) returns (Response);
}

message NoParams {}

message ListResponse {
	repeated Response services = 1;
}

message GitRepository {
	string url = 1;
	string revision = 2;
	string sha = 3;
}

message ImageInformation {
	string name = 1;
	string tag = 2;
	bool always_pull = 3;
}

enum BuildState {
	Canceled = 1;
	Pending = 2;
	Building = 3;
	BuildFailed = 4;
	Deploying = 5;
	DeployFailed = 6;
	CodeExists = 7;
	Succeeded = 8;
}

message BuildInfo {
	BuildState state = 1;
	string id = 2; // sha of source
	int64 created = 3;
}

enum ServiceState {
	Running = 1;
	NotRunning = 2;
	Deploying = 3;
	DeployFailed = 4;

	NotExists = 10;
}

message ServiceInfo {
	string name = 1;
	ServiceState state = 2;
	int64 created = 3;
	bool prod = 4;
	repeated string codes = 5;
	string version = 6;
}

message BuildRequest {
	string name = 1;
	bool prod = 2;
	repeated string codes = 3;
	GitRepository source = 4;
    ImageInformation image = 5;
    string version = 6;
}

message BuildStatusRequest {
	string name = 1;
	bool prod = 2;
	string id = 3; // sha of source
}

message ServiceStatusRequest {
	string name = 1;
	bool prod = 2;
}

message ReplacePair {
	string code = 1;
	string newcode = 2;
}

message UpdateCodesRequest {
	string service = 1;
	bool prod = 2;
	repeated string add = 3;
	repeated string remove = 4;
	repeated string update = 5;
	repeated ReplacePair replace = 6;
}

message GetLanguagesRequest {
	bool prod = 1;
}

message LanguagesList {
	repeated string langs = 1;
}

message UpdateLanguagesRequest {
	bool prod = 1;
	repeated string langs = 2;
}

// Responses
message Response {
	string error = 1;
	ServiceInfo service = 2;
	BuildInfo build = 3;
}
