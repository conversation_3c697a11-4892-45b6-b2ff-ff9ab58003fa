const k8s = require('@kubernetes/client-node')
const request = require('request')
const fs = require('fs')

const buildNamespace = process.env.BUILD_NAMESPACE || 'cs-menu-dev-build';
const stagingNamespace = process.env.STAGING_NAMESPACE || 'cs-menu-dev-staging';
const prodNamespace = process.env.PROD_NAMESPACE || 'cs-menu-dev-prod';
const namespaces = [stagingNamespace, prodNamespace];
const template = process.env.TEMPLATE || 'kaniko';
const nodeEnv = process.env.NODE_ENV || 'development';
//const registry = process.env.REGISTRY || 'localhost:32000';
const registry = process.env.REGISTRY || 'docker.internal.coalescelab.com:443';
const serviceAccountName = process.env.SERVICE_ACCOUNT_NAME || 'ms-deployment';
const gatewayName = process.env.GATEWAY_NAME || 'ussd-gateway';
const virtualServiceName = process.env.VIRTUAL_SERVICE_NAME || 'cs-menu-dev-route';
const serviceStatusPollInterval = process.env.SERVICE_STATUS_POLL_INTERVAL || 60;
const kRetryTimeout = process.env.RETRY_TIMEOUT || 10; // 10s seconds
const kLifecycleInterval = 2 * 60*60 * 1000; // 2 hours
const kFailedBuildTimeout = process.env.FAILED_BUILD_TIMEOUT || 24 * 60*60 * 1000; // 1 day

if (!serviceAccountName) {
	console.error('Missing SERVICE_ACCOUNT_NAME environment variable, cannot continue');
	process.exit(-1);
}

const kc = new k8s.KubeConfig();
var tf = null;
if (nodeEnv == 'production') {
	tf = fs.readFileSync('/var/run/secrets/kubernetes.io/serviceaccount/token');
	console.log('Found token file: ', tf);
	kc.loadFromCluster();
} else {
	console.warn('No token file found, loading from default');
	kc.loadFromDefault();
}

const server = kc.getCurrentCluster().server;

const TASK_URL = `${server}/apis/tekton.dev/v1beta1/namespaces/${buildNamespace}/taskruns`;

const DEPLOY_URL = [
	`${server}/apis/apps/v1/namespaces/${stagingNamespace}/deployments`,
	`${server}/apis/apps/v1/namespaces/${prodNamespace}/deployments`
];

const SERVICE_URL = [
	`${server}/api/v1/namespaces/${stagingNamespace}/services`,
	`${server}/api/v1/namespaces/${prodNamespace}/services`
];

const STAGING_POD_URL = `${server}/api/v1/namespaces/${stagingNamespace}/pods`;
const PROD_POD_URL = `${server}/api/v1/namespaces/${prodNamespace}/pods`;
//const RS_URL = apis/extensions/v1beta1/namespaces/default/replicasets/
const VIRTUAL_SERVICE_URL = `${server}/apis/networking.istio.io/v1alpha3/namespaces/default/virtualservices`;

const CONFIGMAP_URL = [
	`${server}/api/v1/namespaces/${stagingNamespace}/configmaps`,
	 `${server}/api/v1/namespaces/${prodNamespace}/configmaps`
];

const BUILD_TIMEOUT = 2 * 60 * 1000; // 2 min
const BUILD_STATUS_UPDATE_INTERVAL = 5 * 1000; // 5 sec

const DEPLOYMENT_TIMEOUT = 2 * 60 * 1000; // 2 min

// note: give a chance old pod to be terminated
const POD_STATUS_UPDATE_INITIAL_INTERVAL = 10 * 1000; // 5 sec
const POD_STATUS_UPDATE_INTERVAL = 5 * 1000; // 5 sec

const kDefaultLanguages = ['eng', 'fra'],
	kLanguageMapName = 'language-map'
	;

const routeMap = new Map;
let failedBuilds = [];

class Service {
	constructor(name, prod) {
		this.name = name;
		this.prod = prod;
		this.label = name;
		this.build = { current: null, running: null, pending: null };
		this.deploy = {};
				//, state: 'Running'
		this.pods = [];
		this.requests = new Map;
		this.state = 'NotRunning';
		this.image = '';
		this.created = Date.now();
		this.source = { url: '', revision: '', sha: '' };
		this.routes = [];
		this.newRoutes = [];
	}

	get fullName() { return `${this.name}.${this.prod ? prodNamespace : stagingNamespace}.svc.cluster.local`; }

	update(req) {
		const id = req.source.sha;
		const now = Date.now();
		const service = this.info;

		if (req.codes) {
			const codes = [];
			for (const i of req.codes) {
				const c = normalizeCode(i);
				const svc = routeMap.get(c);

				if (!svc) {
					codes.push(c);
					continue;
				}

				// check whether it is already added to same service
				if (svc == this)
					continue;

				return {
					service
					, build: {
						id: req.source.sha
						, state: 'CodeExists'
					}
					, error: `Short code '${c}' already mapped to '${svc.name}'`
				}
			}

			// register the new short codes for that service
			console.debug('Adding new routes:', codes);
			this.newRoutes = codes;
			codes.map(c => routeMap.set(c, this));
		}

		if (this.build.current) {
			const build = this.build.current;
			if (build.id == id) {
				return { service, build: {
					id: build.request.source.sha
					, created: build.request.created
					, state: build.request.state
				}};
			}

			if (this.build.pending) {
				const build = this.build.pending;

				if (build.id == id) {
					return { service, build: {
						id: build.request.source.sha
						, created: build.request.created
						, state: build.request.state
					}};
				}
				
				build.request.state = 'Canceled';
				build.request.ts = now;
			}

			const r  = {
				created: now
				, source: req.source
				, image: req.image
				, state: 'Pending'
				, version: req.version
			};

			this.requests.set(id, r);
			this.build.pending = {
				name: `${this.name}-${id}-${getDate()}`
				, id
				, request: r
			};
			console.debug('build.pending::Configuring '+JSON.stringify(this.build.pending))

			return { service, build: { id, created: now, state: 'Pending' }};
		}

		// no current builds
		console.debug('Service source:', this.source);
		if (id == this.source.sha) {
			console.debug('Ingoring build request because is same version as service');
			return { service, build: {
				id: this.source.sha
				, created: this.build.running.request.created
				, state: this.build.running.request.state
			}};
		}

		const r  = {
			created: now
			, source: req.source
			, image: req.image
			, state: 'Building'
			, version: req.version
		};

		this.requests.set(id, r);
		this.build.current = {
			name: `${this.name}-${id}-${getDate()}`,
			id,
			request: r
		};
		console.debug('build.current::Configuring '+JSON.stringify(this.build.current))

		this.buildImage();
		
		return { service, build: {
			id: req.source.sha
			, created: now
			, state: 'Building'
		}};
	}

	buildStatus(id) {
		console.debug('Build state request:', this.name, id);
		const req = this.requests.get(id);

		if (!req) return null; 

		return { service: this.info, 
			build: {
				id
				, created: req.created
				, state: req.state
			}
		};
	}

	get info() {
		//console.debug(this);
		return {
			name: this.name
			, state: this.state
			, created: this.created
			, codes: this.routes
			, version: this.currentVersion
			, prod: this.prod
		};
	}

	get buildInfo() {
		if (this.build.running) {
			const r = this.build.running.request;
			return {
				state: r.state
				, id: r.source.sha
				, created: r.created
			}
		}

		return {
			state: 'unknown'
			, id: 'unknown'
			, created: Date.now()
		};
	}

	get selfLink() {
		return `${DEPLOY_URL[~~this.prod]}/${this.name}`;
	}

	async buildImage() {
		const build = this.build.current;
		const name = build.name;
		let response = null;
		try {
			const k8sreq = createTaskRequest({ name, source: build.request.source, image: build.request.image });
			const yaml = k8s.dumpYaml(k8sreq);
			console.log('K8S BUILD REQUEST:\n', `$ kubectl apply -f <<EOF\n${yaml}\nEOF`);
			
			await postRequest({ url: TASK_URL, body: k8sreq });

			build.request.state = 'Building';
			build.request.started = Date.now();

			const cb = async () => {
				try {
					const s = await getTaskStatus(name);

					if (s === 'True')
						this.onBuildSuccess();
					else if (s === 'False') 
						this.onBuildFailed();
					else {
						if (Date.now() - build.request.started < BUILD_TIMEOUT)
							setTimeout(cb, BUILD_STATUS_UPDATE_INTERVAL);
						else
							this.onBuildFailed();
					}
				}
				catch(err) {
					console.error('buildStatus::ERROR')
					console.error(err);
				}
			};

			setTimeout(cb, BUILD_STATUS_UPDATE_INTERVAL);
		}
		catch(err) {
			console.error('buildImage::ERROR')
			console.error(err);
		}
	}

	onBuildSuccess() {
		console.log('BUILD successfull');

		const build = this.build.current;
		build.request.state = 'BuildSuccess';
		build.request.ts = Date.now();

		deleteTask(build.name);
		if (this.build.pending) {
			this.build.current = this.build.pending;
			this.build.pending = null;
			this.buildImage();
		}
		else {
			this.deployImage();
		}
	}

	onBuildFailed() {
		console.log('BUILD failed');

		const build = this.build.current;
		build.request.state = 'BuildFailed';
		build.request.ts = Date.now();

		failedBuilds.push({ name: build.name, ts: build.request.ts });

		if (this.build.pending) {
			this.build.current = this.build.pending;
			this.build.pending = null;
			this.buildImage();
			return;
		}

		//console.debug('Last build failed and no pending requests');
		// if (this.build.last && (!this.build.running || this.build.last.id != this.build.running.id)) {
		// 	console.debug('Deploying last successfull build:', this.build.last.name);
		// 	this.deploy();
		// 	return;
		// }
	}

	updateDeployment(image) {
		console.debug('UPDATING existing deployment:', this.name, this.currentVersion);
		
		return updateRequest({ url: this.selfLink, body: [
			{ op: 'replace', path: '/spec/template/spec/containers/0/image', value: image},
			{ op: 'replace', path: '/spec/template/metadata/creationTimestamp', value: new Date().toISOString()},
			{ op: 'replace', path: '/spec/template/metadata/labels/version', value: this.currentVersion}
		] });
	}

	async deployImage() {
		console.debug('K8S UPDATE Deployment request');
		try {
			this.state = 'Deploying';
			this.deploy.started = Date.now();

			const build = this.build.current;
			build.request.state = 'Deploying';

			let exists = false;
			try { 
				//console.debug('CHECKING Deployment exists on:', url);
				await getRequest({ url: this.selfLink });
				exists = true;
			}
			catch (err) {
				exists = false;
				console.error(err)
			}

			this.currentVersion = build.request.version;
			const image = registry + '/' + build.request.image.name + ':' + build.request.image.tag;
			
			if (exists) {
				console.debug('UPDATING existing deployment:', this.name);
				await this.updateDeployment(image);
			}
			else {
				const k8sreq = createDeployRequest({ name: this.name, image, version: this.currentVersion }, this.prod);
				const yaml = k8s.dumpYaml(k8sreq);
				console.log('K8S DEPLOY REQUEST:\n', `$ kubectl apply -f <<EOF\n${yaml}\nEOF`);

				const url = DEPLOY_URL[~~this.prod];

				const r = await postRequest({ url, body: k8sreq });
				this.createService();
			}

			const cb = async () => {
				this.pods = await getPodStatus(this.label, this.prod);
				console.log('PODS:\n', this.pods);

				for (const p of this.pods) {
					if (p.state == 'Running') {
						this.onDeploySuccess();
						return;
					}

					if (p.state == 'Failed') {
						this.onDeployFailed();
						return;
					}
				}

				if (Date.now() - this.deploy.started < DEPLOYMENT_TIMEOUT) {
					setTimeout(cb, POD_STATUS_UPDATE_INTERVAL);
					return;
				}

				this.onDeployFailed();
			};

			setTimeout(cb, POD_STATUS_UPDATE_INITIAL_INTERVAL);
		}
		catch(err) {
			console.error(err)
		}
	}

	onDeploySuccess() {
		const build = this.build.current;
		build.request.state = 'Succeeded';

		this.state = 'Running';
		this.image = build.request.image;
		this.source = build.request.source;

		this.build.running = build;
		this.build.current = null;
		this.created = Date.now();

		if (this.build.pending) {
			this.build.current = this.build.pending;
			this.build.pending = null;
			this.buildImage();
		}
		else {
			this.addRoutes();
		}
	}

	onDeployFailed() {
		const build = this.build.current;
		build.request.state = 'DeployFailed';

		this.state = 'NotRunning';
		this.image = build.request.image;
		this.source = build.request.source;

		this.build.running = build;
		this.build.current = null;

		if (this.build.pending) {
			this.build.current = this.build.pending;
			this.build.pending = null;
			this.buildImage();
		}
	}

	async addRoutes() {
		if (this.newRoutes.length == 0)
			return;

		const paths = this.newRoutes.map(i => routeToPath(i));

		// no routings
		if (routeMap.size == 0) {
			const k8sreq = createVirtualServiceRequest(this.fullName, paths);
			const yaml = k8s.dumpYaml(k8sreq);
			console.log('K8S VIRTUAL SERVICE REQUEST:\n', `$ kubectl apply -f <<EOF\n${yaml}\nEOF`);

			try {
				await postRequest({ url: VIRTUAL_SERVICE_URL, body: k8sreq });

				for (const i of this.newRoutes)
					routeMap.set(i, this);

				this.routes.push(...this.newRoutes);
			}
			catch (e) {
				console.error('Failed to create VirtualService');
				// todo delete entries from routeMap
			}

		}
		else {

			const ops = paths.map(i => { return { op: 'add', path: '/spec/http/-', value: createMatchRoute(this.fullName, i) }; });

			try {

				await updateRequest({ 
					url: `${VIRTUAL_SERVICE_URL}/${virtualServiceName}`, 
					body: ops });

				for (const i of this.newRoutes)
					routeMap.set(i, this);

				this.routes.push(...this.newRoutes);
			}
			catch (e) {
				console.error('Failed to update VirtualService');
				// todo clear them from routeMap
			}
		}

		this.newRoutes.length = 0;
	}

	async removeRoutes(codes) {

		const ops = [];

		for (const c of codes) {
			let j = 0;
			for (const k of routeMap.keys()) {
				if (k == c) {
					ops.push({ op: 'remove', path: `/spec/http/${j}`});
					break;
				}

				j++;
			}

			this.routes = this.routes.filter(i => i != c);
		}

		for (const c of codes)
			routeMap.delete(c);

		try {
			if (routeMap.size == 0) {
				console.log('Deleting VirtualService:', virtualServiceName);
				
				await deleteRequest({ url: `${VIRTUAL_SERVICE_URL}/${virtualServiceName}` });
			}
			else {
				await updateRequest({ 
					url: `${VIRTUAL_SERVICE_URL}/${virtualServiceName}`, 
					body: ops });
			}

		}
		catch (e) {
			console.error('Failed to delete from VirtualService:', e);
		}
	}

	async updateRoutes(codes) {
		const ops = [];

		//console.debug(codes);
		for (const c of codes) {
			let j = 0;
			for (const [code,s] of routeMap.entries()) {
				//console.debug('\t', k);
				if (code == c) {
					ops.push({ op: 'replace', path: `/spec/http/${j}/route/0/destination/host`, value: this.fullName });

					//console.log('Updating service:', s);
					s.routes = s.routes.filter(i => i != c);
					this.routes.push(c);
					routeMap.set(c, this);

					break;
				}

				j++;
			}
		}

		if (ops.length == 0)
			return;

		try {
			console.debug(ops);
			await updateRequest({ 
				url: `${VIRTUAL_SERVICE_URL}/${virtualServiceName}`, 
				body: ops });

		}
		catch (e) {
			console.error('Failed to delete from VirtualService');
		}
	}

	async replaceRoutes(codes) {
		const ops = [];

		//console.debug(codes);
		for (const {code, newcode} of codes) {

			let j = 0;
			for (const c of routeMap.keys()) {

				if (c == code) {

					ops.push({ op: 'replace', path: `/spec/http/${j}/match/0/uri/prefix`, value: routeToPath(newcode) });
					break;
				}

				j++;
			}
		}

		if (ops.length == 0)
			return;

		try {
			console.debug(ops);
			await updateRequest({ 
				url: `${VIRTUAL_SERVICE_URL}/${virtualServiceName}`, 
				body: ops });

			for (const {code, newcode} of codes) {

				routeMap.delete(code);
				routeMap.set(newcode, this);

				this.routes.splice(this.routes.indexOf(code), 1, newcode);
			}

		}
		catch (e) {
			console.error('Failed to replace from VirtualService');
		}
	}

	async createService() {
		const CURRENT_URL = SERVICE_URL[~~this.prod];
		try {
			await getRequest({ url: `${CURRENT_URL}/${this.name}` });
		}
		catch (e) {
			const k8sreq = createServiceRequest(this.name, (this.prod?prodNamespace:stagingNamespace));
			const yaml = k8s.dumpYaml(k8sreq);
			console.log('K8S CREATE SERVICE REQUEST:\n', `$ kubectl apply -f <<EOF\n${yaml}\nEOF`);

			try {
				const r = await postRequest({ url: CURRENT_URL, body: k8sreq });
				console.debug('K8S SERVICE created:', this.name);
			}
			catch(err) {
				console.error(err)
			}
		}
	}
}

function deleteTask(name) {
	console.log('K8S DELETE TASK REQUEST:\n', `$ kubectl delete taskrun -n ${buildNamespace} ${name}`);
	deleteRequest({ url: `${TASK_URL}/${name}` });
}

async function getTaskStatus(name) {
	try {
		console.log('K8S TASK STATUS REQUEST:\n', `$ kubectl get taskrun -n ${buildNamespace} ${name}`);
		const url = TASK_URL + '/' + name;
		const res = await getRequest({ url });

		if (!res.status)
			return '';

		//console.debug(res);
		if (!res.status.conditions) throw Error("No build status found")
		const cond = res.status.conditions[0];
		console.debug(cond);
		return cond.status;
	}
	catch(err) {
		console.error(err)
		return -1
	}
}

async function getCompletedTasks() {
	try {
		console.log('K8s: GET COMPLETE TASKRUN REQUEST:\n', `$ kubectl get taskrun -n ${buildNamespace}`);

		const r = await getRequest({ url: TASK_URL });

		//	console.debug(JSON.stringify(r.items[0], null, '\t'));

		return r.items.map(i => Object({
			name: i.metadata.name
			, ts: new Date(i.metadata.creationTimestamp)
			, status: function(s) { return s == 'True' ? true : false; }(i.status.conditions[0].status)
		}));

	}
	catch (err) {
		console.error(err);
	}

	return [];
}

async function getPodStatus(label, prod) {
	try {
		const currentNamespace = this.prod?prodNamespace:stagingNamespace
		//console.log('K8S POD STATUS REQUEST:\n', `$ kubectl get pods -n ${currentNamespace} -l run=${label}`);
		const url = (prod?PROD_POD_URL:STAGING_POD_URL) + '/?labelSelector=run%3D' + label;
		const r = await getRequest({ url });

		return r.items.map(p => Object({
			name: p.metadata.name,
			link: p.metadata.selfLink,
			uid: p.metadata.uid,
			version: p.metadata.resourceVersion,
			created: p.metadata.creationTimestamp,
			state: p.status.phase
		}));
	}
	catch(err) {
		console.error(err)
		return null
	}
}

class Client {
	constructor() {
		this.services = new Map;
		this.languages = [kDefaultLanguages, kDefaultLanguages];
	}

	async setServiceStatus(namespace, prod) {
		try {
			//let stagurl = `${server}/api/v1/namespaces/${stagingNamespace}/services`
			//let prodgurl = `${server}/api/v1/namespaces/${prodNamespace}/services`

			/*
			 * Changed code to use replica sets instead of service, as service is not updated for
			 * each new build that is deployed.
			 */
			let stagurl = `${server}/apis/apps/v1/namespaces/${stagingNamespace}/replicasets`
			let prodgurl = `${server}/apis/apps/v1/namespaces/${prodNamespace}/replicasets`


			let url=prod?prodgurl:stagurl
			const replicaList = await getRequest({ url: url });
			for (const item of replicaList.items) {
				const meta = item.metadata;
                const name = meta.labels.run;
                const version = meta.labels.version ? meta.labels.version : '1';
				if (typeof name === 'string') {
					let currentService = this.services.get((prod?'p_':'s_')+name);
					if (currentService) {
						let svcTime = new Date(meta.creationTimestamp).getTime();
						if (svcTime > currentService.created) {
							currentService.created = svcTime;
							currentService.link = meta.selfLink;
							currentService.uid = meta.uid;
                            currentService.version = meta.resourceVersion;
                            currentService.currentVersion = version;
						}
					}
					else {
						currentService = new Service(name, prod);
						currentService.link = meta.selfLink;
						currentService.uid = meta.uid;
						currentService.version = meta.resourceVersion;
						currentService.created = new Date(meta.creationTimestamp).getTime();
                        currentService.currentVersion = version;
                        
						this.services.set((prod?'p_':'s_')+name, currentService);
					}
				}
			}
			this.services.forEach(async (value, key, map) => {
				value.pods = await getPodStatus(value.name, prod);

				for (const p of value.pods) {
					if (p.state == 'Running') {
						value.state = 'Running';
						break;
					}
				}
			})
		}
		catch(err) {
			console.error(err)
		}
	}

	async updateServiceStatus() {
		try {
			await this.setServiceStatus(stagingNamespace, false)
			await this.setServiceStatus(prodNamespace, true)
		}
		catch(err) {
			console.error(err)
		}
		setTimeout(() => {
			this.updateServiceStatus()
		}, serviceStatusPollInterval*1000);

	}

	async start() {
		console.info('Starting K8S client');

		while (true) {
			try {
				await getRequest({ url: `${server}/api/v1/namespaces/${stagingNamespace}` });
			}
			catch (e) {
				if (e.errno && e.errno == 'ECONNREFUSED') {

					await sleep(kRetryTimeout);
					continue;
				}
			}

			break;
		}

		const now = new Date;
		const tasks = await getCompletedTasks();

		//console.debug('####', tasks);

		for (const i of tasks) {
			if (i.status || now - i.ts > kFailedBuildTimeout) {
				console.info('Deleting old build:', i.name);
				deleteTask(i.name);
			}
			else {
				failedBuilds.push(i);
			}
		}

		this.startLFC();

		console.debug('Retrieving service status\'');
		await this.updateServiceStatus()
		console.debug('Done. Listing codes and associated modules...');

		let i = 0;
		const ops = [];
		for (const [host, path] of await getRoutesRequest()) {

			const [,name, ns] = host.match(/^([^.]+)\.([^.]+)/);
			const [,spath] = path.match(/^\/ussd\/(.+)/);

			const code = spath.split('/').join('*');
			if (routeMap.has(code)) {
				ops.push({ op: 'remove', path: `/spec/http/${i}`});
				continue;
			}

			++i;
			const svc = (ns == prodNamespace ? 'p_' : 's_')+name;
			let s = this.services.get(svc);

			if (!s) {
				console.warn('  ', code, '=>', name, 'Ignoring code assignment. Not created by us -- assigned in routeMap though');
				continue;
			}

			console.debug('  ', code, '=>', name);
			routeMap.set(code, s);
			s.routes.push(code);
		}

		if (ops.length > 0) {
			try {
				await updateRequest({ 
					url: `${VIRTUAL_SERVICE_URL}/${virtualServiceName}`, 
					body: ops });
			}
			catch (e) {
				console.error('Failed to delete from VirtualService:', e);
			}
		}

		namespaces.map(async (ns, i) => {

			console.debug('Checking namespace:', ns, i);

			let url = CONFIGMAP_URL[i];
			let exists = false;

			console.debug('Checking ConfigMap:', url);

			try {
				const r = await getRequest({ url: `${url}/${kLanguageMapName}` });
				exists = true;

				console.debug('GET ConfigMap languages response:', r);

				this.languages[i] = fromLanguageData(r.data.LANG_MAP);
			}
			catch (e) {}

			if (!exists) {
				const k8sreq = createConfigmapRequest(ns, kDefaultLanguages);
				const yaml = k8s.dumpYaml(k8sreq);

				console.debug('K8S CREATE CONFIGMAP REQUEST:\n', `$ kubectl apply -f <<EOF\n${yaml}\nEOF`);

				try {
					await postRequest({ url, body: k8sreq });
				}
				catch (e) {
					console.error('Failed to create language map:', e);
				}
			}

			const languages = this.languages[i];
			console.log('Languages:', ns, languages);
		});
	}

	/*public*/ 
	execute(req) {
		const name = req.name;
		const svc = this.services.get((req.prod?'p_':'s_')+name);

		if (svc) {
			console.log('Updating service request:', name);
			
			return svc.update(req);
		}

		const s = new Service(name, req.prod);
		this.services.set((req.prod?'p_':'s_')+name, s);

		let response = s.update(req);
		this.updateServiceStatus(); // Perform update in background.
		return response;
	}

	buildStatus(req) {
		const name = req.name;
		const svc = this.services.get((req.prod?'p_':'s_')+name);

		if (!svc)
			return undefined;

		return svc.buildStatus(req.id);
	}

	serviceStatus(req) {
		const name = req.name;
		const svc = this.services.get((req.prod?'p_':'s_')+name);

		if (!svc)
			return undefined;

		return {
			service: svc.info,
			build: svc.buildInfo
		};
	}

	async listServiceStatus(req) {
		//await this.updateServiceStatus()
		let serviceList = []
		for (const [name, svc] of this.services.entries()) {
			serviceList.push({
				service: svc.info,
				build: svc.buildInfo
			})
		}
		return serviceList
	}

	async deleteService(req) {
		const name = `${req.prod ? 'p_' : 's_'}${req.name}`;
		const svc = this.services.get(name);

		if (!svc) {
			console.warn('K8s service does not exist:', req.name);
			return { error: 'Not exists' };
		}

		console.log('Deleting service:', req.name, 'codes:', svc.routes);

		try {
			await deleteRequest({ url: `${SERVICE_URL[~~req.prod]}/${req.name}` });
			await deleteRequest({ url: `${DEPLOY_URL[~~req.prod]}/${req.name}` });

			if (svc.routes.length > 0) {
				const indeces = [];
				let j = 0;
				for (const i of routeMap.keys()) {
					if (svc.routes.includes(i)) {
						routeMap.delete(i);
						indeces.push(j);
					}

					++j;
				}
				
				//indeces.sort();

				if (routeMap.size == 0) {
					await deleteRequest({ url: `${VIRTUAL_SERVICE_URL}/${virtualServiceName}` });
				}
				else {
					const ops = [];
					for (let i = 0, j = 0; i < indeces.length; ++i)
						ops.push({ op: 'remove', path: `/spec/http/${indeces[i] - j++}`});

					await updateRequest({
						url: `${VIRTUAL_SERVICE_URL}/${virtualServiceName}`,
						body: ops });
				}
			}

			this.services.delete(name);
		}
		catch (e) {
			console.error('Failed to delete service', req.name);
			return { error: 'Failed to delete service' };
		}

		return { service: svc.info };
	}

	async updateCodes(req) {
		const name = req.service;

		const svc = this.services.get((req.prod?'p_':'s_')+name);
		if (!svc) {
			console.error('Service does not exist:', name);
			return {
				error: `Service does not exist: ${name}`
			};
		}

		console.log("= gRPC updateCodes() for service: " + name);
		console.log("From Map", routeMap);

		if (req.remove) {
			console.log(req.remove.length > 0 ? "Removing codes:" : "Not removing, empty codes:", req.remove );
			const codes = [];

			for (const i of req.remove) {
				const c = normalizeCode(i);
				const s = routeMap.get(c);

				if (s != svc)
					continue;

				codes.push(c);
			}

			await svc.removeRoutes(codes);

			return { service: svc.info };
		}

		if (req.update) {
			console.log(req.update.length > 0 ? "Updating codes:" : "Not updating, empty codes:", req.update );
			const codes = [];

			for (const i of req.update) {
				const c = normalizeCode(i);
				const s = routeMap.get(c);

				if (s == svc)
					continue;

				codes.push(c);
			}

			await svc.updateRoutes(codes);

			return { service: svc.info };
		}

		if (req.replace) {
			console.log(req.replace.length > 0 ? "Replacing codes:" : "Not replacing, empty codes:", req.replace );
			const codes = [];

			for (const i of req.replace) {
				const code = normalizeCode(i.code);
				const s = routeMap.get(code);

				if (svc != s) 
					return { error: `Assigned to different Service: ${code}` };

				const newcode = normalizeCode(i.newcode);

				codes.push({ code, newcode });

				await svc.replaceRoutes(codes);

				return { service: svc.info };
			}
		}

		const add = [];

		if (req.add) {
			console.log(req.add.length > 0 ? "Adding codes:" : "Not adding, empty codes:", req.add );
			for (const i of req.add) {
				const c = normalizeCode(i);
				const s = routeMap.get(c);

				if (!s) {
					add.push(c); 
					continue;
				}

				if (s != svc) {
					console.error('Already assign to different service:', s.name);
					return {
						error: `${c} assigned to different service: ${s.name}`
					};
				}
			}
		}

		if (add.length > 0) {
			svc.newRoutes = add;
			await svc.addRoutes();
		}

		return { service: svc.info };
	}

	getLanguages(req) {
		return { langs: this.languages[~~req.prod] };
	}

	async updateLanguages(req) {

		const ns = ~~req.prod;
		this.languages[ns] = req.langs;

		try {

			await updateRequest({
				url: `${CONFIGMAP_URL[ns]}/${kLanguageMapName}`,
				body: [ { op: 'replace', path: '/data/LANG_MAP', value: toLanguageData(req.langs) } ]
			});

			const prefix = req.prod ? 'p_' : 's_';

			for (const [name, svc] of this.services.entries())
				if (name.startsWith(prefix))
					await svc.updateDeployment();
		}
		catch (e) {
			console.error('Failed to update languages:', e);
		}
	}

	startLFC() {
		setInterval(() => {

			const now = new Date;
			const builds = [];

			for (const i of failedBuilds) {
				if (now - i.ts > kFailedBuildTimeout) {
					console.info('Deleting failed build:', i.name);
					deleteTask(i.name);
				} 
				else {
					builds.push({ name: i.name, ts: i.request.ts });
				}
			}

			failedBuilds = builds;

		}, kLifecycleInterval);
	}
}

function createTaskRequest(req) {
	const image = registry + '/' + req.image.name + ':' + req.image.tag;
	const name = req.name;

	return {
		apiVersion: 'tekton.dev/v1beta1',
		kind: 'TaskRun',
		metadata: { name, namespace: buildNamespace },
		spec: {
			taskRef: { name: 'kaniko' },
			resources: {
				inputs: [ 
					{ 
						name: 'source',
						resourceSpec: {
							type: 'git',
							params: [
								{ name: 'url', value: req.source.url},
								// FIXME hard coding revision to master for the time being.
								{ name: 'revision', value: req.source.revision }
								// { name: 'revision', value:'master' }
							]
						}
					} 
				],
				outputs: [{
					name: 'image',
					resourceSpec: {
						type: 'image',
						params: [ { name: 'url', value: image } ]
					}
				}]
			}
		}
	};
}

function createDeployRequest(req, prod) {

	const name = req.name;
	const image = req.image;
	const port = process.env.PORT || 5001
	const HTTP_SERVER_PORT = process.env.HTTP_SERVER_PORT || 8085
	const namespace = prod ? prodNamespace : stagingNamespace;

	return {
		apiVersion: 'apps/v1',
		kind: 'Deployment',
		metadata: { name, namespace },
		spec: {
			selector: { matchLabels: { run: name } },
			replicas: 1,
			//strategy: { type: 'Recreate' },
			strategy: { type: 'RollingUpdate', rollingUpdate: { maxSurge: 1, maxUnavailable: 0 } },
			template: {
				metadata: { labels: { run: name, version: req.version }, creationTimestamp: new Date().toISOString() },
				spec: {
					containers: [
						{
							name, 
							image,
							imagePullPolicy: 'Always',
							livenessProbe: {
								failureThreshold: 3,
								httpGet: {
										path: "/",
										port: 5000,
										scheme: "HTTP"
								},
								initialDelaySeconds: 15,
								periodSeconds: 10,
								successThreshold: 1,
								timeoutSeconds: 15
							},
							ports: [
								{ containerPort: 5000, name: 'hux' }
							],
							readinessProbe: {
								failureThreshold: 3,
								httpGet: {
										path: "/",
										port: 5000,
										scheme: "HTTP"
								},
								initialDelaySeconds: 5,
								periodSeconds: 10,
								successThreshold: 1,
								timeoutSeconds: 3
							},
							env: [
								{
									name: 'NAMESPACE',
									value: namespace
								},
								{
									name: 'CS_ENVIRONMENT_NAME',
									value: process.env.CS_ENVIRONMENT_NAME || 'development'
								},
								{
									name: 'CS_CUSTOMER_NAME',
									value: process.env.CS_CUSTOMER_NAME || 'cs'
								},
								{
									name: 'CS_MENU_ENVIRONMENT',
									value: prod ? 'prod' : 'preprod'
								},
								{
									name: 'CS_CONFIG_PEERS',
									value: process.env.CS_CONFIG_PEERS || 'http://internal.coalescelab.com:2379'
								}
							],
							envFrom: [
								{ configMapRef: { name: "language-map" } }
							]
						}
					]
				}
			}
		}
	};
}

function createServiceRequest(name, currentNamespace) {
	const port = process.env.PORT || 5001
	const HTTP_SERVER_PORT = process.env.HTTP_SERVER_PORT || 8085
	return {
		apiVersion: 'v1',
		kind: 'Service',
		metadata: {
			name,
			namespace: currentNamespace,
			labels: { run: name }
		},
		spec: {
			selector: { run: name },
			type: 'ClusterIP',
			ports: [ { protocol: 'TCP', port: 5000, targetPort: 5000 } ]
		}
	}
}

function createVirtualServiceRequest(host, routes) {

	return {
		apiVersion: 'networking.istio.io/v1alpha3'
		, kind: 'VirtualService'
		, metadata: {
			name: virtualServiceName,
			namespace: "default"
		}
		, spec: {
			hosts: ['*']
			, gateways: [ gatewayName ]
			, http: routes.map(i => createMatchRoute(host, i))
		}
	};
}

function createMatchRoute(host, path) {
	return {
		match: [{
			uri: {
				prefix: path
			}
		}]
		, rewrite: { uri: '/RPC2' }
		, route: [{
			destination: {
				host
				, port: { number: 5000 }
			}
		}]
	};
}

function createNamespaceRequest(name) {
	return {
		apiVersion: 'v1'
		, kind: 'Namespace'
		, metadata: {
			name
			, labels: { 
				'istio-injection': 'enabled'
			}
		}
	};
}

function createConfigmapRequest(namespace, langs) {
	return {
		apiVersion: 'v1'
		, kind: 'ConfigMap'
		, metadata: {
			name: kLanguageMapName
			, namespace
		},
		data: {
			LANG_MAP: toLanguageData(langs)
		}
	};
}

function toLanguageData(langs) {
	//langs.unshift(langs[0]);
	return langs.map((v, i) => `${i+1}: ${v}`).join('\n') + '\n';
}

function fromLanguageData(data) {
	const langs = data.split('\n').slice(0, -1);
	const res = [];

	for (const l of langs) {
		const [i, lang] = l.split(': ');
		//res[Number(i - 1)] = lang.toUpperCase();
		res[Number(i - 1)] = lang;
	}

	return res;
}

async function getRoutesRequest() {
	//console.log('K8S VIRTUAL SERVICE STATUS REQUEST:\n', `$ kubectl describe virtualservice -n ${namespace} ${virtualServiceName}`);

	const routes = [];

	let res;
	try {
		res = await getRequest({ url: `${VIRTUAL_SERVICE_URL}/${virtualServiceName}` });
	}
	catch (e) {
		return routes;
	}
	
	//console.log(res);
	for (const r of res.spec.http) {
		const svc = r.route[0].destination.host;
		const path = r.match[0].uri.prefix;
		
		routes.push([svc, path]);
	}

	return routes;
}

// KNative API
function handleResponse(error, response, body, resolve, reject) {
	if (error) {
		console.error('K8S client HTTP request:', error)
		reject(error);
	} else if (response.statusCode >= 200 && response.statusCode <= 299) {
		//console.log('Successful REST request, HTTP status code:', response.statusCode);
		if (body) {
			//console.log('RESPONSE body:\n', body)
			resolve(JSON.parse(body));
		}
		else {
			resolve();
		}
	} else {
		//console.error('REST error:\n', response);
		let status = -1
		if (response.statusCode) {
			status = response.statusCode;
		}
		//console.log('buildAndDeploy request failed with an error/unknown statusCode:', statusCode)
		reject({ error: { status } });
	}
}

function setupRequest(req) {
	const headers = { 'Accept': 'application/json' };
	if (tf)
		headers['Authorization'] = `Bearer ${tf}`;

	if (!req.headers) req.headers = headers;
	else Object.assign(req.headers, headers);

	kc.applyToRequest(req);
	//console.debug('HTTP => Sending request:', req);
}

function postRequest(req) {
	setupRequest(req);

	if (req.body) {
		req.headers['Content-Type'] = 'application/json';
		req.body = JSON.stringify(req.body);
	}

	return new Promise((resolve, reject) => {
		request.post(req, (error, response, body) => {
			handleResponse(error, response, body, resolve, reject);
		})
	})
	.catch((err) => {
		console.error(err)
		return Promise.reject(new Error(err.message))
	});
}

async function updateRequest(req) {
	setupRequest(req);
	//console.debug('PATCH request\n', req);
	//
	if (req.body) {
		req.headers['Content-Type'] = 'application/json-patch+json';
		req.body = JSON.stringify(req.body);
	}

	return new Promise((resolve, reject) => {
		request.patch(req, (error, response, body) => {
			handleResponse(error, response, body, resolve, reject);
		});
	})
	.catch((err) => {
		console.error("updateRequest::Error processing request to URL "+req.url);
		console.error("updateRequest::Parameters "+req.body);
		console.error(err)
		return Promise.reject(new Error(err.message))
	});
}

function getRequest(req) {
	setupRequest(req);
	return new Promise((resolve, reject) => {
		request.get(req, (error, response, body) => {
			handleResponse(error, response, body, resolve, reject);
		});
	})
	.catch((err) => {
		console.error("getRequest::Error processing request to URL "+req.url);
		console.error(err)
		return Promise.reject(err);
	});
}

function deleteRequest(req) {
	setupRequest(req);
	return new Promise((resolve, reject) => {
		request.delete(req, (error, response, body) => {
			handleResponse(error, response, body, resolve, reject);
		});
	})
	.catch((err) => {
		console.error('error in deleteRequest',err)
		return Promise.reject(new Error(err.message))
	});
}

function getDate() {
	const now = new Date;

	const format = function(i) {
		return i < 10 ? '0' + i.toString() : i.toString();
	}

	return now.getFullYear().toString() + format(now.getMonth()) + format(now.getDate()) + format(now.getHours()) + format(now.getMinutes()) + format(now.getSeconds());
}

function normalizeCode(code) {
	return code.match(/^\*?([^#]+)#?$/)[1];
}

function routeToPath(route) {
	return '/ussd/' + route.split('*').join('/');
}

async function sleep(sec) {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			resolve();
		}, sec*1000);
	});
}

module.exports = {
	Client,
}
