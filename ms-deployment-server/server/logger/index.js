/* eslint-disable class-methods-use-this */
const log4js = require('log4js');
const path = require('path');

log4js.configure(path.join(__dirname, '../../conf/log4js.json'));

const logger = log4js.getLogger();

const ACCEPTED_LOG_LEVELS = ['debug', 'info', 'warn', 'error', 'fatal'];

console.level = function level(newLevel) {
  if (ACCEPTED_LOG_LEVELS.includes(String(newLevel).toLowerCase())) {
    logger.level = newLevel;
  }
};

console.debug = (...args) => logger.debug(...args);
console.log = (...args) => logger.info(...args);
console.info = (...args) => logger.info(...args);
console.warn = (...args) => logger.warn(...args);
console.error = (...args) => logger.error(...args);
console.fatal = (...args) => logger.fatal(...args);

module.exports = console;
