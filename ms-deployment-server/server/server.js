#!/usr/bin/env node

const http = require('http')

const protoPath = __dirname + '/../proto/deployment.proto'
const grpc = require('grpc')
const protoLoader = require('@grpc/proto-loader')
require('./logger');
require('dotenv').config()

console.info("CI / CD Build anv Version Information");
console.info("CI_BUILD_DATETIME: ", process.env.CI_BUILD_DATETIME);
console.info("CI_BUILD_NUMBER: ", process.env.CI_BUILD_NUMBER);
console.info("CI_COMMIT_REF: ", process.env.CI_COMMIT_REF);
console.info("CI_GITHUB_TAG: ", process.env.CI_GITHUB_TAG);
console.info("CI_BRANCH_NAME: ", process.env.CI_BRANCH_NAME);
console.info("CI_DOCKER_TAG: ", process.env.CI_DOCKER_TAG);

const k8s = require('../kubernetes/client');

const port = process.env.PORT || 5001
const HTTP_SERVER_PORT = process.env.HTTP_SERVER_PORT || 8085
const packageDefinition = protoLoader.loadSync(
	protoPath, 
	{ keepCase: true, longs: Number, enums: String, defaults: false, oneofs: true })

const protoDescriptor = grpc.loadPackageDefinition(packageDefinition)
const proto = protoDescriptor.Deployment

const server = new grpc.Server();
const client = new k8s.Client();

console.info('Starting HTTP server on: ', HTTP_SERVER_PORT);
http.createServer((req, res) => {

	if (req.method == 'GET') {
		res.statusCode = 200;
		res.end('Response to Kubernetes: I am Alive!');
		return;
	}

	console.warn('Unknown request? ... will responsd with 501 Not Implemented');
	console.error('Unknown request? ... will responsd with 501 Not Implemented', req);

	res.statusCode = 501;
	res.end('Not implemented');
}).listen(HTTP_SERVER_PORT);

function buildAndDeploy(call, callback) {
	const r = call.request

	console.log(`buildAndDeploy request name: ${r.name} code ${r.code}, namespace: ${r.prod?'production':'staging'}, source: ${JSON.stringify(r.source)}, image: ${JSON.stringify(r.image)}`)

	call.on('cancelled', () => {
		console.log('Client got disconnected')
	})

	let resp = null
	try {
		//resp = await k8s.executeBuildAndDeploy(r)
		resp = client.execute(r);
		if (resp.error) {
			console.error('buildAndDeploy encountered an error:', resp.error)
			callback(resp.error, null)
			return
		}
	}
	catch (e) {
		console.error('buildAndDeploy failed with:', e)
		callback(e)
		return
	}

	//const name = resp.name;
	//const name = 'coalesce-menu-server-deploy-XXX';
	//console.log('Successful buildAndDepoy request with name:', name)
	callback(null, resp);
}

function buildStatus(call, callback) {
	console.debug('gRPC: buildStatus request');

	const req = call.request;
	const r = client.buildStatus(req);

	if (r) callback(null, r);
	else callback(null, { error: 'Not exists' });
	//else callback({ code: grpc.status.UNKNOWN, message: 'Not exists' });
}

function serviceStatus(call, callback) {
	console.debug('gRPC: serviceStatus request');

	const req = call.request;
	const r = client.serviceStatus(req);

	if (r) callback(null, r);
	else callback(null, { error: 'Not exists' });
	//else callback({ code: grpc.status.UNKNOWN, message: 'Not exists' });
}
async function deleteService(call, callback) {
	console.debug('gRPC: deleteService request');

    const req = call.request;
    const r = await client.deleteService(req);
    
	if (r) callback(null, r);
	else callback(null, { error: 'Not exists' });
}

async function listServiceStatus(call, callback) {
	console.debug('gRPC: listServiceStatus request');

	const req = call.request;
	const r = await client.listServiceStatus(req);
	console.debug('gRPC: listServiceStatus response: ', r);

	if (r) callback(null, { services: r });
	else callback(null, { error: 'Not exists' });
}

async function updateCodes(call, callback) {
	console.debug('gRPC: updateCodes request');

	const req = call.request;
	const r = await client.updateCodes(req);

	if (r) callback(null, r);
	else callback(null, { error: 'Not exists' });
}

async function getLanguages(call, callback) {
	console.debug('gRPC: getLanguages request');

	const req = call.request;
	const r = await client.getLanguages(req);

	if (r) callback(null, r);
	else callback(null, { error: 'Not exists' });
}

async function updateLanguages(call, callback) {
	console.debug('gRPC: updateLanguages request');

	const req = call.request;
	const r = await client.updateLanguages(req);

	if (r) callback(null, r);
	else callback(null, { error: 'Not exists' });
}

async function start() {
	try {
		await client.start();

		console.info('Starting gRPC server on:', port);
		server.addService(proto.service, { buildAndDeploy, buildStatus, serviceStatus, listServiceStatus, updateCodes, deleteService
			, getLanguages
			, updateLanguages });
		server.bind('0.0.0.0:' + port, grpc.ServerCredentials.createInsecure());
		server.start();
	}
	catch(err) {
		console.error(err)
	}

}

if (require.main === module)
	start();
