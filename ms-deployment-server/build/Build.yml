.auto-build:
  image: "registry.gitlab.com/gitlab-org/cluster-integration/auto-build-image:v1.4.0"
  services:
    - docker:19.03.12-dind
  stage: build
  variables:
    DOCKER_TLS_CERTDIR: ""
  script:
    - |
      if [[ -z "$CI_COMMIT_TAG" ]]; then
        export CI_APPLICATION_REPOSITORY=${CI_APPLICATION_REPOSITORY:-$CI_REGISTRY_IMAGE/$CI_COMMIT_REF_SLUG}
        export CI_APPLICATION_TAG=${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
      else
        export CI_APPLICATION_REPOSITORY=${CI_APPLICATION_REPOSITORY:-$CI_REGISTRY_IMAGE}
        export CI_APPLICATION_TAG=${CI_APPLICATION_TAG:-$CI_COMMIT_TAG}
      fi
    - /build/build.sh

Build:
  extends: .auto-build
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: '$CI_COMMIT_BRANCH == "qa"'

Build on Tag:
  extends: .auto-build
  rules:
    - if: "$CI_COMMIT_TAG"
