.auto-deploy:
  image: "registry.gitlab.com/gitlab-org/cluster-integration/auto-deploy-image:v1.0.6" 
  script:
    - auto-deploy check_kube_domain
    - auto-deploy download_chart
    - auto-deploy ensure_namespace
    - auto-deploy initialize_tiller
    - auto-deploy create_secret
    - auto-deploy deploy
  cache: {}

Dev Deployment:
  extends: .auto-deploy
  stage: deploy_dev
  environment:
    name: development 
    kubernetes:
      namespace: cs-core-dev
  variables:
    HELM_UPGRADE_EXTRA_ARGS: --set namespace=cs-core-dev --set menuNamespaceBase=cs-menu-dev --set menuGateway=cs-menu-dev
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'

QA Deployment:
  extends: .auto-deploy
  stage: deploy_qa
  environment:
    name: qa 
    kubernetes:
      namespace: cs-core-qa
  variables:
    HELM_UPGRADE_EXTRA_ARGS: --set namespace=cs-core-qa --set menuNamespaceBase=cs-menu-qa --set menuGateway=cs-menu-qa
  rules:
    - if: '$CI_COMMIT_BRANCH == "qa"'

Staging Deployment:
  extends: .auto-deploy
  stage: deploy_staging
  environment:
    name: staging 
    kubernetes:
      namespace: cs-core-staging
  variables:
    HELM_UPGRADE_EXTRA_ARGS: --set namespace=cs-core-staging --set menuNamespaceBase=cs-menu-staging --set menuGateway=cs-menu-staging
  rules:
    - if: '$CI_COMMIT_BRANCH == "qa"'

Pre-Production Deployment:
  extends: .auto-deploy
  stage: deploy_preprod
  environment:
    name: preprod
    kubernetes:
      namespace: cs-core-preprod
  variables:
    HELM_UPGRADE_EXTRA_ARGS: --set namespace=cs-core-preprod --set menuNamespaceBase=cs-menu-preprod --set menuGateway=cs-menu-preprod
  rules:
    - if: '$CI_COMMIT_TAG'

Production Deployment:
  extends: .auto-deploy
  stage: deploy_production
  environment:
    name: production
    kubernetes:
      namespace: cs-core-prod
  variables:
    HELM_UPGRADE_EXTRA_ARGS: --set namespace=cs-core-prod --set menuNamespaceBase=cs-menu-prod --set menuGateway=cs-menu-prod
  allow_failure: false
  rules:
    - if: '$CI_COMMIT_TAG'
      when: manual
