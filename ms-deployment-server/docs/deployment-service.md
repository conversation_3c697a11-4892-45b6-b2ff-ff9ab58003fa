## [CoaleSCE Deployment Server)](https://coalescelab.com/)
 This document contains information related to CoaleSCE Deployment server i,e how it connects with CoaleSCE UI & Kubernetes Cluster

 Source code for DS is located in Gitlab repository here:
 https://gitlab.com/csys/products/coalesce-studio/microservices/ms-deployment-server


## Introduction:
Deployment Server is mainly responsible for building and deploying menus into the cluster, managing the deployments, services, configmaps, virtual service and the shortcodes associated with a running menu. It acts like a gateway between GUI BE and K8s API server.

1.  Build Docker images and store them in a private docker registry
1.  Manage Deployments (create/update/delete) in kubernetes namespaces using private docker images
1.  Manage virtual services and shortcodes associated with a running menu
1.  Manage Configmaps in kubernetes 
1.  Manage kubernetes services
1.  Maintain internal context (state) of all services running in K8s


## DS Startup:

When deployment server starts-up, it connects with Kubernetes API Server via a k8s-client library and pull valuable information from K8s and then creates the initial context (or state) and maintains that context throughout its lifecycle.

Following information would be pulled from kubernetes upon DS startup

1. Deployments
	- Deployments from staging namepace
	- Deployments from production namepace
1. Services
	- Services from staging namepace
	- Services from production namepace
1. Pods
	- Pods running in staging namepace
	- Pods running in production namepace
1. Replicasets
	- Replicasets from staging namepace
	- Replicasets from production namepace
1. Configmaps
	- Configmaps from staging namepace
	- Configmaps from production namepace
1. VirtualServices


DS will use the above info to create the internal context that will basically depict state of all running services (modules) alongwith heir associated shortcodes, configmaps (language-maps) for both staging and prod namespaces.

![deploy-server-startup](./images/deploy-server-startup.png)



### Docker Registry
<br>
The Docker Registry acts as a store for Docker images. When the Deployment Server has built a new menu, the resulting Docker image is stored in the private docker Registry. Kubernetes then uses that image to create the menu-pod.

<br>

## gRPC Methods (used for communication btw gui-backend and Deployment-server)

<br>

```sh
rpc buildAndDeploy (BuildRequest) returns (Response);
rpc buildStatus (BuildStatusRequest) returns (Response);
rpc serviceStatus (ServiceStatusRequest) returns (Response);
rpc listServiceStatus (NoParams) returns (ListResponse);
rpc updateCodes (UpdateCodesRequest) returns (Response);
rpc deleteService (ServiceInfo) returns (Response);
rpc getLanguages (GetLanguagesRequest) returns (LanguagesList);
rpc updateLanguages (UpdateLanguagesRequest) returns (Response);
```

<br><br>

## Sequence Diagram
```Note: Please reference this section for checking the requests/responses of all the gRPC methods mentioned in the below diagram```

[Request/Response messages of gRPC methods](#grpc-methods)

<br>

![Sequence Diagram](./images/sequence-diagram.png)

<br>

## Gui interaction with DS


#### 1) Push (deploy a menu to staging namespace via DS)
After creating and testing the menu via previewer (aka debugger), users can deploy the menus to kubernetes via Push button (located at the top of previewer). Push actions first of all creates/updates the menu config in a Git Repository (we are using an open-source version control software "Gitea" (similar to Github/Gitlab) to host all the menus(menu configs with all dependencies i,e components)) and it'll then sends a gRPC request to deployment-server to deploy the menu in staging(pre-prod) namespace in Kubernetes. Deployment server basically creates a new pod with a new instance of menu-server in a separate namespace(staging) in kubernetes. That menu-server instance would just run(handle) only one menu thats being passed-on unlike the menu-server instance for previewer(debugger) located in "core" namepsaces to handle any number of menus.

After pushing the menu to staging, that menu would appear on the launchpad staging-table. Users could assign multiple shortcodes to the menu via launchpad and all customer traffic could then be routed to that menu (pod) via those shortcodes.

Note: CoaleSCE App Developer (CAD) can launch menus to staging and assign shortcodes

```mermaid
graph LR
        A[Client] -- Push -->  B[GUI-SERVER - REST API ]
        B -- create/update git repo --> C[Gitea]
        C -- gRPC buildAndDeploy --> D[Deployment Service]
        D --> E[kubernetes]
        D -- gRPC buildAndDeploy --> C
```


All staging menus would be visible on Launchpad Page (pre-prod tab) 

#### 2) Launch (deploy a menu to production namespace via DS)
After testing the staging-menu (pod),  its now time to launch it to production namespace to handle the live traffic. This can be done via launch buttons located against menus running in staging namespace visisble on launchpad table. Upon pressing the launch button, GUI will then send a gRPC request to Deployment service to deploy the same menu to production namespace in kubernetes to handle live traffic. 

Note: Admins can launch menus to production and assign shortcodes

Production(Launched) menus could be visible on Launchpad Page  (Pre-Production Tab)

```mermaid
graph LR
        A[Client] -- Launch -->  B[GUI-SERVER - REST API ]
        B -- gRPC buildAndDeploy --> C[Deployment Service]
        C --> D[kubernetes]
        C -- gRPC buildAndDeploy --> B
```

## Launchpad:
The CoaleSCE launchpad is an easy to manage production pipeline with in-built testing, soft-launch and A/B testing of applications and their dependencies. Users can view and manage the launch of applications to the pre-production and production environments. The status or health of the application is visible at a glance, as well as the date of launch and version in use. The launchpad allows UI management of the USSD shortcode assigned to the application, both for use on the pre-production and production node(s). Standard length and long shortcodes are supported (e.g. *123# , *12345#) an well as single shot USSD requests (e.g. *123*1*2*3# corresponding to the options selected via USSD menu navigation).

<br>

![Getting Started](./images/pushAndLaunch.png)

<br>

### <div id="grpc-methods">Request/Response messages of gRPC methods</div> 

```java
message NoParams {}

message ListResponse {
	repeated Response services = 1;
}

message GitRepository {
	string url = 1;
	string revision = 2;
	string sha = 3;
}

message ImageInformation {
	string name = 1;
	string tag = 2;
	bool always_pull = 3;
}

enum BuildState {
	Canceled = 1;
	Pending = 2;
	Building = 3;
	BuildFailed = 4;
	Deploying = 5;
	DeployFailed = 6;
	CodeExists = 7;
	Succeeded = 8;
}

message BuildInfo {
	BuildState state = 1;
	string id = 2; // sha of source
	int64 created = 3;
}

enum ServiceState {
	Running = 1;
	NotRunning = 2;
	Deploying = 3;
	DeployFailed = 4;
	NotExists = 10;
}

message ServiceInfo {
	string name = 1;
	ServiceState state = 2;
	int64 created = 3;
	bool prod = 4;
	repeated string codes = 5;
	string version = 6;
}

message BuildRequest {
	string name = 1;
	bool prod = 2;
	repeated string codes = 3;
	GitRepository source = 4;
	ImageInformation image = 5;
	string version = 6;
}

message BuildStatusRequest {
	string name = 1;
	bool prod = 2;
	string id = 3; // sha of source
}

  

message ServiceStatusRequest {
	string name = 1;
	bool prod = 2;
}

  

message ReplacePair {
	string code = 1;
	string newcode = 2;
}

  

message UpdateCodesRequest {
	string service = 1;
	bool prod = 2;
	repeated string add = 3;
	repeated string remove = 4;
	repeated string update = 5;
	repeated ReplacePair replace = 6;
}

message GetLanguagesRequest {
	bool prod = 1;
}

message LanguagesList {
	repeated string langs = 1;
}

message UpdateLanguagesRequest {
	bool prod = 1;
	repeated string langs = 2;
}

// Responses

message Response {
	string error = 1;
	ServiceInfo service = 2;
	BuildInfo build = 3;
}
```
