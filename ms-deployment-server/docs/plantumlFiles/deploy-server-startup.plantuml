@startuml

skinparam ParticipantPadding 20
skinparam BoxPadding 40
!pragma teoz true

participant "Deployment Server" as DS


box 
participant "Kubernet<PERSON>" as K8s
end box

 
DS --> K8s :Get all deployments (from staging and prod namespaces)
DS --> K8s :Get all services (from staging and prod namespaces)
DS --> K8s :Get all pods (from staging and prod namespaces)
DS --> K8s :Get all configmaps (from staging and prod namespaces)
DS --> K8s :Get all vritualservices


@enduml