@startuml
skinparam ParticipantPadding 20
skinparam BoxPadding 40
!pragma teoz true

participant guiServer as GS


box
participant DeploymentServer as DS
end box

participant DockerResgistry as Registry


box
participant Kubernetes as K8s
end box

== buildAndDeploy ==

GS -> DS: gRPC buildAndDeploy Req
activate DS #FFBBBB


note right 
 //build Image //
 //and push to //
 //docker registry//
end note

DS -> Registry:
Registry --> DS: Image built and stored in registry

DS -> K8s: ** createOrUpdateDeployment in K8s **
activate K8s
K8s --> DS: Deployment created/updated
deactivate K8s


DS --> GS: buildAndDeploy Response
deactivate DS



== buildStatus ==

GS -> DS: gRPC buildStatus Req
activate DS #FFBBBB


DS --> GS: buildStatus Response
deactivate DS


== serviceStatus ==
GS -> DS: gRPC serviceStatus Req
activate DS #FFBBBB

DS --> GS: serviceStatus Response
deactivate DS


== listServiceStatus ==


GS -> DS: gRPC listServiceStatus Req
activate DS #FFBBBB
DS --> GS: serviceStatus Response
deactivate DS


== updateCodes ==


GS -> DS: gRPC listServiceStatus Req
activate DS #FFBBBB


DS -> K8s: create/update shortcode in k8s virtualservice
activate K8s
K8s --> DS: shortcode updated in VS
deactivate K8s
DS --> DS: update internal context info
DS --> GS: serviceStatus Response
deactivate DS


== deleteService ==


GS -> DS: gRPC deleteService Req
activate DS #FFBBBB

DS -> K8s: deleteService from kubernetes
activate K8s
K8s --> DS: service deleted from kubernetes
deactivate K8s
DS --> DS: update internal context info
DS --> GS: deleteService Response
deactivate DS


== getLanguages ==

GS -> DS: gRPC getLanguages Req
activate DS #FFBBBB

DS --> GS: getLanguages Response
deactivate DS

== updateLanguages ==

GS -> DS: gRPC updateLanguages Req
activate DS #FFBBBB

DS -> K8s: update a configmap named "language-map" in kubernetes ns
activate K8s
K8s --> DS: configmap updated in k8s
deactivate K8s
DS --> DS: update internal context info
DS --> GS: updateLanguages Response
deactivate DS

@enduml