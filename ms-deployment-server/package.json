{"name": "ms-deployment-server", "version": "1.0.0", "description": "The Deployment Microservice exposes a gRPC API that allows clients to build and deploy applications on a Kubernetes cluster.", "main": "server/server.js", "config": {"entrypoint": "server/server.js"}, "scripts": {"start": "node $npm_package_config_entrypoint", "test": "echo \"Error: no test specified\" && exit 0"}, "repository": {"type": "git", "url": "**************:csys/products/coalesce-studio/microservices/ms-deployment-server.git"}, "keywords": ["microservice", "deployment", "Kubernetes", "gRPC", "Knative", "Kanik<PERSON>"], "author": "Concurrent Systems", "license": "ISC", "dependencies": {"@grpc/proto-loader": "^0.6.2", "@kubernetes/client-node": "^0.14.3", "dotenv": "^16.3.1", "grpc": "^1.24.10", "log4js": "^6.3.0"}, "devDependencies": {"@semantic-release/gitlab-config": "^8.0.0", "semantic-release": "^17.4.4", "yargs": "^17.0.1"}}