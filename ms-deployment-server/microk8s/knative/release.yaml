
---
# eventing.yaml
apiVersion: v1
kind: Namespace
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: knative-eventing

---
aggregationRule:
  clusterRoleSelectors:
  - matchLabels:
      duck.knative.dev/addressable: "true"
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: addressable-resolver
rules: []
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    duck.knative.dev/addressable: "true"
    eventing.knative.dev/release: "v0.8.0"
  name: service-addressable-resolver
rules:
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    duck.knative.dev/addressable: "true"
    eventing.knative.dev/release: "v0.8.0"
  name: serving-addressable-resolver
rules:
- apiGroups:
  - serving.knative.dev
  resources:
  - routes
  - routes/status
  - services
  - services/status
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    duck.knative.dev/addressable: "true"
    eventing.knative.dev/release: "v0.8.0"
  name: channel-addressable-resolver
rules:
- apiGroups:
  - eventing.knative.dev
  - messaging.knative.dev
  resources:
  - channels
  - channels/status
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    duck.knative.dev/addressable: "true"
    eventing.knative.dev/release: "v0.8.0"
  name: broker-addressable-resolver
rules:
- apiGroups:
  - eventing.knative.dev
  resources:
  - brokers
  - brokers/status
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    duck.knative.dev/addressable: "true"
    eventing.knative.dev/release: "v0.8.0"
  name: messaging-addressable-resolver
rules:
- apiGroups:
  - messaging.knative.dev
  resources:
  - sequences
  - sequences/status
  - choices
  - choices/status
  verbs:
  - get
  - list
  - watch

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: eventing-broker-filter
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - eventing.knative.dev
  resources:
  - triggers
  - triggers/status
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: eventing-broker-ingress
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: eventing-config-reader
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch

---
aggregationRule:
  clusterRoleSelectors:
  - matchLabels:
      duck.knative.dev/channelable: "true"
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: channelable-manipulator
rules: []

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    rbac.authorization.k8s.io/aggregate-to-admin: "true"
  name: knative-eventing-namespaced-admin
rules:
- apiGroups:
  - eventing.knative.dev
  resources:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    rbac.authorization.k8s.io/aggregate-to-admin: "true"
  name: knative-messaging-namespaced-admin
rules:
- apiGroups:
  - messaging.knative.dev
  resources:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    rbac.authorization.k8s.io/aggregate-to-admin: "true"
  name: knative-eventing-sources-namespaced-admin
rules:
- apiGroups:
  - sources.eventing.knative.dev
  resources:
  - '*'
  verbs:
  - '*'

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: knative-eventing-controller
rules:
- apiGroups:
  - ""
  resources:
  - namespaces
  - secrets
  - configmaps
  - services
  - events
  - serviceaccounts
  verbs:
  - get
  - list
  - create
  - update
  - delete
  - patch
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - get
  - list
  - create
  - update
  - delete
  - patch
  - watch
- apiGroups:
  - rbac.authorization.k8s.io
  resources:
  - rolebindings
  verbs:
  - get
  - list
  - create
  - update
  - delete
  - patch
  - watch
- apiGroups:
  - eventing.knative.dev
  resources:
  - brokers
  - brokers/status
  - channels
  - channels/status
  - clusterchannelprovisioners
  - clusterchannelprovisioners/status
  - subscriptions
  - subscriptions/status
  - triggers
  - triggers/status
  - eventtypes
  - eventtypes/status
  verbs:
  - get
  - list
  - create
  - update
  - delete
  - patch
  - watch
- apiGroups:
  - eventing.knative.dev
  resources:
  - brokers/finalizers
  - triggers/finalizers
  verbs:
  - update
- apiGroups:
  - messaging.knative.dev
  resources:
  - sequences
  - sequences/status
  - channels
  - channels/status
  - choices
  - choices/status
  verbs:
  - get
  - list
  - create
  - update
  - delete
  - patch
  - watch
- apiGroups:
  - messaging.knative.dev
  resources:
  - sequences/finalizers
  - choices/finalizers
  verbs:
  - update
- apiGroups:
  - apiextensions.k8s.io
  resources:
  - customresourcedefinitions
  verbs:
  - get
  - list
  - watch

---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: eventing-controller
  namespace: knative-eventing
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: eventing-webhook
  namespace: knative-eventing
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: eventing-source-controller
  namespace: knative-eventing

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: knative-eventing-source-controller
rules:
- apiGroups:
  - ""
  resources:
  - secrets
  - configmaps
  - services
  verbs:
  - get
  - list
  - create
  - update
  - delete
  - patch
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - get
  - list
  - create
  - update
  - delete
  - patch
  - watch
- apiGroups:
  - sources.eventing.knative.dev
  resources:
  - cronjobsources
  - cronjobsources/status
  - cronjobsources/finalizers
  - containersources
  - containersources/status
  - containersources/finalizers
  - apiserversources
  - apiserversources/status
  - apiserversources/finalizers
  verbs:
  - get
  - list
  - create
  - update
  - delete
  - patch
  - watch
- apiGroups:
  - serving.knative.dev
  resources:
  - services
  verbs:
  - get
  - list
  - create
  - update
  - delete
  - patch
  - watch
- apiGroups:
  - eventing.knative.dev
  resources:
  - eventtypes
  verbs:
  - get
  - list
  - create
  - update
  - delete
  - patch
  - watch
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - get
  - list
  - create
  - update
  - delete
  - patch
  - watch

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: knative-eventing-webhook
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - create
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - get
- apiGroups:
  - apps
  resources:
  - deployments/finalizers
  verbs:
  - update
- apiGroups:
  - admissionregistration.k8s.io
  resources:
  - mutatingwebhookconfigurations
  verbs:
  - get
  - list
  - create
  - update
  - delete
  - patch
  - watch
- apiGroups:
  - eventing.knative.dev
  resources:
  - brokers
  - brokers/status
  - channels
  - channels/status
  - clusterchannelprovisioners
  - clusterchannelprovisioners/status
  - subscriptions
  - subscriptions/status
  - triggers
  - triggers/status
  - eventtypes
  - eventtypes/status
  verbs:
  - get
  - list
  - watch

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: eventing-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: knative-eventing-controller
subjects:
- kind: ServiceAccount
  name: eventing-controller
  namespace: knative-eventing
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: eventing-controller-resolver
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: addressable-resolver
subjects:
- kind: ServiceAccount
  name: eventing-controller
  namespace: knative-eventing
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: eventing-controller-manipulator
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: channelable-manipulator
subjects:
- kind: ServiceAccount
  name: eventing-controller
  namespace: knative-eventing
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: eventing-webhook
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: knative-eventing-webhook
subjects:
- kind: ServiceAccount
  name: eventing-webhook
  namespace: knative-eventing
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: eventing-source-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: knative-eventing-source-controller
subjects:
- kind: ServiceAccount
  name: eventing-source-controller
  namespace: knative-eventing
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: eventing-source-controller-resolver
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: addressable-resolver
subjects:
- kind: ServiceAccount
  name: eventing-source-controller
  namespace: knative-eventing

---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  creationTimestamp: null
  labels:
    eventing.knative.dev/release: "v0.8.0"
    eventing.knative.dev/source: "true"
    knative.dev/crd-install: "true"
  name: apiserversources.sources.eventing.knative.dev
spec:
  group: sources.eventing.knative.dev
  names:
    categories:
    - all
    - knative
    - eventing
    - sources
    - importers
    kind: ApiServerSource
    plural: apiserversources
  scope: Namespaced
  validation:
    openAPIV3Schema:
      properties:
        registry:
          description: Internal information, users should not set this property
          properties:
            eventTypes:
              description: Event types that ApiServerSource can produce
              properties:
                addRef:
                  properties:
                    schema:
                      type: string
                    type:
                      pattern: dev.knative.apiserver.ref.add
                      type: string
                  type: object
                addResource:
                  properties:
                    schema:
                      type: string
                    type:
                      pattern: dev.knative.apiserver.resource.add
                      type: string
                  type: object
                deleteRef:
                  properties:
                    schema:
                      type: string
                    type:
                      pattern: dev.knative.apiserver.ref.delete
                      type: string
                  type: object
                deleteResource:
                  properties:
                    schema:
                      type: string
                    type:
                      pattern: dev.knative.apiserver.resource.delete
                      type: string
                  type: object
                updateRef:
                  properties:
                    schema:
                      type: string
                    type:
                      pattern: dev.knative.apiserver.ref.update
                      type: string
                  type: object
                updateResource:
                  properties:
                    schema:
                      type: string
                    type:
                      pattern: dev.knative.apiserver.resource.update
                      type: string
                  type: object
              type: object
          type: object
        spec:
          properties:
            mode:
              description: 'Mode controls the content of the event payload. One of:
                ''Ref'' (only references of resources), ''Resource'' (full resource).'
              type: string
            resources:
              items:
                properties:
                  apiVersion:
                    description: API version of the objects to watch.
                    type: string
                  controller:
                    description: 'If true, emits the managing controller ref. Only
                      supported for mode=Ref. More info: https://kubernetes.io/docs/concepts/workloads/controllers/garbage-collection/'
                    type: boolean
                  kind:
                    description: Kind of the objects to watch.
                    type: string
              type: array
            serviceAccountName:
              description: 'name of the ServiceAccount to use to run the receive adapter.
                More info: https://kubernetes.io/docs/reference/access-authn-authz/service-accounts-admin/.'
              type: string
            sink:
              description: A reference to the object that should receive events.
              type: object
          required:
          - resources
          - sink
          type: object
        status:
          properties:
            conditions:
              items:
                properties:
                  lastTransitionTime:
                    type: string
                  message:
                    type: string
                  reason:
                    type: string
                  severity:
                    type: string
                  status:
                    type: string
                  type:
                    type: string
                required:
                - type
                - status
                type: object
              type: array
            sinkUri:
              type: string
          type: object
  versions:
  - name: v1alpha1
    served: true
    storage: true

---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    knative.dev/crd-install: "true"
  name: brokers.eventing.knative.dev
spec:
  additionalPrinterColumns:
  - JSONPath: .status.conditions[?(@.type=="Ready")].status
    name: Ready
    type: string
  - JSONPath: .status.conditions[?(@.type=="Ready")].reason
    name: Reason
    type: string
  - JSONPath: .status.address.hostname
    name: Hostname
    type: string
  - JSONPath: .metadata.creationTimestamp
    name: Age
    type: date
  group: eventing.knative.dev
  names:
    categories:
    - all
    - knative
    - eventing
    kind: Broker
    plural: brokers
    singular: broker
  scope: Namespaced
  validation:
    openAPIV3Schema:
      properties:
        spec:
          properties:
            channelTemplate:
              properties:
                arguments:
                  type: object
                provisioner:
                  properties:
                    apiVersion:
                      minLength: 1
                      type: string
                    kind:
                      minLength: 1
                      type: string
                    name:
                      minLength: 1
                      type: string
                  required:
                  - apiVersion
                  - kind
                  - name
                  type: object
              required:
              - provisioner
              type: object
  versions:
  - name: v1alpha1
    served: true
    storage: true

---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    knative.dev/crd-install: "true"
  name: channels.eventing.knative.dev
spec:
  additionalPrinterColumns:
  - JSONPath: .status.conditions[?(@.type=="Ready")].status
    name: Ready
    type: string
  - JSONPath: .status.conditions[?(@.type=="Ready")].reason
    name: Reason
    type: string
  - JSONPath: .metadata.creationTimestamp
    name: Age
    type: date
  group: eventing.knative.dev
  names:
    categories:
    - all
    - knative
    - eventing
    kind: Channel
    plural: channels
    shortNames:
    - chan
    singular: channel
  scope: Namespaced
  validation:
    openAPIV3Schema:
      properties:
        spec:
          properties:
            arguments:
              type: object
            provisioner:
              properties:
                apiVersion:
                  minLength: 1
                  type: string
                kind:
                  minLength: 1
                  type: string
                name:
                  minLength: 1
                  type: string
              required:
              - apiVersion
              - kind
              - name
              type: object
            subscribable:
              properties:
                subscribers:
                  items:
                    properties:
                      generation:
                        type: integer
                      ref:
                        properties:
                          apiVersion:
                            type: string
                          kind:
                            type: string
                          name:
                            minLength: 1
                            type: string
                          namespace:
                            minLength: 1
                            type: string
                          uid:
                            minLength: 1
                            type: string
                        required:
                        - namespace
                        - name
                        - uid
                        type: object
                      replyURI:
                        minLength: 1
                        type: string
                      subscriberURI:
                        minLength: 1
                        type: string
                      uid:
                        minLength: 1
                        type: string
                    required:
                    - uid
                  type: array
              type: object
  versions:
  - name: v1alpha1
    served: true
    storage: true

---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    knative.dev/crd-install: "true"
    messaging.knative.dev/subscribable: "true"
  name: channels.messaging.knative.dev
spec:
  additionalPrinterColumns:
  - JSONPath: .status.conditions[?(@.type=="Ready")].status
    name: Ready
    type: string
  - JSONPath: .status.conditions[?(@.type=="Ready")].reason
    name: Reason
    type: string
  - JSONPath: .status.address.url
    name: URL
    type: string
  - JSONPath: .metadata.creationTimestamp
    name: Age
    type: date
  group: messaging.knative.dev
  names:
    categories:
    - all
    - knative
    - messaging
    - channel
    kind: Channel
    plural: channels
    shortNames:
    - ch
    singular: channel
  scope: Namespaced
  validation:
    openAPIV3Schema:
      properties:
        spec:
          properties:
            channelTemplate:
              properties:
                apiVersion:
                  minLength: 1
                  type: string
                kind:
                  minLength: 1
                  type: string
                spec:
                  type: object
              required:
              - apiVersion
              - kind
              type: object
            subscribable:
              properties:
                subscribers:
                  items:
                    properties:
                      ref:
                        properties:
                          apiVersion:
                            type: string
                          kind:
                            type: string
                          name:
                            minLength: 1
                            type: string
                          namespace:
                            minLength: 1
                            type: string
                          uid:
                            minLength: 1
                            type: string
                        required:
                        - namespace
                        - name
                        - uid
                        type: object
                      replyURI:
                        minLength: 1
                        type: string
                      subscriberURI:
                        minLength: 1
                        type: string
                      uid:
                        minLength: 1
                        type: string
                    required:
                    - uid
                  type: array
              type: object
  versions:
  - name: v1alpha1
    served: true
    storage: true

---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    knative.dev/crd-install: "true"
  name: choices.messaging.knative.dev
spec:
  additionalPrinterColumns:
  - JSONPath: .status.conditions[?(@.type=="Ready")].status
    name: Ready
    type: string
  - JSONPath: .status.conditions[?(@.type=="Ready")].reason
    name: Reason
    type: string
  - JSONPath: .status.address.url
    name: URL
    type: string
  - JSONPath: .metadata.creationTimestamp
    name: Age
    type: date
  group: messaging.knative.dev
  names:
    categories:
    - all
    - knative
    - eventing
    - messaging
    kind: Choice
    plural: choices
    singular: choice
  scope: Namespaced
  validation:
    openAPIV3Schema:
      properties:
        spec:
          properties:
            cases:
              items:
                properties:
                  filter:
                    properties:
                      dnsName:
                        minLength: 1
                        type: string
                      ref:
                        properties:
                          apiVersion:
                            minLength: 1
                            type: string
                          kind:
                            minLength: 1
                            type: string
                          name:
                            minLength: 1
                            type: string
                          namespace:
                            maxLength: 0
                            type: string
                        required:
                        - apiVersion
                        - kind
                        - name
                        type: object
                      uri:
                        minLength: 1
                        type: string
                    type: object
                  subscriber:
                    properties:
                      dnsName:
                        minLength: 1
                        type: string
                      ref:
                        properties:
                          apiVersion:
                            minLength: 1
                            type: string
                          kind:
                            minLength: 1
                            type: string
                          name:
                            minLength: 1
                            type: string
                          namespace:
                            maxLength: 0
                            type: string
                        required:
                        - apiVersion
                        - kind
                        - name
                        type: object
                      uri:
                        minLength: 1
                        type: string
                    type: object
                required:
                - subscriber
                type: object
              type: array
            channelTemplate:
              properties:
                apiVersion:
                  minLength: 1
                  type: string
                kind:
                  minLength: 1
                  type: string
                spec:
                  type: object
              required:
              - apiVersion
              - kind
              type: object
          required:
          - cases
          - channelTemplate
  version: v1alpha1

---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    knative.dev/crd-install: "true"
  name: clusterchannelprovisioners.eventing.knative.dev
spec:
  additionalPrinterColumns:
  - JSONPath: .status.conditions[?(@.type=="Ready")].status
    name: Ready
    type: string
  - JSONPath: .status.conditions[?(@.type=="Ready")].reason
    name: Reason
    type: string
  - JSONPath: .metadata.creationTimestamp
    name: Age
    type: date
  group: eventing.knative.dev
  names:
    categories:
    - all
    - knative
    - eventing
    - provisioner
    kind: ClusterChannelProvisioner
    plural: clusterchannelprovisioners
    shortNames:
    - ccp
    singular: clusterchannelprovisioner
  scope: Cluster
  versions:
  - name: v1alpha1
    served: true
    storage: true

---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    eventing.knative.dev/source: "true"
    knative.dev/crd-install: "true"
  name: containersources.sources.eventing.knative.dev
spec:
  group: sources.eventing.knative.dev
  names:
    categories:
    - all
    - knative
    - eventing
    - sources
    - importers
    kind: ContainerSource
    plural: containersources
  scope: Namespaced
  validation:
    openAPIV3Schema:
      properties:
        apiVersion:
          type: string
        kind:
          type: string
        metadata:
          type: object
        spec:
          properties:
            args:
              items:
                type: string
              type: array
            env:
              items:
                type: object
              type: array
            image:
              minLength: 1
              type: string
            serviceAccountName:
              type: string
            sink:
              type: object
            template:
              type: object
          type: object
        status:
          properties:
            conditions:
              items:
                properties:
                  lastTransitionTime:
                    type: string
                  message:
                    type: string
                  reason:
                    type: string
                  severity:
                    type: string
                  status:
                    type: string
                  type:
                    type: string
                required:
                - type
                - status
                type: object
              type: array
            sinkUri:
              type: string
          type: object
  versions:
  - name: v1alpha1
    served: true
    storage: true

---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    eventing.knative.dev/source: "true"
    knative.dev/crd-install: "true"
  name: cronjobsources.sources.eventing.knative.dev
spec:
  group: sources.eventing.knative.dev
  names:
    categories:
    - all
    - knative
    - eventing
    - sources
    - importers
    kind: CronJobSource
    plural: cronjobsources
  scope: Namespaced
  validation:
    openAPIV3Schema:
      properties:
        registry:
          description: Internal information, users should not set this property
          properties:
            eventTypes:
              description: Event types that CronJobSource can produce
              properties:
                cron:
                  properties:
                    schema:
                      type: string
                    type:
                      pattern: dev.knative.cronjob.event
                      type: string
                  type: object
              type: object
          type: object
        spec:
          properties:
            data:
              type: string
            resources:
              properties:
                limits:
                  properties:
                    cpu:
                      type: string
                    memory:
                      type: string
                  type: object
                requests:
                  properties:
                    cpu:
                      type: string
                    memory:
                      type: string
                  type: object
              type: object
            schedule:
              type: string
            serviceAccountName:
              type: string
            sink:
              type: object
          required:
          - schedule
          type: object
        status:
          properties:
            conditions:
              items:
                properties:
                  lastTransitionTime:
                    type: string
                  message:
                    type: string
                  reason:
                    type: string
                  severity:
                    type: string
                  status:
                    type: string
                  type:
                    type: string
                required:
                - type
                - status
                type: object
              type: array
            sinkUri:
              type: string
          type: object
  versions:
  - name: v1alpha1
    served: true
    storage: true

---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    knative.dev/crd-install: "true"
  name: eventtypes.eventing.knative.dev
spec:
  additionalPrinterColumns:
  - JSONPath: .spec.type
    name: Type
    type: string
  - JSONPath: .spec.source
    name: Source
    type: string
  - JSONPath: .spec.schema
    name: Schema
    type: string
  - JSONPath: .spec.broker
    name: Broker
    type: string
  - JSONPath: .spec.description
    name: Description
    type: string
  - JSONPath: .status.conditions[?(@.type=="Ready")].status
    name: Ready
    type: string
  - JSONPath: .status.conditions[?(@.type=="Ready")].reason
    name: Reason
    type: string
  group: eventing.knative.dev
  names:
    categories:
    - all
    - knative
    - eventing
    kind: EventType
    plural: eventtypes
    singular: eventtype
  scope: Namespaced
  validation:
    openAPIV3Schema:
      properties:
        spec:
          properties:
            broker:
              minLength: 1
              type: string
            description:
              type: string
            schema:
              type: string
            source:
              minLength: 1
              type: string
            type:
              minLength: 1
              type: string
          required:
          - type
          - source
          - broker
  versions:
  - name: v1alpha1
    served: true
    storage: true

---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    knative.dev/crd-install: "true"
  name: sequences.messaging.knative.dev
spec:
  additionalPrinterColumns:
  - JSONPath: .status.conditions[?(@.type=="Ready")].status
    name: Ready
    type: string
  - JSONPath: .status.conditions[?(@.type=="Ready")].reason
    name: Reason
    type: string
  - JSONPath: .status.address.hostname
    name: Hostname
    type: string
  - JSONPath: .metadata.creationTimestamp
    name: Age
    type: date
  group: messaging.knative.dev
  names:
    categories:
    - all
    - knative
    - eventing
    - messaging
    kind: Sequence
    plural: sequences
    singular: sequence
  scope: Namespaced
  validation:
    openAPIV3Schema:
      properties:
        spec:
          properties:
            channelTemplate:
              properties:
                apiVersion:
                  minLength: 1
                  type: string
                kind:
                  minLength: 1
                  type: string
                spec:
                  type: object
              required:
              - apiVersion
              - kind
              type: object
            steps:
              items:
                properties:
                  dnsName:
                    minLength: 1
                    type: string
                  ref:
                    properties:
                      apiVersion:
                        minLength: 1
                        type: string
                      kind:
                        minLength: 1
                        type: string
                      name:
                        minLength: 1
                        type: string
                      namespace:
                        maxLength: 0
                        type: string
                    required:
                    - apiVersion
                    - kind
                    - name
                    type: object
                  uri:
                    minLength: 1
                    type: string
                type: object
              type: array
          required:
          - steps
          - channelTemplate
  versions:
  - name: v1alpha1
    served: true
    storage: true

---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    knative.dev/crd-install: "true"
  name: subscriptions.eventing.knative.dev
spec:
  additionalPrinterColumns:
  - JSONPath: .status.conditions[?(@.type=="Ready")].status
    name: Ready
    type: string
  - JSONPath: .status.conditions[?(@.type=="Ready")].reason
    name: Reason
    type: string
  - JSONPath: .metadata.creationTimestamp
    name: Age
    type: date
  group: eventing.knative.dev
  names:
    categories:
    - all
    - knative
    - eventing
    kind: Subscription
    plural: subscriptions
    shortNames:
    - sub
    singular: subscription
  scope: Namespaced
  validation:
    openAPIV3Schema:
      properties:
        spec:
          properties:
            channel:
              properties:
                apiVersion:
                  minLength: 1
                  type: string
                kind:
                  type: string
                name:
                  minLength: 1
                  type: string
              required:
              - apiVersion
              - kind
              - name
              type: object
            reply:
              properties:
                channel:
                  properties:
                    apiVersion:
                      minLength: 1
                      type: string
                    kind:
                      type: string
                    name:
                      minLength: 1
                      type: string
                  required:
                  - apiVersion
                  - kind
                  - name
                  type: object
              type: object
            subscriber:
              properties:
                dnsName:
                  minLength: 1
                  type: string
                ref:
                  properties:
                    apiVersion:
                      minLength: 1
                      type: string
                    kind:
                      minLength: 1
                      type: string
                    name:
                      minLength: 1
                      type: string
                  required:
                  - apiVersion
                  - kind
                  - name
                  type: object
                uri:
                  minLength: 1
                  type: string
              type: object
          required:
          - channel
  versions:
  - name: v1alpha1
    served: true
    storage: true

---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    knative.dev/crd-install: "true"
  name: triggers.eventing.knative.dev
spec:
  additionalPrinterColumns:
  - JSONPath: .status.conditions[?(@.type=="Ready")].status
    name: Ready
    type: string
  - JSONPath: .status.conditions[?(@.type=="Ready")].reason
    name: Reason
    type: string
  - JSONPath: .spec.broker
    name: Broker
    type: string
  - JSONPath: .status.subscriberURI
    name: Subscriber_URI
    type: string
  - JSONPath: .metadata.creationTimestamp
    name: Age
    type: date
  group: eventing.knative.dev
  names:
    categories:
    - all
    - knative
    - eventing
    kind: Trigger
    plural: triggers
    singular: trigger
  scope: Namespaced
  validation:
    openAPIV3Schema:
      properties:
        spec:
          properties:
            broker:
              type: string
            filter:
              properties:
                sourceAndType:
                  properties:
                    source:
                      type: string
                    type:
                      type: string
                  type: object
              type: object
            subscriber:
              properties:
                dnsName:
                  minLength: 1
                  type: string
                ref:
                  properties:
                    apiVersion:
                      minLength: 1
                      type: string
                    kind:
                      minLength: 1
                      type: string
                    name:
                      minLength: 1
                      type: string
                  required:
                  - apiVersion
                  - kind
                  - name
                  type: object
                uri:
                  minLength: 1
                  type: string
              type: object
          required:
          - subscriber
  versions:
  - name: v1alpha1
    served: true
    storage: true

---
apiVersion: v1
data:
  default-ch-config: |
    clusterDefault:
      apiVersion: messaging.knative.dev/v1alpha1
      kind: InMemoryChannel
    namespaceDefaults:
      some-namespace:
        apiVersion: messaging.knative.dev/v1alpha1
        kind: InMemoryChannel
kind: ConfigMap
metadata:
  name: default-ch-webhook
  namespace: knative-eventing

---
apiVersion: v1
data:
  default-channel-config: |
    clusterdefault:
      apiversion: eventing.knative.dev/v1alpha1
      kind: ClusterChannelProvisioner
      name: in-memory
    namespacedefaults:
      some-namespace:
        apiversion: eventing.knative.dev/v1alpha1
        kind: ClusterChannelProvisioner
        name: some-other-provisioner
kind: ConfigMap
metadata:
  name: default-channel-webhook
  namespace: knative-eventing

---
apiVersion: v1
kind: Service
metadata:
  annotations:
    prometheus.io/scrape: "true"
  labels:
    app: sources-controller
    eventing.knative.dev/release: "v0.8.0"
  name: sources-controller
  namespace: knative-eventing
spec:
  ports:
  - name: metrics
    port: 9090
    protocol: TCP
    targetPort: 9090
  selector:
    app: sources-controller

---
apiVersion: v1
kind: Service
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    role: eventing-webhook
  name: eventing-webhook
  namespace: knative-eventing
spec:
  ports:
  - port: 443
    targetPort: 8443
  selector:
    role: eventing-webhook

---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: eventing-controller
  namespace: knative-eventing
spec:
  replicas: 1
  selector:
    matchLabels:
      app: eventing-controller
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
      labels:
        app: eventing-controller
        eventing.knative.dev/release: "v0.8.0"
    spec:
      containers:
      - env:
        - name: SYSTEM_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: CONFIG_LOGGING_NAME
          value: config-logging
        - name: CONFIG_OBSERVABILITY_NAME
          value: config-observability
        - name: METRICS_DOMAIN
          value: knative.dev/eventing
        - name: BROKER_INGRESS_IMAGE
          value: gcr.io/knative-releases/github.com/knative/eventing/cmd/broker/ingress@sha256:f029d8d2cb65e31853f8a82394524e45b40958a457450b06f2533e491ffae436
        - name: BROKER_INGRESS_SERVICE_ACCOUNT
          value: eventing-broker-ingress
        - name: BROKER_FILTER_IMAGE
          value: gcr.io/knative-releases/github.com/knative/eventing/cmd/broker/filter@sha256:4d68a946700f184d594de9ebcd9dbbdde7539595fed3e0c3d401d5ee7e2010b6
        - name: BROKER_FILTER_SERVICE_ACCOUNT
          value: eventing-broker-filter
        image: gcr.io/knative-releases/github.com/knative/eventing/cmd/controller@sha256:65d747cf69f3093aec07e4d0a71fab2b16383e990328afb61ff85d62aeb2fa33
        name: eventing-controller
        ports:
        - containerPort: 9090
          name: metrics
        terminationMessagePolicy: FallbackToLogsOnError
        volumeMounts:
        - mountPath: /etc/config-logging
          name: config-logging
      serviceAccountName: eventing-controller
      volumes:
      - configMap:
          name: config-logging
        name: config-logging

---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: sources-controller
  namespace: knative-eventing
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sources-controller
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
      labels:
        app: sources-controller
        eventing.knative.dev/release: "v0.8.0"
    spec:
      containers:
      - env:
        - name: SYSTEM_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: CONFIG_LOGGING_NAME
          value: config-logging
        - name: CONFIG_OBSERVABILITY_NAME
          value: config-observability
        - name: METRICS_DOMAIN
          value: knative.dev/sources
        - name: CRONJOB_RA_IMAGE
          value: gcr.io/knative-releases/github.com/knative/eventing/cmd/cronjob_receive_adapter@sha256:0067924e5089e9fe8d8c788d3030b2361471560734b75ef6baf9eaef8b6deaca
        - name: APISERVER_RA_IMAGE
          value: gcr.io/knative-releases/github.com/knative/eventing/cmd/apiserver_receive_adapter@sha256:73e7a51895a82d23c97e1e0016c0faee7cf7393d2099f862a288acb606258a64
        image: gcr.io/knative-releases/github.com/knative/eventing/cmd/sources_controller@sha256:3115114a63a08fc0a508b13c78e68bb10ce9c1dff6b32c5fec1007a1d130979c
        name: controller
        ports:
        - containerPort: 9090
          name: metrics
        resources:
          requests:
            cpu: 100m
            memory: 100Mi
        volumeMounts:
        - mountPath: /etc/config-logging
          name: config-logging
      serviceAccountName: eventing-source-controller
      volumes:
      - configMap:
          name: config-logging
        name: config-logging

---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: eventing-webhook
  namespace: knative-eventing
spec:
  replicas: 1
  selector:
    matchLabels:
      app: eventing-webhook
      role: eventing-webhook
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
      labels:
        app: eventing-webhook
        role: eventing-webhook
    spec:
      containers:
      - env:
        - name: SYSTEM_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: CONFIG_LOGGING_NAME
          value: config-logging
        - name: WEBHOOK_NAME
          value: eventing-webhook
        - name: REG_DELAY_TIME
          value: "10"
        image: gcr.io/knative-releases/github.com/knative/eventing/cmd/webhook@sha256:188a29908aaf6267324aa796240ce979c09649a7334ff2e847b3e2155427807d
        name: eventing-webhook
        terminationMessagePolicy: FallbackToLogsOnError
        volumeMounts:
        - mountPath: /etc/config-logging
          name: config-logging
      serviceAccountName: eventing-webhook
      volumes:
      - configMap:
          name: config-logging
        name: config-logging

---
apiVersion: v1
data:
  loglevel.controller: info
  loglevel.webhook: info
  zap-logger-config: |
    {
      "level": "info",
      "development": false,
      "outputPaths": ["stdout"],
      "errorOutputPaths": ["stderr"],
      "encoding": "json",
      "encoderConfig": {
        "timeKey": "ts",
        "levelKey": "level",
        "nameKey": "logger",
        "callerKey": "caller",
        "messageKey": "msg",
        "stacktraceKey": "stacktrace",
        "lineEnding": "",
        "levelEncoder": "",
        "timeEncoder": "iso8601",
        "durationEncoder": "",
        "callerEncoder": ""
      }
    }
kind: ConfigMap
metadata:
  name: config-logging
  namespace: knative-eventing

---
apiVersion: v1
data:
  _example: |
    ################################
    #                              #
    #    EXAMPLE CONFIGURATION     #
    #                              #
    ################################

    # This block is not actually functional configuration,
    # but serves to illustrate the available configuration
    # options and document them in a way that is accessible
    # to users that `kubectl edit` this config map.
    #
    # These sample configuration options may be copied out of
    # this example block and unindented to be in the data block
    # to actually change the configuration.

    # logging.enable-var-log-collection defaults to false.
    # A fluentd sidecar will be set up to collect var log if
    # this flag is true.
    logging.enable-var-log-collection: false

    # logging.fluentd-sidecar-image provides the fluentd sidecar image
    # to inject as a sidecar to collect logs from /var/log.
    # Must be presented if logging.enable-var-log-collection is true.
    logging.fluentd-sidecar-image: k8s.gcr.io/fluentd-elasticsearch:v2.0.4

    # logging.fluentd-sidecar-output-config provides the configuration
    # for the fluentd sidecar, which will be placed into a configmap and
    # mounted into the fluentd sidecar image.
    logging.fluentd-sidecar-output-config: |
      # Parse json log before sending to Elastic Search
      <filter **>
        @type parser
        key_name log
        <parse>
          @type multi_format
          <pattern>
            format json
            time_key fluentd-time # fluentd-time is reserved for structured logs
            time_format %Y-%m-%dT%H:%M:%S.%NZ
          </pattern>
          <pattern>
            format none
            message_key log
          </pattern>
        </parse>
      </filter>
      # Send to Elastic Search
      <match **>
        @id elasticsearch
        @type elasticsearch
        @log_level info
        include_tag_key true
        # Elasticsearch service is in monitoring namespace.
        host elasticsearch-logging.knative-monitoring
        port 9200
        logstash_format true
        <buffer>
          @type file
          path /var/log/fluentd-buffers/kubernetes.system.buffer
          flush_mode interval
          retry_type exponential_backoff
          flush_thread_count 2
          flush_interval 5s
          retry_forever
          retry_max_interval 30
          chunk_limit_size 2M
          queue_limit_length 8
          overflow_action block
        </buffer>
      </match>

    # logging.revision-url-template provides a template to use for producing the
    # logging URL that is injected into the status of each Revision.
    # This value is what you might use the the Knative monitoring bundle, and provides
    # access to Kibana after setting up kubectl proxy.
    logging.revision-url-template: |
      http://localhost:8001/api/v1/namespaces/knative-monitoring/services/kibana-logging/proxy/app/kibana#/discover?_a=(query:(match:(kubernetes.labels.knative-dev%2FrevisionUID:(query:'${REVISION_UID}',type:phrase))))

    # If non-empty, this enables queue proxy writing request logs to stdout.
    # The value determines the shape of the request logs and it must be a valid go text/template.
    # It is important to keep this as a single line. Multiple lines are parsed as separate entities
    # by most collection agents and will split the request logs into multiple records.
    #
    # The following fields and functions are available to the template:
    #
    # Request: An http.Request (see https://golang.org/pkg/net/http/#Request)
    # representing an HTTP request received by the server.
    #
    # Response:
    # struct {
    #   Code    int       // HTTP status code (see https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml)
    #   Size    int       // An int representing the size of the response.
    #   Latency float64   // A float64 representing the latency of the response in seconds.
    # }
    #
    # Revision:
    # struct {
    #   Name          string  // Knative revision name
    #   Namespace     string  // Knative revision namespace
    #   Service       string  // Knative service name
    #   Configuration string  // Knative configuration name
    #   PodName       string  // Name of the pod hosting the revision
    #   PodIP         string  // IP of the pod hosting the revision
    # }
    #
    logging.request-log-template: '{"httpRequest": {"requestMethod": "{{.Request.Method}}", "requestUrl": "{{js .Request.RequestURI}}", "requestSize": "{{.Request.ContentLength}}", "status": {{.Response.Code}}, "responseSize": "{{.Response.Size}}", "userAgent": "{{js .Request.UserAgent}}", "remoteIp": "{{js .Request.RemoteAddr}}", "serverIp": "{{.Revision.PodIP}}", "referer": "{{js .Request.Referer}}", "latency": "{{.Response.Latency}}s", "protocol": "{{.Request.Proto}}"}, "traceId": "{{index .Request.Header "X-B3-Traceid"}}"}'

    # metrics.backend-destination field specifies the system metrics destination.
    # It supports either prometheus (the default) or stackdriver.
    # Note: Using stackdriver will incur additional charges
    metrics.backend-destination: prometheus

    # metrics.request-metrics-backend-destination specifies the request metrics
    # destination. If non-empty, it enables queue proxy to send request metrics.
    # Currently supported values: prometheus, stackdriver.
    metrics.request-metrics-backend-destination: prometheus

    # metrics.stackdriver-project-id field specifies the stackdriver project ID. This
    # field is optional. When running on GCE, application default credentials will be
    # used if this field is not provided.
    metrics.stackdriver-project-id: "<your stackdriver project id>"

    # metrics.allow-stackdriver-custom-metrics indicates whether it is allowed to send metrics to
    # Stackdriver using "global" resource type and custom metric type if the
    # metrics are not supported by "knative_revision" resource type. Setting this
    # flag to "true" could cause extra Stackdriver charge.
    # If metrics.backend-destination is not Stackdriver, this is ignored.
    metrics.allow-stackdriver-custom-metrics: "false"
kind: ConfigMap
metadata:
  name: config-observability
  namespace: knative-eventing

---
apiVersion: v1
data:
  _example: |
    ################################
    #                              #
    #    EXAMPLE CONFIGURATION     #
    #                              #
    ################################

    # This block is not actually functional configuration,
    # but serves to illustrate the available configuration
    # options and document them in a way that is accessible
    # to users that `kubectl edit` this config map.
    #
    # These sample configuration options may be copied out of
    # this example block and unindented to be in the data block
    # to actually change the configuration.
    #
    # If true we enable adding spans within our applications.
    enable: "false"

    # URL to zipkin collector where traces are sent.
    zipkin-endpoint: "http://zipkin.istio-system.svc.cluster.local:9411/api/v2/spans"

    # Enable zipkin debug mode. This allows all spans to be sent to the server
    # bypassing sampling.
    debug: "false"

    # Percentage (0-1) of requests to trace
    sample-rate: "0.1"
kind: ConfigMap
metadata:
  name: config-tracing
  namespace: knative-eventing

---
---
# in-memory-channel-crd.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    duck.knative.dev/addressable: "true"
    eventing.knative.dev/release: "v0.8.0"
  name: imc-addressable-resolver
rules:
- apiGroups:
  - messaging.knative.dev
  resources:
  - inmemorychannels
  - inmemorychannels/status
  verbs:
  - get
  - list
  - watch

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    duck.knative.dev/channelable: "true"
    eventing.knative.dev/release: "v0.8.0"
  name: imc-channelable-manipulator
rules:
- apiGroups:
  - messaging.knative.dev
  resources:
  - inmemorychannels
  - inmemorychannels/status
  verbs:
  - create
  - get
  - list
  - watch
  - update
  - patch

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: imc-controller
rules:
- apiGroups:
  - messaging.knative.dev
  resources:
  - inmemorychannels
  - inmemorychannels/status
  verbs:
  - get
  - list
  - watch
  - update
- apiGroups:
  - messaging.knative.dev
  resources:
  - inmemorychannels/finalizers
  verbs:
  - update
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
- apiGroups:
  - ""
  resources:
  - endpoints
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  - deployments/status
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: imc-dispatcher
rules:
- apiGroups:
  - messaging.knative.dev
  resources:
  - inmemorychannels
  - inmemorychannels/status
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - messaging.knative.dev
  resources:
  - inmemorychannels/status
  verbs:
  - update

---
apiVersion: v1
kind: Service
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    messaging.knative.dev/channel: in-memory-channel
    messaging.knative.dev/role: dispatcher
  name: imc-dispatcher
  namespace: knative-eventing
spec:
  ports:
  - port: 80
    protocol: TCP
    targetPort: 8080
  selector:
    messaging.knative.dev/channel: in-memory-channel
    messaging.knative.dev/role: dispatcher

---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: imc-controller
  namespace: knative-eventing
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: imc-dispatcher
  namespace: knative-eventing

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: imc-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: imc-controller
subjects:
- kind: ServiceAccount
  name: imc-controller
  namespace: knative-eventing
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: imc-dispatcher
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: imc-dispatcher
subjects:
- kind: ServiceAccount
  name: imc-dispatcher
  namespace: knative-eventing

---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
    knative.dev/crd-install: "true"
    messaging.knative.dev/subscribable: "true"
  name: inmemorychannels.messaging.knative.dev
spec:
  additionalPrinterColumns:
  - JSONPath: .status.conditions[?(@.type=="Ready")].status
    name: Ready
    type: string
  - JSONPath: .status.conditions[?(@.type=="Ready")].reason
    name: Reason
    type: string
  - JSONPath: .status.address.hostname
    name: Hostname
    type: string
  - JSONPath: .metadata.creationTimestamp
    name: Age
    type: date
  group: messaging.knative.dev
  names:
    categories:
    - all
    - knative
    - messaging
    - channel
    kind: InMemoryChannel
    plural: inmemorychannels
    shortNames:
    - imc
    singular: inmemorychannel
  scope: Namespaced
  validation:
    openAPIV3Schema:
      properties:
        spec:
          properties:
            subscribable:
              properties:
                subscribers:
                  items:
                    properties:
                      ref:
                        properties:
                          apiVersion:
                            type: string
                          kind:
                            type: string
                          name:
                            minLength: 1
                            type: string
                          namespace:
                            minLength: 1
                            type: string
                          uid:
                            minLength: 1
                            type: string
                        required:
                        - namespace
                        - name
                        - uid
                        type: object
                      replyURI:
                        minLength: 1
                        type: string
                      subscriberURI:
                        minLength: 1
                        type: string
                      uid:
                        minLength: 1
                        type: string
                    required:
                    - uid
                  type: array
              type: object
  versions:
  - name: v1alpha1
    served: true
    storage: true

---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: imc-controller
  namespace: knative-eventing
spec:
  replicas: 1
  selector:
    matchLabels:
      messaging.knative.dev/channel: in-memory-channel
      messaging.knative.dev/role: controller
  template:
    metadata:
      labels:
        messaging.knative.dev/channel: in-memory-channel
        messaging.knative.dev/role: controller
    spec:
      containers:
      - env:
        - name: CONFIG_LOGGING_NAME
          value: config-logging
        - name: CONFIG_OBSERVABILITY_NAME
          value: config-observability
        - name: METRICS_DOMAIN
          value: knative.dev/inmemorychannel-controller
        - name: SYSTEM_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        image: gcr.io/knative-releases/github.com/knative/eventing/cmd/in_memory/channel_controller@sha256:0eb085815a87cbae0bafc212d5476283dc174b333c4f9166ce76b111f336f3d4
        name: controller
        volumeMounts:
        - mountPath: /etc/config-logging
          name: config-logging
      serviceAccountName: imc-controller
      volumes:
      - configMap:
          name: config-logging
        name: config-logging

---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: imc-dispatcher
  namespace: knative-eventing
spec:
  replicas: 1
  selector:
    matchLabels:
      messaging.knative.dev/channel: in-memory-channel
      messaging.knative.dev/role: dispatcher
  template:
    metadata:
      labels:
        messaging.knative.dev/channel: in-memory-channel
        messaging.knative.dev/role: dispatcher
    spec:
      containers:
      - env:
        - name: CONFIG_LOGGING_NAME
          value: config-logging
        - name: CONFIG_OBSERVABILITY_NAME
          value: config-observability
        - name: METRICS_DOMAIN
          value: knative.dev/inmemorychannel-dispatcher
        - name: SYSTEM_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        image: gcr.io/knative-releases/github.com/knative/eventing/cmd/in_memory/channel_dispatcher@sha256:21cadd1185756767cb3cda7a03b1ca3e7df806c21d9d5d1a70d029c9e5d64837
        name: dispatcher
        volumeMounts:
        - mountPath: /etc/config-logging
          name: config-logging
      serviceAccountName: imc-dispatcher
      volumes:
      - configMap:
          name: config-logging
        name: config-logging

---
---
# in-memory-channel-provisioner.yaml
apiVersion: eventing.knative.dev/v1alpha1
kind: ClusterChannelProvisioner
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: in-memory
spec: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: in-memory-channel-controller
  namespace: knative-eventing
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: in-memory-channel-controller
rules:
- apiGroups:
  - eventing.knative.dev
  resources:
  - channels
  - channels/status
  - clusterchannelprovisioners
  - clusterchannelprovisioners/status
  verbs:
  - get
  - list
  - watch
  - update
- apiGroups:
  - eventing.knative.dev
  resources:
  - channels/finalizers
  - clusterchannelprovisioners/finalizers
  verbs:
  - update
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - get
  - list
  - watch
  - create
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - update
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
  - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: in-memory-channel-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: in-memory-channel-controller
subjects:
- kind: ServiceAccount
  name: in-memory-channel-controller
  namespace: knative-eventing
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: in-memory-channel-controller
  namespace: knative-eventing
spec:
  replicas: 1
  selector:
    matchLabels:
      clusterChannelProvisioner: in-memory-channel
      role: controller
  template:
    metadata:
      labels:
        clusterChannelProvisioner: in-memory-channel
        role: controller
    spec:
      containers:
      - env:
        - name: SYSTEM_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        image: gcr.io/knative-releases/github.com/knative/eventing/cmd/in_memory/controller@sha256:bafbbcc72baca0de04c2a0a9dc993909affef3d825ffedd4797bd86d71f1dc54
        name: controller
      serviceAccountName: in-memory-channel-controller
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: in-memory-channel-dispatcher
  namespace: knative-eventing
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: in-memory-channel-dispatcher
rules:
- apiGroups:
  - eventing.knative.dev
  resources:
  - channels
  - channels/status
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: in-memory-channel-dispatcher
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: in-memory-channel-dispatcher
subjects:
- kind: ServiceAccount
  name: in-memory-channel-dispatcher
  namespace: knative-eventing
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: in-memory-channel-dispatcher
  namespace: knative-eventing
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: in-memory-channel-dispatcher
  namespace: knative-eventing
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: in-memory-channel-dispatcher
subjects:
- kind: ServiceAccount
  name: in-memory-channel-dispatcher
  namespace: knative-eventing
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    eventing.knative.dev/release: "v0.8.0"
  name: in-memory-channel-dispatcher
  namespace: knative-eventing
spec:
  replicas: 1
  selector:
    matchLabels:
      clusterChannelProvisioner: in-memory-channel
      role: dispatcher
  template:
    metadata:
      labels:
        clusterChannelProvisioner: in-memory-channel
        role: dispatcher
    spec:
      containers:
      - env:
        - name: SYSTEM_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        image: gcr.io/knative-releases/github.com/knative/eventing/cmd/in_memory/dispatcher@sha256:75101533d5467d13700f6c1a5fd0daac7af31dc7ca5aa44abe14ffedf2f71c7e
        name: dispatcher
      serviceAccountName: in-memory-channel-dispatcher

---
