apiVersion: tekton.dev/v1alpha1
kind: Task
metadata:
  name: kaniko
  namespace: coalesce
spec:
  inputs:
    params:
    - name: DOCKERFILE
      description: Path to the Dockerfile to build.
      default: ./Dockerfile
    - name: CONTEXT
      description: The build context used by Kaniko.
      default: ./
    resources:
    - name: source
      type: git

  outputs:
    resources:
    - name: image
      type: image

  steps:
  - name: build-and-push
    workingdir: /workspace/source
    image: gcr.io/kaniko-project/executor:v0.9.0
    # specifying DOCKER_CONFIG is required to allow kaniko to detect docker credential
    # https://github.com/tektoncd/pipeline/pull/706
    env:
    - name: DOCKER_CONFIG
      value: /builder/home/<USER>
    command:
    - /kaniko/executor
    - --dockerfile=$(inputs.params.DOCKERFILE)
    - --context=/workspace/source/$(inputs.params.CONTEXT) # The user does not need to care the workspace and the source.
    - --destination=$(outputs.resources.image.url)
#    - --insecure-registry=docker.internal.coalescelab.com:5000
