apiVersion: v1
kind: Namespace
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: knative-monitoring

---
apiVersion: v1
kind: Service
metadata:
  labels:
    addonmanager.kubernetes.io/mode: Reconcile
    app: elasticsearch-logging
    kubernetes.io/cluster-service: "true"
    kubernetes.io/name: Elasticsearch
  name: elasticsearch-logging
  namespace: knative-monitoring
spec:
  ports:
  - port: 9200
    protocol: TCP
    targetPort: db
  selector:
    app: elasticsearch-logging
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    addonmanager.kubernetes.io/mode: Reconcile
    app: elasticsearch-logging
    kubernetes.io/cluster-service: "true"
  name: elasticsearch-logging
  namespace: knative-monitoring
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    addonmanager.kubernetes.io/mode: Reconcile
    app: elasticsearch-logging
    kubernetes.io/cluster-service: "true"
  name: elasticsearch-logging
rules:
- apiGroups:
  - ""
  resources:
  - services
  - namespaces
  - endpoints
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    addonmanager.kubernetes.io/mode: Reconcile
    app: elasticsearch-logging
    kubernetes.io/cluster-service: "true"
  name: elasticsearch-logging
  namespace: knative-monitoring
roleRef:
  apiGroup: ""
  kind: ClusterRole
  name: elasticsearch-logging
subjects:
- apiGroup: ""
  kind: ServiceAccount
  name: elasticsearch-logging
  namespace: knative-monitoring
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    addonmanager.kubernetes.io/mode: Reconcile
    app: elasticsearch-logging
    kubernetes.io/cluster-service: "true"
    version: v5.6.4
  name: elasticsearch-logging
  namespace: knative-monitoring
spec:
  replicas: 2
  selector:
    matchLabels:
      app: elasticsearch-logging
      version: v5.6.4
  serviceName: elasticsearch-logging
  template:
    metadata:
      labels:
        app: elasticsearch-logging
        kubernetes.io/cluster-service: "true"
        version: v5.6.4
    spec:
      containers:
      - env:
        - name: NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        image: k8s.gcr.io/elasticsearch:v5.6.4
        name: elasticsearch-logging
        ports:
        - containerPort: 9200
          name: db
          protocol: TCP
        - containerPort: 9300
          name: transport
          protocol: TCP
        resources:
          limits:
            cpu: 1000m
          requests:
            cpu: 100m
        volumeMounts:
        - mountPath: /data
          name: elasticsearch-logging
      initContainers:
      - command:
        - /sbin/sysctl
        - -w
        - vm.max_map_count=262144
        image: alpine:3.6
        name: elasticsearch-logging-init
        securityContext:
          privileged: true
      serviceAccountName: elasticsearch-logging
      volumes:
      - emptyDir: {}
        name: elasticsearch-logging

---
apiVersion: v1
kind: Service
metadata:
  labels:
    addonmanager.kubernetes.io/mode: Reconcile
    app: kibana-logging
    kubernetes.io/cluster-service: "true"
    kubernetes.io/name: Kibana
  name: kibana-logging
  namespace: knative-monitoring
spec:
  ports:
  - port: 5601
    protocol: TCP
    targetPort: ui
  selector:
    app: kibana-logging
  type: NodePort
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    addonmanager.kubernetes.io/mode: Reconcile
    app: kibana-logging
    kubernetes.io/cluster-service: "true"
  name: kibana-logging
  namespace: knative-monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kibana-logging
  template:
    metadata:
      labels:
        app: kibana-logging
    spec:
      containers:
      - env:
        - name: ELASTICSEARCH_URL
          value: http://elasticsearch-logging:9200
        - name: SERVER_BASEPATH
          value: /api/v1/namespaces/knative-monitoring/services/kibana-logging/proxy
        - name: XPACK_MONITORING_ENABLED
          value: "false"
        - name: XPACK_SECURITY_ENABLED
          value: "false"
        image: docker.elastic.co/kibana/kibana:5.6.4
        name: kibana-logging
        ports:
        - containerPort: 5601
          name: ui
          protocol: TCP
        resources:
          limits:
            cpu: 1000m
          requests:
            cpu: 100m

---
apiVersion: v1
data:
  100.system.conf: |-
    <system>
      root_dir /tmp/fluentd-buffers/
    </system>
  200.containers.input.conf: |-
    # Capture logs from container's stdout/stderr -> Docker -> .log in JSON format
    <source>
      @id containers-stdout-stderr
      @type tail
      path /var/log/containers/*user-container-*.log,/var/log/containers/*build-step-*.log,/var/log/containers/controller-*controller-*.log,/var/log/containers/webhook-*webhook-*.log,/var/log/containers/*autoscaler-*autoscaler-*.log,/var/log/containers/*queue-proxy-*.log,/var/log/containers/activator-*activator-*.log
      pos_file /var/log/containers-stdout-stderr.pos
      time_format %Y-%m-%dT%H:%M:%S.%NZ
      tag raw.kubernetes.*
      format json
      read_from_head true
    </source>
    # Capture logs from Knative containers' /var/log
    <source>
      @id containers-var-log
      @type tail
      # **/*/**/* allows path expansion to go through one symlink (the one created by the init container)
      path /var/lib/kubelet/pods/*/volumes/kubernetes.io~empty-dir/knative-internal/**/*/**/*
      path_key stream
      pos_file /var/log/containers-var-log.pos
      tag raw.kubernetes.*
      message_key log
      read_from_head true
      <parse>
        @type multi_format
        <pattern>
          format json
          time_key fluentd-time # fluentd-time is reserved for structured logs
          time_format %Y-%m-%dT%H:%M:%S.%NZ
        </pattern>
        <pattern>
          format none
          message_key log
        </pattern>
      </parse>
    </source>
    # Combine multi line logs which form an exception stack trace into a single log entry
    <match raw.kubernetes.**>
      @id raw.kubernetes
      @type detect_exceptions
      remove_tag_prefix raw
      message log
      stream stream
      multiline_flush_interval 5
      max_bytes 500000
      max_lines 1000
    </match>
    # Make stream path correct from the container's point of view
    <filter kubernetes.var.lib.kubelet.pods.*.volumes.kubernetes.io~empty-dir.knative-internal.*.**>
      @type record_transformer
      enable_ruby true
      <record>
        stream /var/log/${record["stream"].scan(/\/knative-internal\/[^\/]+\/(.*)/).last.last}
      </record>
    </filter>
    # Add Kubernetes metadata to logs from /var/log/containers
    <filter kubernetes.var.log.containers.**>
      @type kubernetes_metadata
    </filter>
    # Add Kubernetes metadata to logs from /var/lib/kubelet/pods/*/volumes/kubernetes.io~empty-dir/knative-internal/**/*/**/*
    <filter kubernetes.var.lib.kubelet.pods.**>
      @type kubernetes_metadata
      tag_to_kubernetes_name_regexp (?<docker_id>[a-z0-9]{8}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{12})\.volumes.kubernetes\.io~empty-dir\.knative-internal\.(?<namespace>[^_]+)_(?<pod_name>[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*)_(?<container_name>user-container)\..*?$
    </filter>
  300.forward.input.conf: |-
    # Takes the messages sent over TCP, e.g. request logs from Istio
    <source>
      @type forward
      port 24224
    </source>
  900.output.conf: |-
    # Send to Elastic Search
    <match **>
      @id elasticsearch
      @type elasticsearch
      @log_level info
      host elasticsearch-logging
      port 9200
      logstash_format true
      <buffer>
        @type file
        path /var/log/fluentd-buffers/kubernetes.system.buffer
        flush_mode interval
        retry_type exponential_backoff
        flush_thread_count 2
        flush_interval 5s
        retry_forever
        retry_max_interval 30
        chunk_limit_size 2M
        queue_limit_length 8
        overflow_action block
      </buffer>
    </match>
kind: ConfigMap
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: fluentd-ds-config
  namespace: knative-monitoring

---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app: fluentd-ds
    serving.knative.dev/release: "v0.8.0"
  name: fluentd-ds
  namespace: knative-monitoring
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: fluentd-ds
    serving.knative.dev/release: "v0.8.0"
  name: fluentd-ds
rules:
- apiGroups:
  - ""
  resources:
  - namespaces
  - pods
  verbs:
  - get
  - watch
  - list
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app: fluentd-ds
    serving.knative.dev/release: "v0.8.0"
  name: fluentd-ds
roleRef:
  apiGroup: ""
  kind: ClusterRole
  name: fluentd-ds
subjects:
- apiGroup: ""
  kind: ServiceAccount
  name: fluentd-ds
  namespace: knative-monitoring
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: fluentd-ds
    serving.knative.dev/release: "v0.8.0"
  name: fluentd-ds
  namespace: knative-monitoring
spec:
  ports:
  - name: fluentd-tcp
    port: 24224
    protocol: TCP
    targetPort: 24224
  - name: fluentd-udp
    port: 24224
    protocol: UDP
    targetPort: 24224
  selector:
    app: fluentd-ds
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  labels:
    app: fluentd-ds
    serving.knative.dev/release: "v0.8.0"
    version: v2.0.4
  name: fluentd-ds
  namespace: knative-monitoring
spec:
  selector:
    matchLabels:
      app: fluentd-ds
      version: v2.0.4
  template:
    metadata:
      annotations:
        scheduler.alpha.kubernetes.io/critical-pod: ""
      labels:
        app: fluentd-ds
        serving.knative.dev/release: "v0.8.0"
        version: v2.0.4
    spec:
      containers:
      - env:
        - name: FLUENTD_ARGS
          value: --no-supervisor -q
        image: k8s.gcr.io/fluentd-elasticsearch:v2.0.4
        name: fluentd-ds
        resources:
          limits:
            memory: 500Mi
          requests:
            cpu: 100m
            memory: 200Mi
        volumeMounts:
        - mountPath: /var/log/containers
          name: varlogcontainers
          readOnly: true
        - mountPath: /var/log/pods
          name: varlogpods
          readOnly: true
        - mountPath: /var/lib/docker/containers
          name: varlibdockercontainers
          readOnly: true
        - mountPath: /var/lib/kubelet/pods
          name: varlibkubeletpods
          readOnly: true
        - mountPath: /host/lib
          name: libsystemddir
          readOnly: true
        - mountPath: /etc/fluent/config.d
          name: config-volume
      nodeSelector:
        beta.kubernetes.io/fluentd-ds-ready: "true"
      serviceAccountName: fluentd-ds
      terminationGracePeriodSeconds: 30
      volumes:
      - hostPath:
          path: /var/log/containers
        name: varlogcontainers
      - hostPath:
          path: /var/log/pods
        name: varlogpods
      - hostPath:
          path: /var/lib/docker/containers
        name: varlibdockercontainers
      - hostPath:
          path: /var/lib/kubelet/pods
        name: varlibkubeletpods
      - hostPath:
          path: /usr/lib64
        name: libsystemddir
      - configMap:
          name: fluentd-ds-config
        name: config-volume

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kube-state-metrics
  namespace: knative-monitoring
---
apiVersion: rbac.authorization.k8s.io/v1beta1
kind: Role
metadata:
  name: kube-state-metrics-resizer
  namespace: knative-monitoring
rules:
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - get
- apiGroups:
  - extensions
  resourceNames:
  - kube-state-metrics
  resources:
  - deployments
  verbs:
  - get
  - update
---
apiVersion: rbac.authorization.k8s.io/v1beta1
kind: RoleBinding
metadata:
  name: kube-state-metrics
  namespace: knative-monitoring
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: kube-state-metrics-resizer
subjects:
- kind: ServiceAccount
  name: kube-state-metrics
  namespace: knative-monitoring
---
apiVersion: rbac.authorization.k8s.io/v1beta1
kind: ClusterRole
metadata:
  name: kube-state-metrics
  namespace: knative-monitoring
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  - secrets
  - nodes
  - pods
  - services
  - resourcequotas
  - replicationcontrollers
  - limitranges
  - persistentvolumeclaims
  - persistentvolumes
  - namespaces
  - endpoints
  verbs:
  - list
  - watch
- apiGroups:
  - extensions
  resources:
  - daemonsets
  - deployments
  - replicasets
  verbs:
  - list
  - watch
- apiGroups:
  - apps
  resources:
  - statefulsets
  verbs:
  - list
  - watch
- apiGroups:
  - batch
  resources:
  - cronjobs
  - jobs
  verbs:
  - list
  - watch
- apiGroups:
  - autoscaling
  resources:
  - horizontalpodautoscalers
  verbs:
  - list
  - watch
- apiGroups:
  - authentication.k8s.io
  resources:
  - tokenreviews
  verbs:
  - create
- apiGroups:
  - authorization.k8s.io
  resources:
  - subjectaccessreviews
  verbs:
  - create
---
apiVersion: rbac.authorization.k8s.io/v1beta1
kind: ClusterRoleBinding
metadata:
  name: kube-state-metrics
  namespace: knative-monitoring
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kube-state-metrics
subjects:
- kind: ServiceAccount
  name: kube-state-metrics
  namespace: knative-monitoring
---
apiVersion: extensions/v1beta1
kind: Deployment
metadata:
  name: kube-state-metrics
  namespace: knative-monitoring
spec:
  replicas: 1
  template:
    metadata:
      labels:
        app: kube-state-metrics
    spec:
      containers:
      - args:
        - --secure-listen-address=:8443
        - --upstream=http://127.0.0.1:8081/
        image: quay.io/coreos/kube-rbac-proxy:v0.3.0
        name: kube-rbac-proxy-main
        ports:
        - containerPort: 8443
          name: https-main
        resources:
          limits:
            cpu: 20m
            memory: 40Mi
          requests:
            cpu: 10m
            memory: 20Mi
      - args:
        - --secure-listen-address=:9443
        - --upstream=http://127.0.0.1:8082/
        image: quay.io/coreos/kube-rbac-proxy:v0.3.0
        name: kube-rbac-proxy-self
        ports:
        - containerPort: 9443
          name: https-self
        resources:
          limits:
            cpu: 20m
            memory: 40Mi
          requests:
            cpu: 10m
            memory: 20Mi
      - args:
        - --host=127.0.0.1
        - --port=8081
        - --telemetry-host=127.0.0.1
        - --telemetry-port=8082
        image: quay.io/coreos/kube-state-metrics:v1.3.0
        name: kube-state-metrics
      - command:
        - /pod_nanny
        - --container=kube-state-metrics
        - --cpu=100m
        - --extra-cpu=1m
        - --memory=100Mi
        - --extra-memory=2Mi
        - --threshold=5
        - --deployment=kube-state-metrics
        env:
        - name: MY_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: MY_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        image: k8s.gcr.io/addon-resizer:1.7
        name: addon-resizer
        resources:
          limits:
            cpu: 100m
            memory: 30Mi
          requests:
            cpu: 100m
            memory: 30Mi
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
      serviceAccountName: kube-state-metrics
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: kube-state-metrics
  name: kube-state-metrics
  namespace: knative-monitoring
spec:
  clusterIP: None
  ports:
  - name: https-main
    port: 8443
    protocol: TCP
    targetPort: https-main
  - name: https-self
    port: 9443
    protocol: TCP
    targetPort: https-self
  selector:
    app: kube-state-metrics

---
apiVersion: v1
data:
  kubernetes-deployment-dashboard.json: |
    {
      "__inputs": [
        {
          "description": "",
          "label": "prometheus",
          "name": "prometheus",
          "pluginId": "prometheus",
          "pluginName": "Prometheus",
          "type": "datasource"
        }
      ],
      "annotations": {
        "list": []
      },
      "editable": false,
      "gnetId": null,
      "graphTooltip": 0,
      "id": null,
      "links": [],
      "rows": [
        {
          "collapse": false,
          "editable": false,
          "height": "200px",
          "panels": [
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 8,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "cores",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 4,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": true
              },
              "targets": [
                {
                  "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"$deployment_namespace\",pod_name=~\"$deployment_name.*\"}[3m]))",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "title": "CPU",
              "type": "singlestat",
              "valueFontSize": "110%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 9,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "GB",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "80%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 4,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": true
              },
              "targets": [
                {
                  "expr": "sum(container_memory_usage_bytes{namespace=\"$deployment_namespace\",pod_name=~\"$deployment_name.*\"}) / 1024^3",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "title": "Memory",
              "type": "singlestat",
              "valueFontSize": "110%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "Bps",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": false
              },
              "id": 7,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 4,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": true
              },
              "targets": [
                {
                  "expr": "sum(rate(container_network_transmit_bytes_total{namespace=\"$deployment_namespace\",pod_name=~\"$deployment_name.*\"}[3m])) + sum(rate(container_network_receive_bytes_total{namespace=\"$deployment_namespace\",pod_name=~\"$deployment_name.*\"}[3m]))",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "title": "Network",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            }
          ],
          "showTitle": false,
          "title": "Dashboard Row",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "100px",
          "panels": [
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": false
              },
              "id": 5,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "max(kube_deployment_spec_replicas{deployment=\"$deployment_name\",namespace=\"$deployment_namespace\"}) without (instance, pod)",
                  "intervalFactor": 2,
                  "metric": "kube_deployment_spec_replicas",
                  "refId": "A",
                  "step": 600
                }
              ],
              "title": "Desired Replicas",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 6,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "min(kube_deployment_status_replicas_available{deployment=\"$deployment_name\",namespace=\"$deployment_namespace\"}) without (instance, pod)",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "title": "Available Replicas",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 3,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "max(kube_deployment_status_observed_generation{deployment=\"$deployment_name\",namespace=\"$deployment_namespace\"}) without (instance, pod)",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "title": "Observed Generation",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 2,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "max(kube_deployment_metadata_generation{deployment=\"$deployment_name\",namespace=\"$deployment_namespace\"}) without (instance, pod)",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "title": "Metadata Generation",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            }
          ],
          "showTitle": false,
          "title": "Dashboard Row",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "350px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 1,
              "isNew": true,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "span": 12,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "max(kube_deployment_status_replicas{deployment=\"$deployment_name\",namespace=\"$deployment_namespace\"}) without (instance, pod)",
                  "intervalFactor": 2,
                  "legendFormat": "current replicas",
                  "refId": "A",
                  "step": 30
                },
                {
                  "expr": "min(kube_deployment_status_replicas_available{deployment=\"$deployment_name\",namespace=\"$deployment_namespace\"}) without (instance, pod)",
                  "intervalFactor": 2,
                  "legendFormat": "available",
                  "refId": "B",
                  "step": 30
                },
                {
                  "expr": "max(kube_deployment_status_replicas_unavailable{deployment=\"$deployment_name\",namespace=\"$deployment_namespace\"}) without (instance, pod)",
                  "intervalFactor": 2,
                  "legendFormat": "unavailable",
                  "refId": "C",
                  "step": 30
                },
                {
                  "expr": "min(kube_deployment_status_replicas_updated{deployment=\"$deployment_name\",namespace=\"$deployment_namespace\"}) without (instance, pod)",
                  "intervalFactor": 2,
                  "legendFormat": "updated",
                  "refId": "D",
                  "step": 30
                },
                {
                  "expr": "max(kube_deployment_spec_replicas{deployment=\"$deployment_name\",namespace=\"$deployment_namespace\"}) without (instance, pod)",
                  "intervalFactor": 2,
                  "legendFormat": "desired",
                  "refId": "E",
                  "step": 30
                }
              ],
              "title": "Replicas",
              "tooltip": {
                "msResolution": true,
                "shared": true,
                "sort": 0,
                "value_type": "cumulative"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "none",
                  "label": "",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "short",
                  "label": "",
                  "logBase": 1,
                  "show": false
                }
              ]
            }
          ],
          "showTitle": false,
          "title": "Dashboard Row",
          "titleSize": "h6"
        }
      ],
      "schemaVersion": 14,
      "sharedCrosshair": false,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": ".*",
            "current": {},
            "datasource": "prometheus",
            "hide": 0,
            "includeAll": false,
            "label": "Namespace",
            "multi": false,
            "name": "deployment_namespace",
            "options": [],
            "query": "label_values(kube_deployment_metadata_generation, namespace)",
            "refresh": 1,
            "regex": "",
            "sort": 0,
            "tagValuesQuery": null,
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {},
            "datasource": "prometheus",
            "hide": 0,
            "includeAll": false,
            "label": "Deployment",
            "multi": false,
            "name": "deployment_name",
            "options": [],
            "query": "label_values(kube_deployment_metadata_generation{namespace=\"$deployment_namespace\"}, deployment)",
            "refresh": 1,
            "regex": "",
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "deployment",
            "type": "query",
            "useTags": false
          }
        ]
      },
      "time": {
        "from": "now-6h",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "browser",
      "title": "Deployment",
      "version": 1
    }
kind: ConfigMap
metadata:
  name: grafana-dashboard-definition-kubernetes-deployment
  namespace: knative-monitoring

---
apiVersion: v1
data:
  kubernetes-capacity-planning-dashboard.json: |
    {
      "__inputs": [
        {
          "description": "",
          "label": "prometheus",
          "name": "prometheus",
          "pluginId": "prometheus",
          "pluginName": "Prometheus",
          "type": "datasource"
        }
      ],
      "annotations": {
        "list": []
      },
      "editable": false,
      "gnetId": null,
      "graphTooltip": 0,
      "id": null,
      "links": [],
      "refresh": false,
      "rows": [
        {
          "collapse": false,
          "editable": false,
          "height": "250px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 3,
              "isNew": false,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "span": 6,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sum(rate(node_cpu{mode=\"idle\"}[2m])) * 100",
                  "hide": false,
                  "intervalFactor": 10,
                  "legendFormat": "",
                  "refId": "A",
                  "step": 50
                }
              ],
              "title": "Idle CPU",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "cumulative"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "percent",
                  "label": "cpu usage",
                  "logBase": 1,
                  "min": 0,
                  "show": true
                },
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                }
              ]
            },
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 9,
              "isNew": false,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "span": 6,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sum(node_load1)",
                  "intervalFactor": 4,
                  "legendFormat": "load 1m",
                  "refId": "A",
                  "step": 20,
                  "target": ""
                },
                {
                  "expr": "sum(node_load5)",
                  "intervalFactor": 4,
                  "legendFormat": "load 5m",
                  "refId": "B",
                  "step": 20,
                  "target": ""
                },
                {
                  "expr": "sum(node_load15)",
                  "intervalFactor": 4,
                  "legendFormat": "load 15m",
                  "refId": "C",
                  "step": 20,
                  "target": ""
                }
              ],
              "title": "System Load",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "cumulative"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "percentunit",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                }
              ]
            }
          ],
          "showTitle": false,
          "title": "New Row",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "250px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 4,
              "isNew": false,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [
                {
                  "alias": "node_memory_SwapFree{instance=\"172.17.0.1:9100\",job=\"prometheus\"}",
                  "yaxis": 2
                }
              ],
              "spaceLength": 10,
              "span": 9,
              "stack": true,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sum(node_memory_MemTotal) - sum(node_memory_MemFree) - sum(node_memory_Buffers) - sum(node_memory_Cached)",
                  "intervalFactor": 2,
                  "legendFormat": "memory usage",
                  "metric": "memo",
                  "refId": "A",
                  "step": 10,
                  "target": ""
                },
                {
                  "expr": "sum(node_memory_Buffers)",
                  "interval": "",
                  "intervalFactor": 2,
                  "legendFormat": "memory buffers",
                  "metric": "memo",
                  "refId": "B",
                  "step": 10,
                  "target": ""
                },
                {
                  "expr": "sum(node_memory_Cached)",
                  "interval": "",
                  "intervalFactor": 2,
                  "legendFormat": "memory cached",
                  "metric": "memo",
                  "refId": "C",
                  "step": 10,
                  "target": ""
                },
                {
                  "expr": "sum(node_memory_MemFree)",
                  "interval": "",
                  "intervalFactor": 2,
                  "legendFormat": "memory free",
                  "metric": "memo",
                  "refId": "D",
                  "step": 10,
                  "target": ""
                }
              ],
              "title": "Memory Usage",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "bytes",
                  "logBase": 1,
                  "min": "0",
                  "show": true
                },
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                }
              ]
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percent",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 5,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "((sum(node_memory_MemTotal) - sum(node_memory_MemFree) - sum(node_memory_Buffers) - sum(node_memory_Cached)) / sum(node_memory_MemTotal)) * 100",
                  "intervalFactor": 2,
                  "metric": "",
                  "refId": "A",
                  "step": 60,
                  "target": ""
                }
              ],
              "thresholds": "80, 90",
              "title": "Memory Usage",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            }
          ],
          "showTitle": false,
          "title": "New Row",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "246px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 6,
              "isNew": false,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [
                {
                  "alias": "read",
                  "yaxis": 1
                },
                {
                  "alias": "{instance=\"172.17.0.1:9100\"}",
                  "yaxis": 2
                },
                {
                  "alias": "io time",
                  "yaxis": 2
                }
              ],
              "spaceLength": 10,
              "span": 9,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sum(rate(node_disk_bytes_read[5m]))",
                  "hide": false,
                  "intervalFactor": 4,
                  "legendFormat": "read",
                  "refId": "A",
                  "step": 20,
                  "target": ""
                },
                {
                  "expr": "sum(rate(node_disk_bytes_written[5m]))",
                  "intervalFactor": 4,
                  "legendFormat": "written",
                  "refId": "B",
                  "step": 20
                },
                {
                  "expr": "sum(rate(node_disk_io_time_ms[5m]))",
                  "intervalFactor": 4,
                  "legendFormat": "io time",
                  "refId": "C",
                  "step": 20
                }
              ],
              "title": "Disk I/O",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "cumulative"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "bytes",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "ms",
                  "logBase": 1,
                  "show": true
                }
              ]
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percentunit",
              "gauge": {
                "maxValue": 1,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 12,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "(sum(node_filesystem_size{device!=\"rootfs\"}) - sum(node_filesystem_free{device!=\"rootfs\"})) / sum(node_filesystem_size{device!=\"rootfs\"})",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 60,
                  "target": ""
                }
              ],
              "thresholds": "0.75, 0.9",
              "title": "Disk Space Usage",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "current"
            }
          ],
          "showTitle": false,
          "title": "New Row",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "250px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 8,
              "isNew": false,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [
                {
                  "alias": "transmitted",
                  "yaxis": 2
                }
              ],
              "spaceLength": 10,
              "span": 6,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sum(rate(node_network_receive_bytes{device!~\"lo\"}[5m]))",
                  "hide": false,
                  "intervalFactor": 2,
                  "legendFormat": "",
                  "refId": "A",
                  "step": 10,
                  "target": ""
                }
              ],
              "title": "Network Received",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "cumulative"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "bytes",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "bytes",
                  "logBase": 1,
                  "show": true
                }
              ]
            },
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 10,
              "isNew": false,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [
                {
                  "alias": "transmitted",
                  "yaxis": 2
                }
              ],
              "spaceLength": 10,
              "span": 6,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sum(rate(node_network_transmit_bytes{device!~\"lo\"}[5m]))",
                  "hide": false,
                  "intervalFactor": 2,
                  "legendFormat": "",
                  "refId": "B",
                  "step": 10,
                  "target": ""
                }
              ],
              "title": "Network Transmitted",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "cumulative"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "bytes",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "bytes",
                  "logBase": 1,
                  "show": true
                }
              ]
            }
          ],
          "showTitle": false,
          "title": "New Row",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "276px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 11,
              "isNew": true,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 11,
              "span": 9,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sum(kube_pod_info)",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "Current number of Pods",
                  "refId": "A",
                  "step": 10
                },
                {
                  "expr": "sum(kube_node_status_capacity_pods)",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "Maximum capacity of pods",
                  "refId": "B",
                  "step": 10
                }
              ],
              "title": "Cluster Pod Utilization",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                }
              ]
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percent",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 7,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "100 - (sum(kube_node_status_capacity_pods) - sum(kube_pod_info)) / sum(kube_node_status_capacity_pods) * 100",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "",
                  "refId": "A",
                  "step": 60,
                  "target": ""
                }
              ],
              "thresholds": "80, 90",
              "title": "Pod Utilization",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "current"
            }
          ],
          "showTitle": false,
          "title": "New Row",
          "titleSize": "h6"
        }
      ],
      "schemaVersion": 14,
      "sharedCrosshair": false,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-1h",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "browser",
      "title": "Kubernetes Capacity Planning",
      "version": 4
    }
kind: ConfigMap
metadata:
  name: grafana-dashboard-definition-kubernetes-capacity-planning
  namespace: knative-monitoring

---
apiVersion: v1
data:
  kubernetes-cluster-health-dashboard.json: |
    {
      "__inputs": [
        {
          "description": "",
          "label": "prometheus",
          "name": "prometheus",
          "pluginId": "prometheus",
          "pluginName": "Prometheus",
          "type": "datasource"
        }
      ],
      "annotations": {
        "list": []
      },
      "editable": false,
      "gnetId": null,
      "graphTooltip": 0,
      "id": null,
      "links": [],
      "refresh": "10s",
      "rows": [
        {
          "collapse": false,
          "editable": false,
          "height": "254px",
          "panels": [
            {
              "colorBackground": false,
              "colorValue": true,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 1,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "sum(up{job=~\"apiserver|kube-scheduler|kube-controller-manager\"} == 0)",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "",
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "1, 3",
              "title": "Control Plane Components Down",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "Everything UP and healthy",
                  "value": "null"
                },
                {
                  "op": "=",
                  "text": "",
                  "value": ""
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": true,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 2,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "sum(ALERTS{alertstate=\"firing\",alertname!=\"DeadMansSwitch\"})",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "",
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "1, 3",
              "title": "Alerts Firing",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "0",
                  "value": "null"
                }
              ],
              "valueName": "current"
            },
            {
              "colorBackground": false,
              "colorValue": true,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 3,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "sum(ALERTS{alertstate=\"pending\",alertname!=\"DeadMansSwitch\"})",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "",
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "3, 5",
              "title": "Alerts Pending",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "0",
                  "value": "null"
                }
              ],
              "valueName": "current"
            },
            {
              "colorBackground": false,
              "colorValue": true,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 4,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "count(increase(kube_pod_container_status_restarts[1h]) > 5)",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "",
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "1, 3",
              "title": "Crashlooping Pods",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "0",
                  "value": "null"
                }
              ],
              "valueName": "current"
            }
          ],
          "showTitle": false,
          "title": "Row",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "250px",
          "panels": [
            {
              "colorBackground": false,
              "colorValue": true,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 5,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "sum(kube_node_status_condition{condition=\"Ready\",status!=\"true\"})",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "",
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "1, 3",
              "title": "Node Not Ready",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "current"
            },
            {
              "colorBackground": false,
              "colorValue": true,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 6,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "sum(kube_node_status_condition{condition=\"DiskPressure\",status=\"true\"})",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "",
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "1, 3",
              "title": "Node Disk Pressure",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "current"
            },
            {
              "colorBackground": false,
              "colorValue": true,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 7,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "sum(kube_node_status_condition{condition=\"MemoryPressure\",status=\"true\"})",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "",
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "1, 3",
              "title": "Node Memory Pressure",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "current"
            },
            {
              "colorBackground": false,
              "colorValue": true,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 8,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "sum(kube_node_spec_unschedulable)",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "",
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "1, 3",
              "title": "Nodes Unschedulable",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "current"
            }
          ],
          "showTitle": false,
          "title": "Row",
          "titleSize": "h6"
        }
      ],
      "schemaVersion": 14,
      "sharedCrosshair": false,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-6h",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "browser",
      "title": "Kubernetes Cluster Health",
      "version": 9
    }
kind: ConfigMap
metadata:
  name: grafana-dashboard-definition-kubernetes-cluster-health
  namespace: knative-monitoring

---
apiVersion: v1
data:
  kubernetes-cluster-status-dashboard.json: |
    {
      "__inputs": [
        {
          "description": "",
          "label": "prometheus",
          "name": "prometheus",
          "pluginId": "prometheus",
          "pluginName": "Prometheus",
          "type": "datasource"
        }
      ],
      "annotations": {
        "list": []
      },
      "editable": false,
      "graphTooltip": 0,
      "hideControls": false,
      "links": [],
      "rows": [
        {
          "collapse": false,
          "editable": false,
          "height": "129px",
          "panels": [
            {
              "colorBackground": false,
              "colorValue": true,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 5,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 6,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "sum(up{job=~\"apiserver|kube-scheduler|kube-controller-manager\"} == 0)",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "1, 3",
              "title": "Control Plane UP",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "UP",
                  "value": "null"
                }
              ],
              "valueName": "total"
            },
            {
              "colorBackground": false,
              "colorValue": true,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 6,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 6,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "sum(ALERTS{alertstate=\"firing\",alertname!=\"DeadMansSwitch\"})",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "3, 5",
              "title": "Alerts Firing",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "0",
                  "value": "null"
                }
              ],
              "valueName": "current"
            }
          ],
          "showTitle": true,
          "title": "Cluster Health",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "168px",
          "panels": [
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percent",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 1,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "(sum(up{job=\"apiserver\"} == 1) / count(up{job=\"apiserver\"})) * 100",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "50, 80",
              "title": "API Servers UP",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "current"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percent",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 2,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "(sum(up{job=\"kube-controller-manager\"} == 1) / count(up{job=\"kube-controller-manager\"})) * 100",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "50, 80",
              "title": "Controller Managers UP",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "current"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percent",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 3,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "(sum(up{job=\"kube-scheduler\"} == 1) / count(up{job=\"kube-scheduler\"})) * 100",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "50, 80",
              "title": "Schedulers UP",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "current"
            },
            {
              "colorBackground": false,
              "colorValue": true,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 4,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "count(increase(kube_pod_container_status_restarts{namespace=~\"kube-system|tectonic-system\"}[1h]) > 5)",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "1, 3",
              "title": "Crashlooping Control Plane Pods",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "0",
                  "value": "null"
                }
              ],
              "valueName": "current"
            }
          ],
          "showTitle": true,
          "title": "Control Plane Status",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "158px",
          "panels": [
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percent",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 8,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "sum(100 - (avg by (instance) (rate(node_cpu{job=\"node-exporter\",mode=\"idle\"}[5m])) * 100)) / count(node_cpu{job=\"node-exporter\",mode=\"idle\"})",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "80, 90",
              "title": "CPU Utilization",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percent",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 7,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "((sum(node_memory_MemTotal) - sum(node_memory_MemFree) - sum(node_memory_Buffers) - sum(node_memory_Cached)) / sum(node_memory_MemTotal)) * 100",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "80, 90",
              "title": "Memory Utilization",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percent",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 9,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "(sum(node_filesystem_size{device!=\"rootfs\"}) - sum(node_filesystem_free{device!=\"rootfs\"})) / sum(node_filesystem_size{device!=\"rootfs\"})",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "80, 90",
              "title": "Filesystem Utilization",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percent",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 10,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "100 - (sum(kube_node_status_capacity_pods) - sum(kube_pod_info)) / sum(kube_node_status_capacity_pods) * 100",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "80, 90",
              "title": "Pod Utilization",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            }
          ],
          "showTitle": true,
          "title": "Capacity Planning",
          "titleSize": "h6"
        }
      ],
      "schemaVersion": 14,
      "sharedCrosshair": false,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-6h",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "browser",
      "title": "Kubernetes Cluster Status",
      "version": 3
    }
kind: ConfigMap
metadata:
  name: grafana-dashboard-definition-kubernetes-cluster-status
  namespace: knative-monitoring

---
apiVersion: v1
data:
  kubernetes-control-plane-status-dashboard.json: |
    {
      "__inputs": [
        {
          "description": "",
          "label": "prometheus",
          "name": "prometheus",
          "pluginId": "prometheus",
          "pluginName": "Prometheus",
          "type": "datasource"
        }
      ],
      "annotations": {
        "list": []
      },
      "editable": false,
      "graphTooltip": 0,
      "hideControls": false,
      "links": [],
      "rows": [
        {
          "collapse": false,
          "editable": false,
          "height": "250px",
          "panels": [
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percent",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 1,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "(sum(up{job=\"apiserver\"} == 1) / sum(up{job=\"apiserver\"})) * 100",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "50, 80",
              "title": "API Servers UP",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percent",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 2,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "(sum(up{job=\"kube-controller-manager\"} == 1) / sum(up{job=\"kube-controller-manager\"})) * 100",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "50, 80",
              "title": "Controller Managers UP",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percent",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 3,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "(sum(up{job=\"kube-scheduler\"} == 1) / sum(up{job=\"kube-scheduler\"})) * 100",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "50, 80",
              "title": "Schedulers UP",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percent",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 4,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "max(sum by(instance) (rate(apiserver_request_count{code=~\"5..\"}[5m])) / sum by(instance) (rate(apiserver_request_count[5m]))) * 100",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "",
                  "refId": "A",
                  "step": 600
                }
              ],
              "thresholds": "5, 10",
              "title": "API Server Request Error Rate",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "0",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            }
          ],
          "showTitle": false,
          "title": "Dashboard Row",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "250px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 7,
              "isNew": false,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 1,
              "links": [],
              "nullPointMode": "null",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "span": 12,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sum by(verb) (rate(apiserver_latency_seconds:quantile[5m]) >= 0)",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "",
                  "refId": "A",
                  "step": 30
                }
              ],
              "title": "API Server Request Latency",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                }
              ]
            }
          ],
          "showTitle": false,
          "title": "Dashboard Row",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "250px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 5,
              "isNew": false,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 1,
              "links": [],
              "nullPointMode": "null",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "span": 6,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "cluster:scheduler_e2e_scheduling_latency_seconds:quantile",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 60
                }
              ],
              "title": "End to End Scheduling Latency",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "dtdurations",
                  "logBase": 1,
                  "show": true
                }
              ]
            },
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 6,
              "isNew": false,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 1,
              "links": [],
              "nullPointMode": "null",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "span": 6,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sum by(instance) (rate(apiserver_request_count{code!~\"2..\"}[5m]))",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "Error Rate",
                  "refId": "A",
                  "step": 60
                },
                {
                  "expr": "sum by(instance) (rate(apiserver_request_count[5m]))",
                  "format": "time_series",
                  "intervalFactor": 2,
                  "legendFormat": "Request Rate",
                  "refId": "B",
                  "step": 60
                }
              ],
              "title": "API Server Request Rates",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                }
              ]
            }
          ],
          "showTitle": false,
          "title": "Dashboard Row",
          "titleSize": "h6"
        }
      ],
      "schemaVersion": 14,
      "sharedCrosshair": false,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-6h",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "browser",
      "title": "Kubernetes Control Plane Status",
      "version": 3
    }
kind: ConfigMap
metadata:
  name: grafana-dashboard-definition-kubernetes-control-plane-status
  namespace: knative-monitoring

---
apiVersion: v1
data:
  kubernetes-resource-requests-dashboard.json: |
    {
      "__inputs": [
        {
          "description": "",
          "label": "prometheus",
          "name": "prometheus",
          "pluginId": "prometheus",
          "pluginName": "Prometheus",
          "type": "datasource"
        }
      ],
      "annotations": {
        "list": []
      },
      "editable": false,
      "graphTooltip": 0,
      "hideControls": false,
      "links": [],
      "refresh": false,
      "rows": [
        {
          "collapse": false,
          "editable": false,
          "height": "300px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "description": "This represents the total [CPU resource requests](https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/#meaning-of-cpu) in the cluster.\nFor comparison the total [allocatable CPU cores](https://github.com/kubernetes/community/blob/master/contributors/design-proposals/node-allocatable.md) is also shown.",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 1,
              "isNew": false,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 1,
              "links": [],
              "nullPointMode": "null",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "span": 9,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "min(sum(kube_node_status_allocatable_cpu_cores) by (instance))",
                  "hide": false,
                  "intervalFactor": 2,
                  "legendFormat": "Allocatable CPU Cores",
                  "refId": "A",
                  "step": 20
                },
                {
                  "expr": "max(sum(kube_pod_container_resource_requests_cpu_cores) by (instance))",
                  "hide": false,
                  "intervalFactor": 2,
                  "legendFormat": "Requested CPU Cores",
                  "refId": "B",
                  "step": 20
                }
              ],
              "title": "CPU Cores",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "short",
                  "label": "CPU Cores",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                }
              ]
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percent",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 2,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": true
              },
              "targets": [
                {
                  "expr": "max(sum(kube_pod_container_resource_requests_cpu_cores) by (instance)) / min(sum(kube_node_status_allocatable_cpu_cores) by (instance)) * 100",
                  "intervalFactor": 2,
                  "legendFormat": "",
                  "refId": "A",
                  "step": 240
                }
              ],
              "thresholds": "80, 90",
              "title": "CPU Cores",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "110%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            }
          ],
          "showTitle": false,
          "title": "CPU Cores",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "300px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "description": "This represents the total [memory resource requests](https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/#meaning-of-memory) in the cluster.\nFor comparison the total [allocatable memory](https://github.com/kubernetes/community/blob/master/contributors/design-proposals/node-allocatable.md) is also shown.",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 3,
              "isNew": false,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 1,
              "links": [],
              "nullPointMode": "null",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "span": 9,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "min(sum(kube_node_status_allocatable_memory_bytes) by (instance))",
                  "hide": false,
                  "intervalFactor": 2,
                  "legendFormat": "Allocatable Memory",
                  "refId": "A",
                  "step": 20
                },
                {
                  "expr": "max(sum(kube_pod_container_resource_requests_memory_bytes) by (instance))",
                  "hide": false,
                  "intervalFactor": 2,
                  "legendFormat": "Requested Memory",
                  "refId": "B",
                  "step": 20
                }
              ],
              "title": "Memory",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "bytes",
                  "label": "Memory",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                }
              ]
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percent",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 4,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": true
              },
              "targets": [
                {
                  "expr": "max(sum(kube_pod_container_resource_requests_memory_bytes) by (instance)) / min(sum(kube_node_status_allocatable_memory_bytes) by (instance)) * 100",
                  "intervalFactor": 2,
                  "legendFormat": "",
                  "refId": "A",
                  "step": 240
                }
              ],
              "thresholds": "80, 90",
              "title": "Memory",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "110%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            }
          ],
          "showTitle": false,
          "title": "Memory",
          "titleSize": "h6"
        }
      ],
      "schemaVersion": 14,
      "sharedCrosshair": false,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-3h",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "browser",
      "title": "Kubernetes Resource Requests",
      "version": 2
    }
kind: ConfigMap
metadata:
  name: grafana-dashboard-definition-kubernetes-resource-requests
  namespace: knative-monitoring

---
apiVersion: v1
data:
  kubernetes-nodes-dashboard.json: |
    {
      "__inputs": [
        {
          "description": "",
          "label": "prometheus",
          "name": "prometheus",
          "pluginId": "prometheus",
          "pluginName": "Prometheus",
          "type": "datasource"
        }
      ],
      "annotations": {
        "list": []
      },
      "description": "Dashboard to get an overview of one server",
      "editable": false,
      "gnetId": 22,
      "graphTooltip": 0,
      "hideControls": false,
      "links": [],
      "refresh": false,
      "rows": [
        {
          "collapse": false,
          "editable": false,
          "height": "250px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 3,
              "isNew": false,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "span": 6,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "100 - (avg by (cpu) (irate(node_cpu{mode=\"idle\", instance=\"$server\"}[5m])) * 100)",
                  "hide": false,
                  "intervalFactor": 10,
                  "legendFormat": "{{cpu}}",
                  "refId": "A",
                  "step": 50
                }
              ],
              "title": "Idle CPU",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "cumulative"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "percent",
                  "label": "cpu usage",
                  "logBase": 1,
                  "max": 100,
                  "min": 0,
                  "show": true
                },
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                }
              ]
            },
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 9,
              "isNew": false,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "span": 6,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "node_load1{instance=\"$server\"}",
                  "intervalFactor": 4,
                  "legendFormat": "load 1m",
                  "refId": "A",
                  "step": 20,
                  "target": ""
                },
                {
                  "expr": "node_load5{instance=\"$server\"}",
                  "intervalFactor": 4,
                  "legendFormat": "load 5m",
                  "refId": "B",
                  "step": 20,
                  "target": ""
                },
                {
                  "expr": "node_load15{instance=\"$server\"}",
                  "intervalFactor": 4,
                  "legendFormat": "load 15m",
                  "refId": "C",
                  "step": 20,
                  "target": ""
                }
              ],
              "title": "System Load",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "cumulative"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "percentunit",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                }
              ]
            }
          ],
          "showTitle": false,
          "title": "New Row",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "250px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 4,
              "isNew": false,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [
                {
                  "alias": "node_memory_SwapFree{instance=\"172.17.0.1:9100\",job=\"prometheus\"}",
                  "yaxis": 2
                }
              ],
              "spaceLength": 10,
              "span": 9,
              "stack": true,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "node_memory_MemTotal{instance=\"$server\"} - node_memory_MemFree{instance=\"$server\"} - node_memory_Buffers{instance=\"$server\"} - node_memory_Cached{instance=\"$server\"}",
                  "hide": false,
                  "interval": "",
                  "intervalFactor": 2,
                  "legendFormat": "memory used",
                  "metric": "",
                  "refId": "C",
                  "step": 10
                },
                {
                  "expr": "node_memory_Buffers{instance=\"$server\"}",
                  "interval": "",
                  "intervalFactor": 2,
                  "legendFormat": "memory buffers",
                  "metric": "",
                  "refId": "E",
                  "step": 10
                },
                {
                  "expr": "node_memory_Cached{instance=\"$server\"}",
                  "intervalFactor": 2,
                  "legendFormat": "memory cached",
                  "metric": "",
                  "refId": "F",
                  "step": 10
                },
                {
                  "expr": "node_memory_MemFree{instance=\"$server\"}",
                  "intervalFactor": 2,
                  "legendFormat": "memory free",
                  "metric": "",
                  "refId": "D",
                  "step": 10
                }
              ],
              "title": "Memory Usage",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "bytes",
                  "logBase": 1,
                  "min": "0",
                  "show": true
                },
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                }
              ]
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percent",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 5,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "((node_memory_MemTotal{instance=\"$server\"} - node_memory_MemFree{instance=\"$server\"}  - node_memory_Buffers{instance=\"$server\"} - node_memory_Cached{instance=\"$server\"}) / node_memory_MemTotal{instance=\"$server\"}) * 100",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 60,
                  "target": ""
                }
              ],
              "thresholds": "80, 90",
              "title": "Memory Usage",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            }
          ],
          "showTitle": false,
          "title": "New Row",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "250px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 6,
              "isNew": true,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [
                {
                  "alias": "read",
                  "yaxis": 1
                },
                {
                  "alias": "{instance=\"172.17.0.1:9100\"}",
                  "yaxis": 2
                },
                {
                  "alias": "io time",
                  "yaxis": 2
                }
              ],
              "spaceLength": 10,
              "span": 9,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sum by (instance) (rate(node_disk_bytes_read{instance=\"$server\"}[2m]))",
                  "hide": false,
                  "intervalFactor": 4,
                  "legendFormat": "read",
                  "refId": "A",
                  "step": 20,
                  "target": ""
                },
                {
                  "expr": "sum by (instance) (rate(node_disk_bytes_written{instance=\"$server\"}[2m]))",
                  "intervalFactor": 4,
                  "legendFormat": "written",
                  "refId": "B",
                  "step": 20
                },
                {
                  "expr": "sum by (instance) (rate(node_disk_io_time_ms{instance=\"$server\"}[2m]))",
                  "intervalFactor": 4,
                  "legendFormat": "io time",
                  "refId": "C",
                  "step": 20
                }
              ],
              "title": "Disk I/O",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "cumulative"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "bytes",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "ms",
                  "logBase": 1,
                  "show": true
                }
              ]
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "percentunit",
              "gauge": {
                "maxValue": 1,
                "minValue": 0,
                "show": true,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "hideTimeOverride": false,
              "id": 7,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "(sum(node_filesystem_size{device!=\"rootfs\",instance=\"$server\"}) - sum(node_filesystem_free{device!=\"rootfs\",instance=\"$server\"})) / sum(node_filesystem_size{device!=\"rootfs\",instance=\"$server\"})",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 60,
                  "target": ""
                }
              ],
              "thresholds": "0.75, 0.9",
              "title": "Disk Space Usage",
              "transparent": false,
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "current"
            }
          ],
          "showTitle": false,
          "title": "New Row",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "250px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 8,
              "isNew": false,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [
                {
                  "alias": "transmitted",
                  "yaxis": 2
                }
              ],
              "spaceLength": 10,
              "span": 6,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "rate(node_network_receive_bytes{instance=\"$server\",device!~\"lo\"}[5m])",
                  "hide": false,
                  "intervalFactor": 2,
                  "legendFormat": "{{device}}",
                  "refId": "A",
                  "step": 10,
                  "target": ""
                }
              ],
              "title": "Network Received",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "cumulative"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "bytes",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "bytes",
                  "logBase": 1,
                  "show": true
                }
              ]
            },
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 10,
              "isNew": false,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [
                {
                  "alias": "transmitted",
                  "yaxis": 2
                }
              ],
              "spaceLength": 10,
              "span": 6,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "rate(node_network_transmit_bytes{instance=\"$server\",device!~\"lo\"}[5m])",
                  "hide": false,
                  "intervalFactor": 2,
                  "legendFormat": "{{device}}",
                  "refId": "B",
                  "step": 10,
                  "target": ""
                }
              ],
              "title": "Network Transmitted",
              "tooltip": {
                "msResolution": false,
                "shared": true,
                "sort": 0,
                "value_type": "cumulative"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "bytes",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "bytes",
                  "logBase": 1,
                  "show": true
                }
              ]
            }
          ],
          "showTitle": false,
          "title": "New Row",
          "titleSize": "h6"
        }
      ],
      "schemaVersion": 14,
      "sharedCrosshair": false,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": null,
            "current": {},
            "datasource": "prometheus",
            "hide": 0,
            "includeAll": false,
            "label": null,
            "multi": false,
            "name": "server",
            "options": [],
            "query": "label_values(node_boot_time, instance)",
            "refresh": 1,
            "regex": "",
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          }
        ]
      },
      "time": {
        "from": "now-1h",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "browser",
      "title": "Nodes",
      "version": 2
    }
kind: ConfigMap
metadata:
  name: grafana-dashboard-definition-kubernetes-nodes
  namespace: knative-monitoring

---
apiVersion: v1
data:
  kubernetes-pods-dashboard.json: |
    {
      "__inputs": [
        {
          "description": "",
          "label": "prometheus",
          "name": "prometheus",
          "pluginId": "prometheus",
          "pluginName": "Prometheus",
          "type": "datasource"
        }
      ],
      "annotations": {
        "list": []
      },
      "editable": false,
      "graphTooltip": 1,
      "hideControls": false,
      "links": [],
      "refresh": false,
      "rows": [
        {
          "collapse": false,
          "editable": false,
          "height": "250px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 1,
              "isNew": false,
              "legend": {
                "alignAsTable": true,
                "avg": true,
                "current": true,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": true,
                "show": true,
                "total": false,
                "values": true
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "span": 12,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sum by(container_name) (container_memory_usage_bytes{pod_name=\"$pod\", container_name=~\"$container\", container_name!=\"POD\"})",
                  "interval": "10s",
                  "intervalFactor": 1,
                  "legendFormat": "Current: {{ container_name }}",
                  "metric": "container_memory_usage_bytes",
                  "refId": "A",
                  "step": 15
                },
                {
                  "expr": "kube_pod_container_resource_requests_memory_bytes{pod=\"$pod\", container=~\"$container\"}",
                  "interval": "10s",
                  "intervalFactor": 2,
                  "legendFormat": "Requested: {{ container }}",
                  "metric": "kube_pod_container_resource_requests_memory_bytes",
                  "refId": "B",
                  "step": 20
                },
                {
                  "expr": "kube_pod_container_resource_limits_memory_bytes{pod=\"$pod\", container=~\"$container\"}",
                  "interval": "10s",
                  "intervalFactor": 2,
                  "legendFormat": "Limit: {{ container }}",
                  "metric": "kube_pod_container_resource_limits_memory_bytes",
                  "refId": "C",
                  "step": 20
                }
              ],
              "title": "Memory Usage",
              "tooltip": {
                "msResolution": true,
                "shared": true,
                "sort": 0,
                "value_type": "cumulative"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "bytes",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                }
              ]
            }
          ],
          "showTitle": false,
          "title": "Row",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "250px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 2,
              "isNew": false,
              "legend": {
                "alignAsTable": true,
                "avg": true,
                "current": true,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": true,
                "show": true,
                "total": false,
                "values": true
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "span": 12,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sum by (container_name)(rate(container_cpu_usage_seconds_total{image!=\"\",container_name!=\"POD\",pod_name=\"$pod\"}[1m]))",
                  "intervalFactor": 2,
                  "legendFormat": "{{ container_name }}",
                  "refId": "A",
                  "step": 30
                },
                {
                  "expr": "kube_pod_container_resource_requests_cpu_cores{pod=\"$pod\", container=~\"$container\"}",
                  "interval": "10s",
                  "intervalFactor": 2,
                  "legendFormat": "Requested: {{ container }}",
                  "metric": "kube_pod_container_resource_requests_cpu_cores",
                  "refId": "B",
                  "step": 20
                },
                {
                  "expr": "kube_pod_container_resource_limits_cpu_cores{pod=\"$pod\", container=~\"$container\"}",
                  "interval": "10s",
                  "intervalFactor": 2,
                  "legendFormat": "Limit: {{ container }}",
                  "metric": "kube_pod_container_resource_limits_memory_bytes",
                  "refId": "C",
                  "step": 20
                }
              ],
              "title": "CPU Usage",
              "tooltip": {
                "msResolution": true,
                "shared": true,
                "sort": 0,
                "value_type": "cumulative"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                }
              ]
            }
          ],
          "showTitle": false,
          "title": "Row",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "250px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 3,
              "isNew": false,
              "legend": {
                "alignAsTable": true,
                "avg": true,
                "current": true,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": true,
                "show": true,
                "total": false,
                "values": true
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "span": 12,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sort_desc(sum by (pod_name) (rate(container_network_receive_bytes_total{pod_name=\"$pod\"}[1m])))",
                  "intervalFactor": 2,
                  "legendFormat": "{{ pod_name }}",
                  "refId": "A",
                  "step": 30
                }
              ],
              "title": "Network I/O",
              "tooltip": {
                "msResolution": true,
                "shared": true,
                "sort": 0,
                "value_type": "cumulative"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "bytes",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "short",
                  "logBase": 1,
                  "show": true
                }
              ]
            }
          ],
          "showTitle": false,
          "title": "New Row",
          "titleSize": "h6"
        }
      ],
      "schemaVersion": 14,
      "sharedCrosshair": false,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": ".*",
            "current": {},
            "datasource": "prometheus",
            "hide": 0,
            "includeAll": true,
            "label": "Namespace",
            "multi": false,
            "name": "namespace",
            "options": [],
            "query": "label_values(kube_pod_info, namespace)",
            "refresh": 1,
            "regex": "",
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {},
            "datasource": "prometheus",
            "hide": 0,
            "includeAll": false,
            "label": "Pod",
            "multi": false,
            "name": "pod",
            "options": [],
            "query": "label_values(kube_pod_info{namespace=~\"$namespace\"}, pod)",
            "refresh": 1,
            "regex": "",
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": ".*",
            "current": {},
            "datasource": "prometheus",
            "hide": 0,
            "includeAll": true,
            "label": "Container",
            "multi": false,
            "name": "container",
            "options": [],
            "query": "label_values(kube_pod_container_info{namespace=\"$namespace\", pod=\"$pod\"}, container)",
            "refresh": 1,
            "regex": "",
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          }
        ]
      },
      "time": {
        "from": "now-6h",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "browser",
      "title": "Pods",
      "version": 1
    }
kind: ConfigMap
metadata:
  name: grafana-dashboard-definition-kubernetes-pods
  namespace: knative-monitoring

---
apiVersion: v1
data:
  kubernetes-statefulset-dashboard.json: |
    {
      "__inputs": [
        {
          "description": "",
          "label": "prometheus",
          "name": "prometheus",
          "pluginId": "prometheus",
          "pluginName": "Prometheus",
          "type": "datasource"
        }
      ],
      "annotations": {
        "list": []
      },
      "editable": false,
      "graphTooltip": 1,
      "hideControls": false,
      "links": [],
      "rows": [
        {
          "collapse": false,
          "editable": false,
          "height": "200px",
          "panels": [
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 8,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "cores",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 4,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": true
              },
              "targets": [
                {
                  "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"$statefulset_namespace\",pod_name=~\"$statefulset_name.*\"}[3m]))",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "title": "CPU",
              "type": "singlestat",
              "valueFontSize": "110%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 9,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "GB",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "80%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 4,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": true
              },
              "targets": [
                {
                  "expr": "sum(container_memory_usage_bytes{namespace=\"$statefulset_namespace\",pod_name=~\"$statefulset_name.*\"}) / 1024^3",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "title": "Memory",
              "type": "singlestat",
              "valueFontSize": "110%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "Bps",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": false
              },
              "id": 7,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 4,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": true
              },
              "targets": [
                {
                  "expr": "sum(rate(container_network_transmit_bytes_total{namespace=\"$statefulset_namespace\",pod_name=~\"$statefulset_name.*\"}[3m])) + sum(rate(container_network_receive_bytes_total{namespace=\"$statefulset_namespace\",pod_name=~\"$statefulset_name.*\"}[3m]))",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "title": "Network",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            }
          ],
          "showTitle": false,
          "title": "Dashboard Row",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "100px",
          "panels": [
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": false
              },
              "id": 5,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "max(kube_statefulset_replicas{statefulset=\"$statefulset_name\",namespace=\"$statefulset_namespace\"}) without (instance, pod)",
                  "intervalFactor": 2,
                  "metric": "kube_statefulset_replicas",
                  "refId": "A",
                  "step": 600
                }
              ],
              "title": "Desired Replicas",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 6,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "min(kube_statefulset_status_replicas{statefulset=\"$statefulset_name\",namespace=\"$statefulset_namespace\"}) without (instance, pod)",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "title": "Available Replicas",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 3,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "max(kube_statefulset_status_observed_generation{statefulset=\"$statefulset_name\",namespace=\"$statefulset_namespace\"}) without (instance, pod)",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "title": "Observed Generation",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            },
            {
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "datasource": "prometheus",
              "editable": false,
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "id": 2,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "span": 3,
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false
              },
              "targets": [
                {
                  "expr": "max(kube_statefulset_metadata_generation{statefulset=\"$statefulset_name\",namespace=\"$statefulset_namespace\"}) without (instance, pod)",
                  "intervalFactor": 2,
                  "refId": "A",
                  "step": 600
                }
              ],
              "title": "Metadata Generation",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "avg"
            }
          ],
          "showTitle": false,
          "title": "Dashboard Row",
          "titleSize": "h6"
        },
        {
          "collapse": false,
          "editable": false,
          "height": "350px",
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "prometheus",
              "editable": false,
              "error": false,
              "fill": 1,
              "grid": {
                "threshold1Color": "rgba(216, 200, 27, 0.27)",
                "threshold2Color": "rgba(234, 112, 112, 0.22)"
              },
              "id": 1,
              "isNew": true,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "hideEmpty": false,
                "hideZero": false,
                "max": false,
                "min": false,
                "rightSide": false,
                "show": true,
                "total": false
              },
              "lines": true,
              "linewidth": 2,
              "links": [],
              "nullPointMode": "connected",
              "percentage": false,
              "pointradius": 5,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "span": 12,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "min(kube_statefulset_status_replicas{statefulset=\"$statefulset_name\",namespace=\"$statefulset_namespace\"}) without (instance, pod)",
                  "intervalFactor": 2,
                  "legendFormat": "available",
                  "refId": "B",
                  "step": 30
                },
                {
                  "expr": "max(kube_statefulset_replicas{statefulset=\"$statefulset_name\",namespace=\"$statefulset_namespace\"}) without (instance, pod)",
                  "intervalFactor": 2,
                  "legendFormat": "desired",
                  "refId": "E",
                  "step": 30
                }
              ],
              "title": "Replicas",
              "tooltip": {
                "msResolution": true,
                "shared": true,
                "sort": 0,
                "value_type": "cumulative"
              },
              "type": "graph",
              "xaxis": {
                "mode": "time",
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "none",
                  "label": "",
                  "logBase": 1,
                  "show": true
                },
                {
                  "format": "short",
                  "label": "",
                  "logBase": 1,
                  "show": false
                }
              ]
            }
          ],
          "showTitle": false,
          "title": "Dashboard Row",
          "titleSize": "h6"
        }
      ],
      "schemaVersion": 14,
      "sharedCrosshair": false,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": ".*",
            "current": {},
            "datasource": "prometheus",
            "hide": 0,
            "includeAll": false,
            "label": "Namespace",
            "multi": false,
            "name": "statefulset_namespace",
            "options": [],
            "query": "label_values(kube_statefulset_metadata_generation, namespace)",
            "refresh": 1,
            "regex": "",
            "sort": 0,
            "tagValuesQuery": null,
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {},
            "datasource": "prometheus",
            "hide": 0,
            "includeAll": false,
            "label": "StatefulSet",
            "multi": false,
            "name": "statefulset_name",
            "options": [],
            "query": "label_values(kube_statefulset_metadata_generation{namespace=\"$statefulset_namespace\"}, statefulset)",
            "refresh": 1,
            "regex": "",
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "statefulset",
            "type": "query",
            "useTags": false
          }
        ]
      },
      "time": {
        "from": "now-6h",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "browser",
      "title": "StatefulSet",
      "version": 1
    }
kind: ConfigMap
metadata:
  name: grafana-dashboard-definition-kubernetes-statefulset
  namespace: knative-monitoring

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: node-exporter
  namespace: knative-monitoring
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: node-exporter
  namespace: knative-monitoring
rules:
- apiGroups:
  - authentication.k8s.io
  resources:
  - tokenreviews
  verbs:
  - create
- apiGroups:
  - authorization.k8s.io
  resources:
  - subjectaccessreviews
  verbs:
  - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: node-exporter
  namespace: knative-monitoring
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: node-exporter
subjects:
- kind: ServiceAccount
  name: node-exporter
  namespace: knative-monitoring
---
apiVersion: extensions/v1beta1
kind: DaemonSet
metadata:
  name: node-exporter
  namespace: knative-monitoring
spec:
  template:
    metadata:
      labels:
        app: node-exporter
      name: node-exporter
      namespace: knative-monitoring
    spec:
      containers:
      - args:
        - --web.listen-address=127.0.0.1:9101
        - --path.procfs=/host/proc
        - --path.sysfs=/host/sys
        image: quay.io/prometheus/node-exporter:v0.15.2
        name: node-exporter
        resources:
          limits:
            cpu: 200m
            memory: 50Mi
          requests:
            cpu: 100m
            memory: 30Mi
        volumeMounts:
        - mountPath: /host/proc
          name: proc
          readOnly: true
        - mountPath: /host/sys
          name: sys
          readOnly: true
      - args:
        - --secure-listen-address=:9100
        - --upstream=http://127.0.0.1:9101/
        image: quay.io/coreos/kube-rbac-proxy:v0.3.0
        name: kube-rbac-proxy
        ports:
        - containerPort: 9100
          hostPort: 9100
          name: https
        resources:
          limits:
            cpu: 20m
            memory: 40Mi
          requests:
            cpu: 10m
            memory: 20Mi
      hostNetwork: true
      hostPID: true
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
      serviceAccountName: node-exporter
      tolerations:
      - effect: NoSchedule
        operator: Exists
      volumes:
      - hostPath:
          path: /proc
        name: proc
      - hostPath:
          path: /sys
        name: sys
  updateStrategy:
    rollingUpdate:
      maxUnavailable: 1
    type: RollingUpdate
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: node-exporter
  name: node-exporter
  namespace: knative-monitoring
spec:
  clusterIP: None
  ports:
  - name: https
    port: 9100
    protocol: TCP
  selector:
    app: node-exporter
  type: ClusterIP

---
apiVersion: v1
data:
  knative-control-plane-efficiency-dashboard.json: |
    {
      "__inputs": [
        {
          "description": "",
          "label": "prometheus",
          "name": "prometheus",
          "pluginId": "prometheus",
          "pluginName": "Prometheus",
          "type": "datasource"
        }
      ],
      "annotations": {
        "list": []
      },
      "description": "Knative Serving - Control Plane Efficiency",
      "editable": false,
      "gnetId": null,
      "graphTooltip": 0,
      "id": null,
      "links": [],
      "panels": [
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "prometheus",
          "decimals": 2,
          "fill": 1,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 0
          },
          "id": 2,
          "legend": {
            "alignAsTable": true,
            "avg": true,
            "current": true,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "percentage": false,
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"knative-serving\"}[1m]))",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "knative-serving",
              "refId": "A"
            },
            {
              "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"knative-build\"}[1m]))",
              "format": "time_series",
              "instant": false,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "knative-build",
              "refId": "C"
            },
            {
              "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"istio-system\"}[1m]))",
              "format": "time_series",
              "instant": false,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "istio-system",
              "refId": "D"
            },
            {
              "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"kube-system\"}[1m]))",
              "format": "time_series",
              "instant": false,
              "intervalFactor": 1,
              "legendFormat": "kube-system",
              "refId": "F"
            },
            {
              "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"kube-public\"}[1m]))",
              "format": "time_series",
              "instant": false,
              "intervalFactor": 1,
              "legendFormat": "kube-public",
              "refId": "E"
            },
            {
              "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"knative-monitoring\"}[1m]))",
              "format": "time_series",
              "instant": false,
              "intervalFactor": 1,
              "legendFormat": "knative-monitoring",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeShift": null,
          "title": "Namespace CPU Usage",
          "tooltip": {
            "shared": true,
            "sort": 2,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ]
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "prometheus",
          "decimals": null,
          "fill": 1,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 0
          },
          "id": 3,
          "legend": {
            "alignAsTable": true,
            "avg": true,
            "current": true,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "percentage": false,
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "sum(container_memory_usage_bytes{namespace=\"knative-serving\"})",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "knative-serving",
              "refId": "A"
            },
            {
              "expr": "sum(container_memory_usage_bytes{namespace=\"knative-build\"})",
              "format": "time_series",
              "instant": false,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "knative-build",
              "refId": "C"
            },
            {
              "expr": "sum(container_memory_usage_bytes{namespace=\"istio-system\"})",
              "format": "time_series",
              "instant": false,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "istio-system",
              "refId": "D"
            },
            {
              "expr": "sum(container_memory_usage_bytes{namespace=\"kube-system\"})",
              "format": "time_series",
              "instant": false,
              "intervalFactor": 1,
              "legendFormat": "kube-system",
              "refId": "F"
            },
            {
              "expr": "sum(container_memory_usage_bytes{namespace=\"kube-public\"})",
              "format": "time_series",
              "instant": false,
              "intervalFactor": 1,
              "legendFormat": "kube-public",
              "refId": "E"
            },
            {
              "expr": "sum(container_memory_usage_bytes{namespace=\"knative-monitoring\"})",
              "format": "time_series",
              "instant": false,
              "intervalFactor": 1,
              "legendFormat": "knative-monitoring",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeShift": null,
          "title": "Namespace Memory Usage",
          "tooltip": {
            "shared": true,
            "sort": 2,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "decbytes",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": false
            }
          ]
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "prometheus",
          "decimals": 2,
          "fill": 1,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 9
          },
          "id": 4,
          "legend": {
            "alignAsTable": true,
            "avg": true,
            "current": true,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "percentage": false,
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "sum(rate(container_cpu_usage_seconds_total{namespace!~\"knative-serving|knative-monitoring|knative-build|istio-system|kube-system|kube-public|^$\"}[1m]))",
              "format": "time_series",
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "Data plane",
              "refId": "A"
            },
            {
              "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=~\"knative-serving|knative-monitoring|knative-build|istio-system|kube-system|kube-public\"}[1m]))",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "Control plane",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeShift": null,
          "title": "Control Plane vs Data Plane CPU Usage",
          "tooltip": {
            "shared": true,
            "sort": 2,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "decimals": null,
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": false
            }
          ]
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "prometheus",
          "fill": 1,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 9
          },
          "id": 5,
          "legend": {
            "alignAsTable": true,
            "avg": true,
            "current": true,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "percentage": false,
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "sum(container_memory_usage_bytes{namespace!~\"knative-serving|knative-monitoring|knative-build|istio-system|kube-system|kube-public|^$\"})",
              "format": "time_series",
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "Data plane",
              "refId": "A"
            },
            {
              "expr": "sum(container_memory_usage_bytes{namespace=~\"knative-serving|knative-monitoring|knative-build|istio-system|kube-system|kube-public\"})",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "Control plane",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeShift": null,
          "title": "Control Plane vs Data Plane Memory Usage",
          "tooltip": {
            "shared": true,
            "sort": 2,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "decbytes",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ]
        }
      ],
      "refresh": "5s",
      "schemaVersion": 16,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-15m",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "",
      "title": "Knative Serving - Control Plane Efficiency",
      "uid": "1oI1URnik",
      "version": 2
    }
kind: ConfigMap
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: grafana-dashboard-definition-knative-efficiency
  namespace: knative-monitoring

---
apiVersion: v1
data:
  knative-reconciler-dashboard.json: |
    {
      "__inputs": [
        {
          "description": "",
          "label": "prometheus",
          "name": "prometheus",
          "pluginId": "prometheus",
          "pluginName": "Prometheus",
          "type": "datasource"
        }
      ],
      "annotations": {
        "list": []
      },
      "description": "Knative Serving - Reconciler",
      "editable": false,
      "gnetId": null,
      "graphTooltip": 0,
      "id": null,
      "links": [],
      "panels": [
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "id": 7,
          "panels": [],
          "title": "Aggregate",
          "type": "row"
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "prometheus",
          "fill": 1,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 1
          },
          "id": 10,
          "legend": {
            "alignAsTable": false,
            "avg": false,
            "current": false,
            "hideEmpty": true,
            "hideZero": false,
            "max": false,
            "min": false,
            "rightSide": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "percentage": false,
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": true,
          "targets": [
            {
              "expr": "sum by (reconciler)(60 * rate(controller_reconcile_count[1m]))",
              "format": "time_series",
              "instant": false,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "{{reconciler}}",
              "refId": "A"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeShift": null,
          "title": "Reconcile Count (per min)",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ]
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 10
          },
          "id": 5,
          "panels": [],
          "title": "Per Reconciler",
          "type": "row"
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "prometheus",
          "fill": 1,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 11
          },
          "id": 2,
          "legend": {
            "alignAsTable": false,
            "avg": false,
            "current": false,
            "hideEmpty": true,
            "hideZero": false,
            "max": false,
            "min": false,
            "rightSide": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "percentage": false,
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": true,
          "targets": [
            {
              "expr": "sum(60 * rate(controller_reconcile_count{reconciler=\"$reconciler\"}[1m]))",
              "format": "time_series",
              "instant": false,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "{{reconciler}}",
              "refId": "A"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeShift": null,
          "title": "$reconciler Reconcile Count (per min)",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ]
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "prometheus",
          "fill": 1,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 11
          },
          "id": 11,
          "legend": {
            "alignAsTable": false,
            "avg": false,
            "current": false,
            "hideEmpty": true,
            "hideZero": false,
            "max": false,
            "min": false,
            "rightSide": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "percentage": false,
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": true,
          "targets": [
            {
              "expr": "histogram_quantile(0.99, sum(rate(controller_reconcile_latency_bucket{reconciler=\"$reconciler\"}[1m])) by (le))",
              "format": "time_series",
              "instant": false,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "99th",
              "refId": "A"
            },
            {
              "expr": "histogram_quantile(0.90, sum(rate(controller_reconcile_latency_bucket{reconciler=\"$reconciler\"}[1m])) by (le))",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "90th",
              "refId": "B"
            },
            {
              "expr": "histogram_quantile(0.50, sum(rate(controller_reconcile_latency_bucket{reconciler=\"$reconciler\"}[1m])) by (le))",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "50th",
              "refId": "C"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeShift": null,
          "title": "$reconciler Reconcile Latency Percentiles",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ]
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 20
          },
          "id": 9,
          "panels": [],
          "title": "Per Reconciler & Key",
          "type": "row"
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "prometheus",
          "fill": 1,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 21
          },
          "id": 12,
          "legend": {
            "alignAsTable": false,
            "avg": false,
            "current": false,
            "hideEmpty": true,
            "hideZero": false,
            "max": false,
            "min": false,
            "rightSide": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "percentage": false,
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": true,
          "targets": [
            {
              "expr": "sum(60 * rate(controller_reconcile_count{reconciler=\"$reconciler\", key=\"$key\"}[1m]))",
              "format": "time_series",
              "instant": false,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "{{reconciler}}",
              "refId": "A"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeShift": null,
          "title": "$reconciler/$key Reconcile Count (per min)",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ]
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "prometheus",
          "fill": 1,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 21
          },
          "id": 13,
          "legend": {
            "alignAsTable": false,
            "avg": false,
            "current": false,
            "hideEmpty": true,
            "hideZero": false,
            "max": false,
            "min": false,
            "rightSide": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "percentage": false,
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": true,
          "targets": [
            {
              "expr": "histogram_quantile(0.99, sum(rate(controller_reconcile_latency_bucket{reconciler=\"$reconciler\", key=\"$key\"}[1m])) by (le))",
              "format": "time_series",
              "instant": false,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "99th",
              "refId": "A"
            },
            {
              "expr": "histogram_quantile(0.90, sum(rate(controller_reconcile_latency_bucket{reconciler=\"$reconciler\", key=\"$key\"}[1m])) by (le))",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "90th",
              "refId": "B"
            },
            {
              "expr": "histogram_quantile(0.50, sum(rate(controller_reconcile_latency_bucket{reconciler=\"$reconciler\", key=\"$key\"}[1m])) by (le))",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "50th",
              "refId": "C"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeShift": null,
          "title": "$reconciler/$key Reconcile Latency Percentiles",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ]
        }
      ],
      "refresh": "5s",
      "schemaVersion": 16,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": null,
            "current": {},
            "datasource": "prometheus",
            "hide": 0,
            "includeAll": false,
            "label": "Reconciler",
            "multi": false,
            "name": "reconciler",
            "options": [],
            "query": "label_values(controller_reconcile_count, reconciler)",
            "refresh": 1,
            "regex": "",
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {},
            "datasource": "prometheus",
            "hide": 0,
            "includeAll": false,
            "label": "Key",
            "multi": false,
            "name": "key",
            "options": [],
            "query": "label_values(controller_reconcile_count{reconciler=\"$reconciler\"}, key)",
            "refresh": 1,
            "regex": "",
            "sort": 1,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          }
        ]
      },
      "time": {
        "from": "now-15m",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "",
      "title": "Knative Serving - Reconciler",
      "uid": "j0oFdEYiz",
      "version": 10
    }
kind: ConfigMap
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: grafana-dashboard-definition-knative-reconciler
  namespace: knative-monitoring

---
apiVersion: v1
data:
  scaling-dashboard.json: |
    {
       "__inputs": [
          {
             "name": "prometheus",
             "label": "prometheus",
             "description": "",
             "type": "datasource",
             "pluginId": "prometheus",
             "pluginName": "Prometheus"
          }
       ],
       "__requires": [
          {
             "type": "grafana",
             "id": "grafana",
             "name": "Grafana",
             "version": "5.0.3"
          },
          {
             "type": "panel",
             "id": "graph",
             "name": "Graph",
             "version": "5.0.0"
          },
          {
             "type": "datasource",
             "id": "prometheus",
             "name": "Prometheus",
             "version": "5.0.0"
          }
       ],
       "annotations": {
          "list": [
             {
             "builtIn": 1,
             "datasource": "-- Grafana --",
             "enable": true,
             "hide": true,
             "iconColor": "rgba(0, 211, 255, 1)",
             "name": "Annotations & Alerts",
             "type": "dashboard"
             }
          ]
       },
       "description": "Knative Serving - Scaling Debugging",
       "editable": true,
       "gnetId": null,
       "graphTooltip": 0,
       "id": null,
       "iteration": 1564079739384,
       "links": [],
       "panels": [
          {
             "collapsed": true,
             "gridPos": {
             "h": 1,
             "w": 24,
             "x": 0,
             "y": 0
             },
             "id": 14,
             "panels": [
             {
                "aliasColors": {},
                "bars": false,
                "dashLength": 10,
                "dashes": false,
                "datasource": "prometheus",
                "fill": 1,
                "gridPos": {
                   "h": 11,
                   "w": 24,
                   "x": 0,
                   "y": 1
                },
                "id": 2,
                "legend": {
                   "avg": false,
                   "current": false,
                   "max": false,
                   "min": false,
                   "show": true,
                   "total": false,
                   "values": false
                },
                "lines": true,
                "linewidth": 1,
                "links": [],
                "nullPointMode": "null",
                "percentage": false,
                "pointradius": 5,
                "points": false,
                "renderer": "flot",
                "seriesOverrides": [],
                "spaceLength": 10,
                "stack": false,
                "steppedLine": true,
                "targets": [
                   {
                   "expr": "sum(autoscaler_actual_pods{namespace_name=\"$namespace\", configuration_name=\"$configuration\", revision_name=\"$revision\"})",
                   "format": "time_series",
                   "interval": "1s",
                   "intervalFactor": 1,
                   "legendFormat": "Actual Pods",
                   "refId": "A"
                   },
                   {
                   "expr": "sum(autoscaler_requested_pods{namespace_name=\"$namespace\", configuration_name=\"$configuration\", revision_name=\"$revision\"})",
                   "format": "time_series",
                   "interval": "1s",
                   "intervalFactor": 1,
                   "legendFormat": "Requested Pods",
                   "refId": "C"
                   }
                ],
                "thresholds": [],
                "timeFrom": null,
                "timeShift": null,
                "title": "Revision Pod Counts",
                "tooltip": {
                   "shared": true,
                   "sort": 0,
                   "value_type": "individual"
                },
                "type": "graph",
                "xaxis": {
                   "buckets": null,
                   "mode": "time",
                   "name": null,
                   "show": true,
                   "values": []
                },
                "yaxes": [
                   {
                   "format": "short",
                   "label": null,
                   "logBase": 1,
                   "max": null,
                   "min": null,
                   "show": true
                   },
                   {
                   "decimals": null,
                   "format": "short",
                   "label": "Concurrency",
                   "logBase": 1,
                   "max": "1",
                   "min": null,
                   "show": false
                   }
                ]
             }
             ],
             "title": "Revision Pod Counts",
             "type": "row"
          },
          {
             "collapsed": true,
             "gridPos": {
             "h": 1,
             "w": 24,
             "x": 0,
             "y": 1
             },
             "id": 18,
             "panels": [
             {
                "aliasColors": {},
                "bars": false,
                "dashLength": 10,
                "dashes": false,
                "datasource": "prometheus",
                "fill": 1,
                "gridPos": {
                   "h": 9,
                   "w": 12,
                   "x": 0,
                   "y": 13
                },
                "id": 4,
                "legend": {
                   "avg": false,
                   "current": false,
                   "max": false,
                   "min": false,
                   "show": true,
                   "total": false,
                   "values": false
                },
                "lines": true,
                "linewidth": 1,
                "links": [],
                "nullPointMode": "null",
                "percentage": false,
                "pointradius": 5,
                "points": false,
                "renderer": "flot",
                "seriesOverrides": [],
                "spaceLength": 10,
                "stack": false,
                "steppedLine": false,
                "targets": [
                   {
                   "expr": "sum(kube_pod_container_resource_requests_cpu_cores{namespace=\"$namespace\", pod=~\"$revision-deployment-.*\"})",
                   "format": "time_series",
                   "interval": "",
                   "intervalFactor": 1,
                   "legendFormat": "Cores requested",
                   "refId": "A"
                   },
                   {
                   "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"$namespace\", pod_name=~\"$revision-deployment-.*\"}[1m]))",
                   "format": "time_series",
                   "intervalFactor": 1,
                   "legendFormat": "Cores used",
                   "refId": "B"
                   },
                   {
                   "expr": "sum(kube_pod_container_resource_limits_cpu_cores{namespace=\"$namespace\", pod=~\"$revision-deployment-.*\"})",
                   "format": "time_series",
                   "intervalFactor": 1,
                   "legendFormat": "Core limit",
                   "refId": "C"
                   }
                ],
                "thresholds": [],
                "timeFrom": null,
                "timeShift": null,
                "title": "Revision CPU Usage",
                "tooltip": {
                   "shared": true,
                   "sort": 2,
                   "value_type": "individual"
                },
                "type": "graph",
                "xaxis": {
                   "buckets": null,
                   "mode": "time",
                   "name": null,
                   "show": true,
                   "values": []
                },
                "yaxes": [
                   {
                   "decimals": null,
                   "format": "short",
                   "label": null,
                   "logBase": 1,
                   "max": null,
                   "min": null,
                   "show": true
                   },
                   {
                   "format": "short",
                   "label": null,
                   "logBase": 1,
                   "max": null,
                   "min": null,
                   "show": false
                   }
                ]
             },
             {
                "aliasColors": {},
                "bars": false,
                "dashLength": 10,
                "dashes": false,
                "datasource": "prometheus",
                "fill": 1,
                "gridPos": {
                   "h": 9,
                   "w": 12,
                   "x": 12,
                   "y": 13
                },
                "id": 6,
                "legend": {
                   "avg": false,
                   "current": false,
                   "max": false,
                   "min": false,
                   "show": true,
                   "total": false,
                   "values": false
                },
                "lines": true,
                "linewidth": 1,
                "links": [],
                "nullPointMode": "null",
                "percentage": false,
                "pointradius": 5,
                "points": false,
                "renderer": "flot",
                "seriesOverrides": [],
                "spaceLength": 10,
                "stack": false,
                "steppedLine": false,
                "targets": [
                   {
                   "expr": "sum(kube_pod_container_resource_requests_memory_bytes{namespace=\"$namespace\", pod=~\"$revision-deployment-.*\"})",
                   "format": "time_series",
                   "interval": "",
                   "intervalFactor": 1,
                   "legendFormat": "Memory requested",
                   "refId": "A"
                   },
                   {
                   "expr": "sum(container_memory_usage_bytes{namespace=\"$namespace\", pod_name=~\"$revision-deployment-.*\"})",
                   "format": "time_series",
                   "hide": false,
                   "intervalFactor": 1,
                   "legendFormat": "Memory used",
                   "refId": "B"
                   },
                   {
                   "expr": "sum(kube_pod_container_resource_limits_memory_bytes{namespace=\"$namespace\", pod=~\"$revision-deployment-.*\"})",
                   "format": "time_series",
                   "intervalFactor": 1,
                   "refId": "C"
                   }
                ],
                "thresholds": [],
                "timeFrom": null,
                "timeShift": null,
                "title": "Pod Memory Usage",
                "tooltip": {
                   "shared": true,
                   "sort": 2,
                   "value_type": "individual"
                },
                "type": "graph",
                "xaxis": {
                   "buckets": null,
                   "mode": "time",
                   "name": null,
                   "show": true,
                   "values": []
                },
                "yaxes": [
                   {
                   "format": "decbytes",
                   "label": null,
                   "logBase": 1,
                   "max": null,
                   "min": null,
                   "show": true
                   },
                   {
                   "format": "short",
                   "label": null,
                   "logBase": 1,
                   "max": null,
                   "min": null,
                   "show": false
                   }
                ]
             }
             ],
             "title": "Resource Usages",
             "type": "row"
          },
          {
             "collapsed": false,
             "gridPos": {
             "h": 1,
             "w": 24,
             "x": 0,
             "y": 2
             },
             "id": 16,
             "panels": [],
             "title": "Autoscaler Metrics",
             "type": "row"
          },
          {
             "aliasColors": {},
             "bars": false,
             "dashLength": 10,
             "dashes": false,
             "datasource": "prometheus",
             "fill": 1,
             "gridPos": {
             "h": 10,
             "w": 24,
             "x": 0,
             "y": 3
             },
             "id": 10,
             "legend": {
             "avg": false,
             "current": false,
             "max": false,
             "min": false,
             "show": true,
             "total": false,
             "values": false
             },
             "lines": true,
             "linewidth": 1,
             "links": [],
             "nullPointMode": "null",
             "percentage": false,
             "pointradius": 5,
             "points": false,
             "renderer": "flot",
             "seriesOverrides": [],
             "spaceLength": 10,
             "stack": false,
             "steppedLine": true,
             "targets": [
             {
                "expr": "sum(autoscaler_desired_pods{namespace_name=\"$namespace\", configuration_name=\"$configuration\", revision_name=\"$revision\"}) ",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "Desired Pods",
                "refId": "A"
             },
             {
                "expr": "sum(autoscaler_observed_pods{namespace_name=\"$namespace\", configuration_name=\"$configuration\", revision_name=\"$revision\"})",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "Observed Pods",
                "refId": "B"
             }
             ],
             "thresholds": [],
             "timeFrom": null,
             "timeShift": null,
             "title": "Pod Counts",
             "tooltip": {
             "shared": true,
             "sort": 0,
             "value_type": "individual"
             },
             "type": "graph",
             "xaxis": {
             "buckets": null,
             "mode": "time",
             "name": null,
             "show": true,
             "values": []
             },
             "yaxes": [
             {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
             },
             {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
             }
             ]
          },
          {
             "aliasColors": {},
             "bars": false,
             "dashLength": 10,
             "dashes": false,
             "datasource": "prometheus",
             "fill": 1,
             "gridPos": {
             "h": 9,
             "w": 24,
             "x": 0,
             "y": 13
             },
             "id": 8,
             "legend": {
             "avg": false,
             "current": false,
             "max": false,
             "min": false,
             "show": true,
             "total": false,
             "values": false
             },
             "lines": true,
             "linewidth": 1,
             "links": [],
             "nullPointMode": "null",
             "percentage": false,
             "pointradius": 5,
             "points": false,
             "renderer": "flot",
             "seriesOverrides": [
             {
                "alias": "Panic Mode",
                "color": "#ea6460",
                "dashes": true,
                "fill": 2,
                "linewidth": 2,
                "steppedLine": true,
                "yaxis": 2
             },
             {
                "alias": "Target Concurrency Per Pod",
                "color": "#0a50a1",
                "dashes": true,
                "steppedLine": false
             }
             ],
             "spaceLength": 10,
             "stack": false,
             "steppedLine": true,
             "targets": [
             {
                "expr": "sum(autoscaler_stable_request_concurrency{namespace_name=\"$namespace\", configuration_name=\"$configuration\", revision_name=\"$revision\"})",
                "format": "time_series",
                "interval": "1s",
                "intervalFactor": 1,
                "legendFormat": "Average Concurrency",
                "refId": "A"
             },
             {
                "expr": "sum(autoscaler_panic_request_concurrency{namespace_name=\"$namespace\", configuration_name=\"$configuration\", revision_name=\"$revision\"})",
                "format": "time_series",
                "interval": "1s",
                "intervalFactor": 1,
                "legendFormat": "Average Panic Concurrency",
                "refId": "B"
             },
             {
                "expr": "sum(autoscaler_target_concurrency_per_pod{namespace_name=\"$namespace\", configuration_name=\"$configuration\", revision_name=\"$revision\"})",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "Target Concurrency",
                "refId": "C"
             },
             {
                "expr": "sum(autoscaler_excess_burst_capacity{namespace_name=\"$namespace\", configuration_name=\"$configuration\", revision_name=\"$revision\"})",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "Excess Burst Capacity",
                "refId": "D"
             }
             ],
             "thresholds": [],
             "timeFrom": null,
             "timeShift": null,
             "title": "Observed Concurrency",
             "tooltip": {
             "shared": true,
             "sort": 0,
             "value_type": "individual"
             },
             "type": "graph",
             "xaxis": {
             "buckets": null,
             "mode": "time",
             "name": null,
             "show": true,
             "values": []
             },
             "yaxes": [
             {
                "format": "short",
                "label": "",
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
             },
             {
                "format": "short",
                "label": "",
                "logBase": 1,
                "max": null,
                "min": null,
                "show": false
             }
             ]
          },
          {
             "aliasColors": {},
             "bars": false,
             "dashLength": 10,
             "dashes": false,
             "datasource": "prometheus",
             "decimals": null,
             "fill": 1,
             "gridPos": {
             "h": 9,
             "w": 24,
             "x": 0,
             "y": 22
             },
             "id": 12,
             "legend": {
             "avg": false,
             "current": false,
             "hideZero": false,
             "max": false,
             "min": false,
             "show": false,
             "total": false,
             "values": false
             },
             "lines": true,
             "linewidth": 1,
             "links": [],
             "nullPointMode": "null",
             "percentage": false,
             "pointradius": 5,
             "points": false,
             "renderer": "flot",
             "seriesOverrides": [
             {
                "alias": "Panic Mode",
                "color": "#e24d42",
                "linewidth": 2,
                "yaxis": 2
             }
             ],
             "spaceLength": 10,
             "stack": false,
             "steppedLine": true,
             "targets": [
             {
                "expr": "sum(autoscaler_panic_mode{namespace_name=\"$namespace\", configuration_name=\"$configuration\", revision_name=\"$revision\"} )",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "Panic Mode",
                "refId": "A"
             }
             ],
             "thresholds": [],
             "timeFrom": null,
             "timeShift": null,
             "title": "Panic Mode",
             "tooltip": {
             "shared": true,
             "sort": 0,
             "value_type": "individual"
             },
             "type": "graph",
             "xaxis": {
             "buckets": null,
             "mode": "time",
             "name": null,
             "show": true,
             "values": []
             },
             "yaxes": [
             {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": "1.0",
                "min": "0",
                "show": true
             },
             {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": false
             }
             ]
          },
          {
             "collapsed": false,
             "gridPos": {
             "h": 1,
             "w": 24,
             "x": 0,
             "y": 31
             },
             "id": 20,
             "panels": [],
             "title": "Activator Metrics",
             "type": "row"
          },
          {
             "aliasColors": {},
             "bars": false,
             "dashLength": 10,
             "dashes": false,
             "datasource": "prometheus",
             "fill": 1,
             "gridPos": {
             "h": 10,
             "w": 24,
             "x": 0,
             "y": 32
             },
             "id": 24,
             "legend": {
             "avg": false,
             "current": false,
             "max": false,
             "min": false,
             "show": true,
             "total": false,
             "values": false
             },
             "lines": true,
             "linewidth": 1,
             "links": [],
             "nullPointMode": "null",
             "percentage": false,
             "pointradius": 5,
             "points": false,
             "renderer": "flot",
             "seriesOverrides": [],
             "spaceLength": 10,
             "stack": false,
             "steppedLine": false,
             "targets": [
             {
                "expr": "round(sum(increase(activator_request_count{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\"}[1m])) by (response_code))",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "{{ response_code }}",
                "refId": "A"
             }
             ],
             "thresholds": [],
             "timeFrom": null,
             "timeShift": null,
             "title": "Request Count in last minute by Response Code",
             "tooltip": {
             "shared": true,
             "sort": 0,
             "value_type": "individual"
             },
             "type": "graph",
             "xaxis": {
             "buckets": null,
             "mode": "time",
             "name": null,
             "show": true,
             "values": []
             },
             "yaxes": [
             {
                "format": "none",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": "0",
                "show": true
             },
             {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
             }
             ]
          },
          {
             "aliasColors": {},
             "bars": false,
             "dashLength": 10,
             "dashes": false,
             "datasource": "prometheus",
             "fill": 1,
             "gridPos": {
             "h": 10,
             "w": 24,
             "x": 0,
             "y": 42
             },
             "id": 28,
             "legend": {
             "avg": true,
             "current": false,
             "max": false,
             "min": false,
             "show": true,
             "total": false,
             "values": true
             },
             "lines": true,
             "linewidth": 1,
             "links": [],
             "nullPointMode": "null",
             "percentage": false,
             "pointradius": 5,
             "points": false,
             "renderer": "flot",
             "seriesOverrides": [],
             "spaceLength": 10,
             "stack": false,
             "steppedLine": false,
             "targets": [
             {
                "expr": "label_replace(histogram_quantile(0.50, sum(rate(activator_request_latencies_bucket{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\"}[1m])) by (revision_name, le)), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "{{ revision_name }} (p50)",
                "refId": "A"
             },
             {
                "expr": "label_replace(histogram_quantile(0.90, sum(rate(activator_request_latencies_bucket{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\"}[1m])) by (revision_name, le)), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "{{ revision_name }} (p90)",
                "refId": "B"
             },
             {
                "expr": "label_replace(histogram_quantile(0.95, sum(rate(activator_request_latencies_bucket{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\"}[1m])) by (revision_name, le)), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "{{ revision_name }} (p95)",
                "refId": "C"
             },
             {
                "expr": "label_replace(histogram_quantile(0.99, sum(rate(activator_request_latencies_bucket{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\"}[1m])) by (revision_name, le)), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "{{ revision_name }} (p99)",
                "refId": "D"
             }
             ],
             "thresholds": [],
             "timeFrom": null,
             "timeShift": null,
             "title": "Response Time in last minute",
             "tooltip": {
             "shared": true,
             "sort": 0,
             "value_type": "individual"
             },
             "type": "graph",
             "xaxis": {
             "buckets": null,
             "mode": "time",
             "name": null,
             "show": true,
             "values": []
             },
             "yaxes": [
             {
                "format": "ms",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
             },
             {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
             }
             ]
          },
          {
             "aliasColors": {},
             "bars": false,
             "dashLength": 10,
             "dashes": false,
             "datasource": "prometheus",
             "fill": 1,
             "gridPos": {
             "h": 9,
             "w": 24,
             "x": 0,
             "y": 52
             },
             "id": 29,
             "legend": {
             "avg": false,
             "current": false,
             "max": false,
             "min": false,
             "show": true,
             "total": false,
             "values": false
             },
             "lines": true,
             "linewidth": 1,
             "links": [],
             "nullPointMode": "null",
             "percentage": false,
             "pointradius": 5,
             "points": false,
             "renderer": "flot",
             "seriesOverrides": [
             {
                "alias": "Panic Mode",
                "color": "#ea6460",
                "dashes": true,
                "fill": 2,
                "linewidth": 2,
                "steppedLine": true,
                "yaxis": 2
             },
             {
                "alias": "Target Concurrency Per Pod",
                "color": "#0a50a1",
                "dashes": true,
                "steppedLine": false
             }
             ],
             "spaceLength": 10,
             "stack": false,
             "steppedLine": true,
             "targets": [
             {
                "expr": "sum(activator_request_concurrency{namespace_name=\"$namespace\", configuration_name=\"$configuration\", revision_name=\"$revision\"})",
                "format": "time_series",
                "interval": "1s",
                "intervalFactor": 1,
                "legendFormat": "Request Concurrency",
                "refId": "A"
             }
             ],
             "thresholds": [],
             "timeFrom": null,
             "timeShift": null,
             "title": "Request Concurrency",
             "tooltip": {
             "shared": true,
             "sort": 0,
             "value_type": "individual"
             },
             "type": "graph",
             "xaxis": {
             "buckets": null,
             "mode": "time",
             "name": null,
             "show": true,
             "values": []
             },
             "yaxes": [
             {
                "format": "short",
                "label": "",
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
             },
             {
                "format": "short",
                "label": "",
                "logBase": 1,
                "max": null,
                "min": null,
                "show": false
             }
             ]
          }
       ],
       "refresh": false,
       "schemaVersion": 16,
       "style": "dark",
       "tags": [],
       "templating": {
          "list": [
             {
             "allValue": null,
             "current": {},
             "datasource": "prometheus",
             "hide": 0,
             "includeAll": false,
             "label": "Namespace",
             "multi": false,
             "name": "namespace",
             "options": [],
             "query": "label_values(autoscaler_desired_pods, namespace_name)",
             "refresh": 1,
             "regex": "",
             "sort": 1,
             "tagValuesQuery": "",
             "tags": [],
             "tagsQuery": "",
             "type": "query",
             "useTags": false
             },
             {
             "allValue": null,
             "current": {},
             "datasource": "prometheus",
             "hide": 0,
             "includeAll": false,
             "label": "Configuration",
             "multi": false,
             "name": "configuration",
             "options": [],
             "query": "label_values(autoscaler_desired_pods{namespace_name=\"$namespace\"}, configuration_name)",
             "refresh": 1,
             "regex": "",
             "sort": 1,
             "tagValuesQuery": "",
             "tags": [],
             "tagsQuery": "",
             "type": "query",
             "useTags": false
             },
             {
             "allValue": null,
             "current": {},
             "datasource": "prometheus",
             "hide": 0,
             "includeAll": false,
             "label": "Revision",
             "multi": false,
             "name": "revision",
             "options": [],
             "query": "label_values(autoscaler_desired_pods{namespace_name=\"$namespace\", configuration_name=\"$configuration\"}, revision_name)",
             "refresh": 1,
             "regex": "",
             "sort": 2,
             "tagValuesQuery": "",
             "tags": [],
             "tagsQuery": "",
             "type": "query",
             "useTags": false
             }
          ]
       },
       "time": {
          "from": "now-15m",
          "to": "now"
       },
       "timepicker": {
          "refresh_intervals": [
             "5s",
             "10s",
             "30s",
             "1m",
             "5m",
             "15m",
             "30m",
             "1h",
             "2h",
             "1d"
          ],
          "time_options": [
             "5m",
             "15m",
             "1h",
             "6h",
             "12h",
             "24h",
             "2d",
             "7d",
             "30d"
          ]
       },
       "timezone": "",
       "title": "Knative Serving - Scaling Debugging",
       "uid": "u_-9SIMiz",
       "version": 2
       }
kind: ConfigMap
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: scaling-config
  namespace: knative-monitoring

---
apiVersion: v1
data:
  resource-dashboard.json: |
    {
      "__inputs": [
        {
          "description": "",
          "label": "prometheus",
          "name": "prometheus",
          "pluginId": "prometheus",
          "pluginName": "Prometheus",
          "type": "datasource"
        }
      ],
      "annotations": {
        "list": []
      },
      "description": "Knative Serving - Revision CPU and Memory Usage",
      "editable": false,
      "gnetId": null,
      "graphTooltip": 0,
      "links": [],
      "panels": [
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "prometheus",
          "fill": 1,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 0
          },
          "id": 2,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "percentage": false,
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"$namespace\", pod_name=~\"$revision.*\", container_name != \"POD\"}[1m])) by (container_name)",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "{{container_name}}",
              "refId": "A"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeShift": null,
          "title": "Total CPU Usage",
          "tooltip": {
            "shared": true,
            "sort": 2,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "s",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": false
            }
          ]
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "prometheus",
          "fill": 1,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 0
          },
          "id": 3,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "percentage": false,
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "sum(container_memory_usage_bytes{namespace=\"$namespace\", pod_name=~\"$revision.*\", container_name != \"POD\"}) by (container_name)",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "{{container_name}}",
              "refId": "A"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeShift": null,
          "title": "Total Memory Usage",
          "tooltip": {
            "shared": true,
            "sort": 2,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "decbytes",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": false
            }
          ]
        }
      ],
      "refresh": "5s",
      "schemaVersion": 16,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": null,
            "current": {},
            "datasource": "prometheus",
            "hide": 0,
            "includeAll": false,
            "label": "Namespace",
            "multi": false,
            "name": "namespace",
            "options": [],
            "query": "label_values(kube_pod_labels{label_serving_knative_dev_configuration=~\".+\"}, namespace)",
            "refresh": 2,
            "regex": "",
            "sort": 1,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {},
            "datasource": "prometheus",
            "hide": 0,
            "includeAll": false,
            "label": "Configuration",
            "multi": false,
            "name": "configuration",
            "options": [],
            "query": "label_values(kube_pod_labels{label_serving_knative_dev_configuration=~\".+\", namespace=\"$namespace\"}, label_serving_knative_dev_configuration)",
            "refresh": 2,
            "regex": "",
            "sort": 1,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {},
            "datasource": "prometheus",
            "hide": 0,
            "includeAll": false,
            "label": "Revision",
            "multi": false,
            "name": "revision",
            "options": [],
            "query": "label_values(kube_pod_labels{label_serving_knative_dev_configuration=~\".+\", namespace=\"$namespace\", label_serving_knative_dev_configuration=\"$configuration\"}, label_serving_knative_dev_revision)",
            "refresh": 2,
            "regex": "",
            "sort": 2,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          }
        ]
      },
      "time": {
        "from": "now-15m",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "",
      "title": "Knative Serving - Revision CPU and Memory Usage",
      "uid": "bKOoE9Wmk",
      "version": 4
    }
  revision-dashboard.json: |
    {
      "__inputs": [
        {
          "description": "",
          "label": "prometheus",
          "name": "prometheus",
          "pluginId": "prometheus",
          "pluginName": "Prometheus",
          "type": "datasource"
        }
      ],
      "annotations": {
        "list": []
      },
      "description": "Knative Serving - Revision HTTP Requests",
      "editable": false,
      "gnetId": null,
      "graphTooltip": 0,
      "links": [],
      "panels": [
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "id": 8,
          "panels": [],
          "title": "Overview (average over the selected time range)",
          "type": "row"
        },
        {
          "cacheTimeout": null,
          "colorBackground": false,
          "colorValue": false,
          "colors": [
            "#299c46",
            "rgba(237, 129, 40, 0.89)",
            "#d44a3a"
          ],
          "datasource": "prometheus",
          "format": "ops",
          "gauge": {
            "maxValue": 100,
            "minValue": 0,
            "show": false,
            "thresholdLabels": false,
            "thresholdMarkers": true
          },
          "gridPos": {
            "h": 4,
            "w": 6,
            "x": 0,
            "y": 1
          },
          "id": 2,
          "interval": null,
          "links": [],
          "mappingType": 1,
          "mappingTypes": [
            {
              "name": "value to text",
              "value": 1
            },
            {
              "name": "range to text",
              "value": 2
            }
          ],
          "maxDataPoints": 100,
          "nullPointMode": "connected",
          "nullText": null,
          "postfix": "",
          "postfixFontSize": "50%",
          "prefix": "",
          "prefixFontSize": "50%",
          "rangeMaps": [
            {
              "from": "null",
              "text": "N/A",
              "to": "null"
            }
          ],
          "repeat": null,
          "repeatDirection": "v",
          "sparkline": {
            "fillColor": "rgba(31, 118, 189, 0.18)",
            "full": true,
            "lineColor": "rgb(31, 120, 193)",
            "show": true
          },
          "tableColumn": "",
          "targets": [
            {
              "expr": "round(sum(rate(revision_request_count{namespace_name=\"$namespace\", revision_name=~\"$revision\", configuration_name=~\"$configuration\"}[1m])), 0.001)",
              "format": "time_series",
              "hide": false,
              "interval": "",
              "intervalFactor": 1,
              "refId": "A"
            }
          ],
          "thresholds": "",
          "title": "Request Volume",
          "type": "singlestat",
          "valueFontSize": "80%",
          "valueMaps": [
            {
              "op": "=",
              "text": "N/A",
              "value": "null"
            }
          ],
          "valueName": "avg"
        },
        {
          "cacheTimeout": null,
          "colorBackground": false,
          "colorValue": false,
          "colors": [
            "#d44a3a",
            "rgba(237, 129, 40, 0.89)",
            "#299c46"
          ],
          "datasource": "prometheus",
          "format": "percentunit",
          "gauge": {
            "maxValue": 100,
            "minValue": 0,
            "show": false,
            "thresholdLabels": false,
            "thresholdMarkers": true
          },
          "gridPos": {
            "h": 4,
            "w": 6,
            "x": 6,
            "y": 1
          },
          "id": 4,
          "interval": null,
          "links": [],
          "mappingType": 1,
          "mappingTypes": [
            {
              "name": "value to text",
              "value": 1
            },
            {
              "name": "range to text",
              "value": 2
            }
          ],
          "maxDataPoints": 100,
          "nullPointMode": "connected",
          "nullText": null,
          "postfix": "",
          "postfixFontSize": "50%",
          "prefix": "",
          "prefixFontSize": "50%",
          "rangeMaps": [
            {
              "from": "null",
              "text": "N/A",
              "to": "null"
            }
          ],
          "sparkline": {
            "fillColor": "rgba(31, 118, 189, 0.18)",
            "full": true,
            "lineColor": "rgb(31, 120, 193)",
            "show": true
          },
          "tableColumn": "",
          "targets": [
            {
              "expr": "sum(rate(revision_request_count{response_code_class!=\"5xx\", namespace_name=\"$namespace\", revision_name=~\"$revision\", configuration_name=~\"$configuration\"}[1m])) / sum(rate(revision_request_count{namespace_name=\"$namespace\", revision_name=~\"$revision\", configuration_name=~\"$configuration\"}[1m]))",
              "format": "time_series",
              "interval": "",
              "intervalFactor": 1,
              "refId": "A"
            }
          ],
          "thresholds": "95, 99, 99.5",
          "title": "Success Rate (non-5xx responses)",
          "type": "singlestat",
          "valueFontSize": "80%",
          "valueMaps": [
            {
              "op": "=",
              "text": "N/A",
              "value": "null"
            }
          ],
          "valueName": "avg"
        },
        {
          "cacheTimeout": null,
          "colorBackground": false,
          "colorValue": false,
          "colors": [
            "#d44a3a",
            "rgba(237, 129, 40, 0.89)",
            "#299c46"
          ],
          "datasource": "prometheus",
          "format": "ops",
          "gauge": {
            "maxValue": 100,
            "minValue": 0,
            "show": false,
            "thresholdLabels": false,
            "thresholdMarkers": true
          },
          "gridPos": {
            "h": 4,
            "w": 6,
            "x": 12,
            "y": 1
          },
          "id": 5,
          "interval": null,
          "links": [],
          "mappingType": 1,
          "mappingTypes": [
            {
              "name": "value to text",
              "value": 1
            },
            {
              "name": "range to text",
              "value": 2
            }
          ],
          "maxDataPoints": 100,
          "nullPointMode": "connected",
          "nullText": null,
          "postfix": "",
          "postfixFontSize": "50%",
          "prefix": "",
          "prefixFontSize": "50%",
          "rangeMaps": [
            {
              "from": "null",
              "text": "N/A",
              "to": "null"
            }
          ],
          "sparkline": {
            "fillColor": "rgba(31, 118, 189, 0.18)",
            "full": true,
            "lineColor": "rgb(31, 120, 193)",
            "show": true
          },
          "tableColumn": "",
          "targets": [
            {
              "expr": "sum(rate(revision_request_count{response_code_class=\"4xx\", namespace_name=\"$namespace\", revision_name=~\"$revision\", configuration_name=~\"$configuration\"}[1m])) ",
              "format": "time_series",
              "interval": "",
              "intervalFactor": 1,
              "refId": "A"
            }
          ],
          "thresholds": "",
          "title": "4xx",
          "type": "singlestat",
          "valueFontSize": "80%",
          "valueMaps": [
            {
              "op": "=",
              "text": "N/A",
              "value": "null"
            }
          ],
          "valueName": "avg"
        },
        {
          "cacheTimeout": null,
          "colorBackground": false,
          "colorValue": false,
          "colors": [
            "#d44a3a",
            "rgba(237, 129, 40, 0.89)",
            "#299c46"
          ],
          "datasource": "prometheus",
          "format": "ops",
          "gauge": {
            "maxValue": 100,
            "minValue": 0,
            "show": false,
            "thresholdLabels": false,
            "thresholdMarkers": true
          },
          "gridPos": {
            "h": 4,
            "w": 6,
            "x": 18,
            "y": 1
          },
          "id": 9,
          "interval": null,
          "links": [],
          "mappingType": 1,
          "mappingTypes": [
            {
              "name": "value to text",
              "value": 1
            },
            {
              "name": "range to text",
              "value": 2
            }
          ],
          "maxDataPoints": 100,
          "nullPointMode": "connected",
          "nullText": null,
          "postfix": "",
          "postfixFontSize": "50%",
          "prefix": "",
          "prefixFontSize": "50%",
          "rangeMaps": [
            {
              "from": "null",
              "text": "N/A",
              "to": "null"
            }
          ],
          "sparkline": {
            "fillColor": "rgba(31, 118, 189, 0.18)",
            "full": true,
            "lineColor": "rgb(31, 120, 193)",
            "show": true
          },
          "tableColumn": "",
          "targets": [
            {
              "expr": "sum(rate(revision_request_count{response_code_class=\"5xx\", namespace_name=\"$namespace\", revision_name=~\"$revision\", configuration_name=~\"$configuration\"}[1m])) ",
              "format": "time_series",
              "interval": "",
              "intervalFactor": 1,
              "refId": "A"
            }
          ],
          "thresholds": "",
          "title": "5xx",
          "type": "singlestat",
          "valueFontSize": "80%",
          "valueMaps": [
            {
              "op": "=",
              "text": "N/A",
              "value": "null"
            }
          ],
          "valueName": "avg"
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 5
          },
          "id": 11,
          "panels": [],
          "title": "Request Volume",
          "type": "row"
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "prometheus",
          "fill": 1,
          "gridPos": {
            "h": 10,
            "w": 12,
            "x": 0,
            "y": 6
          },
          "id": 17,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "percentage": false,
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "label_replace(round(sum(rate(revision_request_count{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\"}[1m])) by (revision_name), 0.001), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
              "format": "time_series",
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "{{revision_name}}",
              "refId": "A"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeShift": null,
          "title": "Request Volume by Revision",
          "tooltip": {
            "shared": true,
            "sort": 2,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "ops",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ]
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "prometheus",
          "fill": 1,
          "gridPos": {
            "h": 10,
            "w": 12,
            "x": 12,
            "y": 6
          },
          "id": 18,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "percentage": false,
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "round(sum(rate(revision_request_count{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\"}[1m])) by (response_code_class), 0.001)",
              "format": "time_series",
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "{{ response_code_class }}",
              "refId": "A"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeShift": null,
          "title": "Request Volume by Response Code Class",
          "tooltip": {
            "shared": true,
            "sort": 2,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "ops",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ]
        },
        {
          "collapsed": false,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 16
          },
          "id": 15,
          "panels": [],
          "title": "Response Time",
          "type": "row"
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "prometheus",
          "fill": 1,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 17
          },
          "id": 20,
          "legend": {
            "alignAsTable": false,
            "avg": true,
            "current": false,
            "hideEmpty": false,
            "hideZero": false,
            "max": false,
            "min": false,
            "rightSide": false,
            "show": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "percentage": false,
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "label_replace(histogram_quantile(0.50, sum(rate(revision_request_latencies_bucket{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\"}[1m])) by (revision_name, le)), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "{{ revision_name }} (p50)",
              "refId": "A"
            },
            {
              "expr": "label_replace(histogram_quantile(0.90, sum(rate(revision_request_latencies_bucket{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\"}[1m])) by (revision_name, le)), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "{{ revision_name }} (p90)",
              "refId": "B"
            },
            {
              "expr": "label_replace(histogram_quantile(0.95, sum(rate(revision_request_latencies_bucket{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\"}[1m])) by (revision_name, le)), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "{{ revision_name }} (p95)",
              "refId": "C"
            },
            {
              "expr": "label_replace(histogram_quantile(0.99, sum(rate(revision_request_latencies_bucket{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\"}[1m])) by (revision_name, le)), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "{{ revision_name }} (p99)",
              "refId": "D"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeShift": null,
          "title": "Response Time by Revision",
          "tooltip": {
            "shared": true,
            "sort": 2,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "s",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": false
            }
          ]
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "prometheus",
          "fill": 1,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 17
          },
          "id": 21,
          "legend": {
            "alignAsTable": false,
            "avg": true,
            "current": false,
            "hideEmpty": false,
            "hideZero": false,
            "max": false,
            "min": false,
            "rightSide": false,
            "show": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "percentage": false,
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "label_replace(histogram_quantile(0.50, sum(rate(revision_request_latencies_bucket{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\",response_code_class=\"2xx\"}[1m])) by (le)), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "2xx (p50)",
              "refId": "C"
            },
            {
              "expr": "label_replace(histogram_quantile(0.50, sum(rate(revision_request_latencies_bucket{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\",response_code_class=\"3xx\"}[1m])) by (le)), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "3xx (p50)",
              "refId": "D"
            },
            {
              "expr": "label_replace(histogram_quantile(0.50, sum(rate(revision_request_latencies_bucket{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\",response_code_class=\"4xx\"}[1m])) by (le)), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "4xx (p50)",
              "refId": "A"
            },
            {
              "expr": "label_replace(histogram_quantile(0.50, sum(rate(revision_request_latencies_bucket{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\",response_code_class=\"5xx\"}[1m])) by (le)), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "5xx (p50)",
              "refId": "B"
            },
            {
              "expr": "label_replace(histogram_quantile(0.95, sum(rate(revision_request_latencies_bucket{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\",response_code_class=\"2xx\"}[1m])) by (le)), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "2xx (p95)",
              "refId": "E"
            },
            {
              "expr": "label_replace(histogram_quantile(0.95, sum(rate(revision_request_latencies_bucket{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\",response_code_class=\"3xx\"}[1m])) by (le)), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "3xx (p95)",
              "refId": "F"
            },
            {
              "expr": "label_replace(histogram_quantile(0.95, sum(rate(revision_request_latencies_bucket{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\",response_code_class=\"4xx\"}[1m])) by (le)), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "4xx (p95)",
              "refId": "G"
            },
            {
              "expr": "label_replace(histogram_quantile(0.95, sum(rate(revision_request_latencies_bucket{namespace_name=\"$namespace\", configuration_name=~\"$configuration\",revision_name=~\"$revision\",response_code_class=\"5xx\"}[1m])) by (le)), \"revision_name\", \"$2\", \"revision_name\", \"$configuration(-+)(.*)\")",
              "format": "time_series",
              "intervalFactor": 1,
              "legendFormat": "5xx (p95)",
              "refId": "H"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeShift": null,
          "title": "Response Time by Response Code Class",
          "tooltip": {
            "shared": true,
            "sort": 2,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "s",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": false
            }
          ]
        }
      ],
      "refresh": "5s",
      "schemaVersion": 16,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": null,
            "current": {},
            "datasource": "prometheus",
            "hide": 0,
            "includeAll": false,
            "label": "Namespace",
            "multi": false,
            "name": "namespace",
            "options": [],
            "query": "label_values(revision_request_count{namespace_name!=\"unknown\"}, namespace_name)",
            "refresh": 1,
            "regex": "",
            "sort": 1,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {},
            "datasource": "prometheus",
            "hide": 0,
            "includeAll": false,
            "label": "Configuration",
            "multi": false,
            "name": "configuration",
            "options": [],
            "query": "label_values(revision_request_count{namespace_name=\"$namespace\", configuration_name!=\"unknown\"}, configuration_name)",
            "refresh": 1,
            "regex": "",
            "sort": 1,
            "tagValuesQuery": "$tag",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {},
            "datasource": "prometheus",
            "hide": 0,
            "includeAll": true,
            "label": "Revision",
            "multi": true,
            "name": "revision",
            "options": [],
            "query": "label_values(revision_request_count{namespace_name=\"$namespace\", configuration_name=~\"$configuration\", revision_name!=\"unknown\"}, revision_name)",
            "refresh": 1,
            "regex": "",
            "sort": 2,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          }
        ]
      },
      "time": {
        "from": "now-15m",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "",
      "title": "Knative Serving - Revision HTTP Requests",
      "uid": "im_gFbWik",
      "version": 2
    }
kind: ConfigMap
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: grafana-dashboard-definition-knative
  namespace: knative-monitoring

---
apiVersion: v1
data:
  prometheus.yaml: |
    datasources:
     - name: prometheus
       type: prometheus
       access: proxy
       org_id: 1
       url: http://prometheus-system-np:8080
       version: 1
       editable: false
kind: ConfigMap
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: grafana-datasources
  namespace: knative-monitoring
---
apiVersion: v1
data:
  dashboards.yaml: |
    - name: 'knative'
      org_id: 1
      folder: ''
      type: file
      options:
        folder: /grafana-dashboard-definition/knative
    - name: 'knative-efficiency'
      org_id: 1
      folder: ''
      type: file
      options:
        folder: /grafana-dashboard-definition/knative-efficiency
    - name: 'knative-reconciler'
      org_id: 1
      folder: ''
      type: file
      options:
        folder: /grafana-dashboard-definition/knative-reconciler
    - name: 'istio'
      org_id: 1
      folder: ''
      type: file
      options:
        folder: /grafana-dashboard-definition/istio
    - name: 'mixer'
      org_id: 1
      folder: ''
      type: file
      options:
        folder: /grafana-dashboard-definition/mixer
    - name: 'pilot'
      org_id: 1
      folder: ''
      type: file
      options:
        folder: /grafana-dashboard-definition/pilot
    - name: 'kubernetes-deployment'
      org_id: 1
      folder: ''
      type: file
      options:
        folder: /grafana-dashboard-definition/kubernetes-deployment
    - name: 'kubernetes-capacity-planning'
      org_id: 1
      folder: ''
      type: file
      options:
        folder: /grafana-dashboard-definition/kubernetes-capacity-planning
    - name: 'kubernetes-cluster-health'
      org_id: 1
      folder: ''
      type: file
      options:
        folder: /grafana-dashboard-definition/kubernetes-cluster-health
    - name: 'kubernetes-cluster-status'
      org_id: 1
      folder: ''
      type: file
      options:
        folder: /grafana-dashboard-definition/kubernetes-cluster-status
    - name: 'kubernetes-control-plane-status'
      org_id: 1
      folder: ''
      type: file
      options:
        folder: /grafana-dashboard-definition/kubernetes-control-plane-status
    - name: 'kubernetes-resource-requests'
      org_id: 1
      folder: ''
      type: file
      options:
        folder: /grafana-dashboard-definition/kubernetes-resource-requests
    - name: 'kubernetes-nodes'
      org_id: 1
      folder: ''
      type: file
      options:
        folder: /grafana-dashboard-definition/kubernetes-nodes
    - name: 'kubernetes-pods'
      org_id: 1
      folder: ''
      type: file
      options:
        folder: /grafana-dashboard-definition/kubernetes-pods
    - name: 'kubernetes-statefulset'
      org_id: 1
      folder: ''
      type: file
      options:
        folder: /grafana-dashboard-definition/kubernetes-statefulset
    - name: 'knative-serving-scaling'
      org_id: 1
      folder: ''
      type: file
      options:
        folder: /grafana-dashboard-definition/scaling
kind: ConfigMap
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: grafana-dashboards
  namespace: knative-monitoring
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: grafana
    serving.knative.dev/release: "v0.8.0"
  name: grafana
  namespace: knative-monitoring
spec:
  ports:
  - port: 30802
    protocol: TCP
    targetPort: 3000
  selector:
    app: grafana
  type: NodePort
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: grafana
  namespace: knative-monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
        serving.knative.dev/release: "v0.8.0"
    spec:
      containers:
      - image: quay.io/coreos/monitoring-grafana:5.0.3
        name: grafana
        ports:
        - containerPort: 3000
          name: web
        resources:
          limits:
            cpu: 200m
            memory: 200Mi
          requests:
            cpu: 100m
            memory: 100Mi
        volumeMounts:
        - mountPath: /data
          name: grafana-storage
        - mountPath: /grafana/conf/provisioning/datasources
          name: grafana-datasources
        - mountPath: /grafana/conf/provisioning/dashboards
          name: grafana-dashboards
        - mountPath: /grafana-dashboard-definition/knative
          name: grafana-dashboard-definition-knative
        - mountPath: /grafana-dashboard-definition/knative-efficiency
          name: grafana-dashboard-definition-knative-efficiency
        - mountPath: /grafana-dashboard-definition/knative-reconciler
          name: grafana-dashboard-definition-knative-reconciler
        - mountPath: /grafana-dashboard-definition/kubernetes-deployment
          name: grafana-dashboard-definition-kubernetes-deployment
        - mountPath: /grafana-dashboard-definition/kubernetes-capacity-planning
          name: grafana-dashboard-definition-kubernetes-capacity-planning
        - mountPath: /grafana-dashboard-definition/kubernetes-cluster-health
          name: grafana-dashboard-definition-kubernetes-cluster-health
        - mountPath: /grafana-dashboard-definition/kubernetes-cluster-status
          name: grafana-dashboard-definition-kubernetes-cluster-status
        - mountPath: /grafana-dashboard-definition/kubernetes-control-plane-status
          name: grafana-dashboard-definition-kubernetes-control-plane-status
        - mountPath: /grafana-dashboard-definition/kubernetes-resource-requests
          name: grafana-dashboard-definition-kubernetes-resource-requests
        - mountPath: /grafana-dashboard-definition/kubernetes-nodes
          name: grafana-dashboard-definition-kubernetes-nodes
        - mountPath: /grafana-dashboard-definition/kubernetes-pods
          name: grafana-dashboard-definition-kubernetes-pods
        - mountPath: /grafana-dashboard-definition/kubernetes-statefulset
          name: grafana-dashboard-definition-kubernetes-statefulset
        - mountPath: /grafana-dashboard-definition/scaling
          name: scaling-config
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
      volumes:
      - emptyDir: {}
        name: grafana-storage
      - configMap:
          name: grafana-datasources
        name: grafana-datasources
      - configMap:
          name: grafana-dashboards
        name: grafana-dashboards
      - configMap:
          name: grafana-dashboard-definition-knative
        name: grafana-dashboard-definition-knative
      - configMap:
          name: grafana-dashboard-definition-knative-efficiency
        name: grafana-dashboard-definition-knative-efficiency
      - configMap:
          name: grafana-dashboard-definition-knative-reconciler
        name: grafana-dashboard-definition-knative-reconciler
      - configMap:
          name: grafana-dashboard-definition-kubernetes-deployment
        name: grafana-dashboard-definition-kubernetes-deployment
      - configMap:
          name: grafana-dashboard-definition-kubernetes-capacity-planning
        name: grafana-dashboard-definition-kubernetes-capacity-planning
      - configMap:
          name: grafana-dashboard-definition-kubernetes-cluster-health
        name: grafana-dashboard-definition-kubernetes-cluster-health
      - configMap:
          name: grafana-dashboard-definition-kubernetes-cluster-status
        name: grafana-dashboard-definition-kubernetes-cluster-status
      - configMap:
          name: grafana-dashboard-definition-kubernetes-control-plane-status
        name: grafana-dashboard-definition-kubernetes-control-plane-status
      - configMap:
          name: grafana-dashboard-definition-kubernetes-resource-requests
        name: grafana-dashboard-definition-kubernetes-resource-requests
      - configMap:
          name: grafana-dashboard-definition-kubernetes-nodes
        name: grafana-dashboard-definition-kubernetes-nodes
      - configMap:
          name: grafana-dashboard-definition-kubernetes-pods
        name: grafana-dashboard-definition-kubernetes-pods
      - configMap:
          name: grafana-dashboard-definition-kubernetes-statefulset
        name: grafana-dashboard-definition-kubernetes-statefulset
      - configMap:
          name: scaling-config
        name: scaling-config

---
apiVersion: v1
data:
  prometheus.yml: |-
    global:
      scrape_interval: 30s
      scrape_timeout: 10s
      evaluation_interval: 30s
    scrape_configs:
    # Controller endpoint
    - job_name: controller
      scrape_interval: 3s
      scrape_timeout: 3s
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_pod_label_app, __meta_kubernetes_pod_container_port_name]
        action: keep
        regex: knative-serving;controller;metrics
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service
    # Autoscaler endpoint
    - job_name: autoscaler
      scrape_interval: 3s
      scrape_timeout: 3s
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_pod_label_app, __meta_kubernetes_pod_container_port_name]
        action: keep
        regex: knative-serving;autoscaler;metrics
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service
    # Activator pods
    - job_name: activator
      scrape_interval: 3s
      scrape_timeout: 3s
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_pod_label_app, __meta_kubernetes_pod_container_port_name]
        action: keep
        regex: knative-serving;activator;metrics-port
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service
    # Webhook pods
    - job_name: webhook
      scrape_interval: 3s
      scrape_timeout: 3s
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_pod_label_role, __meta_kubernetes_pod_container_port_name]
        action: keep
        regex: knative-serving;webhook;metrics-port
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service
    # Queue proxy metrics
    - job_name: queue-proxy
      scrape_interval: 3s
      scrape_timeout: 3s
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_pod_label_serving_knative_dev_revision, __meta_kubernetes_pod_container_port_name]
        action: keep
        regex: .+;user-metrics
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service
    # Fluentd daemonset
    - job_name: fluentd-ds
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_label_app, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: knative-monitoring;fluentd-ds;prometheus-metrics
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service
    # Istio mesh
    - job_name: istio-mesh
      scrape_interval: 5s
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: istio-system;istio-telemetry;prometheus
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service
    # Istio Envoy
    # These are very noisy and not enabled by default.
    # - job_name: istio-envoy
    #   scrape_interval: 5s
    #   kubernetes_sd_configs:
    #   - role: endpoints
    #   relabel_configs:
    #   # Scrape only the the targets matching the following metadata
    #   - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
    #     action: keep
    #     regex: istio-system;istio-statsd-prom-bridge;statsd-prom
    #   # Rename metadata labels to be reader friendly
    #   - source_labels: [__meta_kubernetes_namespace]
    #     target_label: namespace
    #   - source_labels: [__meta_kubernetes_pod_name]
    #     target_label: pod
    #   - source_labels: [__meta_kubernetes_service_name]
    #     target_label: service
    # Istio policy
    - job_name: istio-policy
      scrape_interval: 5s
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: istio-system;istio-policy;http-monitoring
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service
    # Istio telemetry
    - job_name: istio-telemetry
      scrape_interval: 5s
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: istio-system;istio-telemetry;http-monitoring
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service
    # Istio pilot
    - job_name: istio-pilot
      scrape_interval: 5s
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: istio-system;istio-pilot;http-monitoring
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service
    # Kube API server
    - job_name: kube-apiserver
      scheme: https
      kubernetes_sd_configs:
      - role: endpoints
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        server_name: kubernetes
        insecure_skip_verify: false
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_label_component, __meta_kubernetes_service_label_provider, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: default;apiserver;kubernetes;https
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service
    # Kube controller manager
    - job_name: kube-controller-manager
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_label_app, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: knative-monitoring;kube-controller-manager;http-metrics
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service
    # Kube scheduler
    - job_name: kube-scheduler
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_label_k8s_app, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: kube-system;kube-scheduler;http-metrics
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service
    # Kube state metrics on https-main port
    - job_name: kube-state-metrics-https-main
      honor_labels: true
      scheme: https
      kubernetes_sd_configs:
      - role: endpoints
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      tls_config:
        insecure_skip_verify: true
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_label_app, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: knative-monitoring;kube-state-metrics;https-main
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service
    # Kube state metrics on https-self port
    - job_name: kube-state-metrics-https-self
      scheme: https
      kubernetes_sd_configs:
      - role: endpoints
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      tls_config:
        insecure_skip_verify: true
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_label_app, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: knative-monitoring;kube-state-metrics;https-self
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service
    # Kubelet - nodes
    # Rather than connecting directly to the node, the scrape is proxied though the
    # Kubernetes apiserver.  This means it will work if Prometheus is running out of
    # cluster, or can't connect to nodes for some other reason (e.g. because of
    # firewalling).
    - job_name: kubernetes-nodes
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      kubernetes_sd_configs:
      - role: node
      relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
      - target_label: __address__
        replacement: kubernetes.default.svc:443
      - source_labels: [__meta_kubernetes_node_name]
        target_label: __metrics_path__
        replacement: /api/v1/nodes/${1}/proxy/metrics
    # Kubelet - cAdvisor
    #
    # This is required for Kubernetes 1.7.3 and later, where cAdvisor metrics
    # (those whose names begin with 'container_') have been removed from the
    # Kubelet metrics endpoint.  This job scrapes the cAdvisor endpoint to
    # retrieve those metrics.
    #
    # In Kubernetes 1.7.0-1.7.2, these metrics are only exposed on the cAdvisor
    # HTTP endpoint; use "replacement: /api/v1/nodes/${1}:4194/proxy/metrics"
    # in that case (and ensure cAdvisor's HTTP server hasn't been disabled with
    # the --cadvisor-port=0 Kubelet flag).
    - job_name: kubernetes-cadvisor
      scrape_interval: 15s
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      kubernetes_sd_configs:
      - role: node
      relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
      - target_label: __address__
        replacement: kubernetes.default.svc:443
      - source_labels: [__meta_kubernetes_node_name]
        target_label: __metrics_path__
        replacement: /api/v1/nodes/${1}/proxy/metrics/cadvisor
    # Node exporter
    - job_name: node-exporter
      scheme: https
      kubernetes_sd_configs:
      - role: endpoints
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      tls_config:
        insecure_skip_verify: true
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_label_app, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: knative-monitoring;node-exporter;https
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service
    # Prometheus
    - job_name: prometheus
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      # Scrape only the the targets matching the following metadata
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_label_app, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: knative-monitoring;prometheus;web
      # Rename metadata labels to be reader friendly
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod
      - source_labels: [__meta_kubernetes_service_name]
        target_label: service

    - job_name: kubernetes-service-endpoints
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      - action: keep
        regex: true
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_scrape
      - action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        source_labels:
        - __address__
        - __meta_kubernetes_service_annotation_prometheus_io_port
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_service_label_(.+)
      - action: replace
        source_labels:
        - __meta_kubernetes_namespace
        target_label: kubernetes_namespace
      - action: replace
        source_labels:
        - __meta_kubernetes_service_name
        target_label: kubernetes_name
kind: ConfigMap
metadata:
  labels:
    name: prometheus-scrape-config
    serving.knative.dev/release: "v0.8.0"
  name: prometheus-scrape-config
  namespace: knative-monitoring

---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: kube-controller-manager
    serving.knative.dev/release: "v0.8.0"
  name: kube-controller-manager
  namespace: knative-monitoring
spec:
  clusterIP: None
  ports:
  - name: http-metrics
    port: 10252
    protocol: TCP
    targetPort: 10252
  selector:
    k8s-app: kube-controller-manager
  type: ClusterIP

---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: prometheus
    serving.knative.dev/release: "v0.8.0"
  name: prometheus-system-discovery
  namespace: knative-monitoring
spec:
  clusterIP: None
  ports:
  - name: web
    port: 9090
    protocol: TCP
    targetPort: web
  selector:
    app: prometheus
  sessionAffinity: None
  type: ClusterIP
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: prometheus-system
  namespace: knative-monitoring
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: prometheus-system
  namespace: default
rules:
- apiGroups:
  - ""
  resources:
  - nodes
  - services
  - endpoints
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: prometheus-system
  namespace: knative-monitoring
rules:
- apiGroups:
  - ""
  resources:
  - nodes
  - services
  - endpoints
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: prometheus-system
  namespace: kube-system
rules:
- apiGroups:
  - ""
  resources:
  - services
  - endpoints
  - pods
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: prometheus-system
  namespace: istio-system
rules:
- apiGroups:
  - ""
  resources:
  - nodes/metrics
  - nodes
  - services
  - endpoints
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: prometheus-system
  namespace: knative-monitoring
rules:
- apiGroups:
  - ""
  resources:
  - nodes/metrics
  - nodes
  - services
  - endpoints
  - pods
  - nodes/proxy
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
- nonResourceURLs:
  - /metrics
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: prometheus-system
  namespace: default
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: prometheus-system
subjects:
- kind: ServiceAccount
  name: prometheus-system
  namespace: knative-monitoring
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: prometheus-system
  namespace: knative-monitoring
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: prometheus-system
subjects:
- kind: ServiceAccount
  name: prometheus-system
  namespace: knative-monitoring
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: prometheus-system
  namespace: kube-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: prometheus-system
subjects:
- kind: ServiceAccount
  name: prometheus-system
  namespace: knative-monitoring
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: prometheus-system
  namespace: istio-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: prometheus-system
subjects:
- kind: ServiceAccount
  name: prometheus-system
  namespace: knative-monitoring
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: prometheus-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus-system
subjects:
- kind: ServiceAccount
  name: prometheus-system
  namespace: knative-monitoring
---
apiVersion: v1
kind: Service
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: prometheus-system-np
  namespace: knative-monitoring
spec:
  ports:
  - port: 8080
    targetPort: 9090
  selector:
    app: prometheus
  type: NodePort
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: prometheus-system
  namespace: knative-monitoring
spec:
  podManagementPolicy: Parallel
  replicas: 2
  selector:
    matchLabels:
      app: prometheus
  serviceName: prometheus-system
  template:
    metadata:
      labels:
        app: prometheus
        serving.knative.dev/release: "v0.8.0"
    spec:
      containers:
      - args:
        - --config.file=/etc/prometheus/prometheus.yml
        - --storage.tsdb.path=/prometheus
        - --storage.tsdb.retention=2d
        - --storage.tsdb.no-lockfile
        - --web.enable-lifecycle
        - --web.route-prefix=/
        image: prom/prometheus:v2.2.1
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 10
          httpGet:
            path: /-/healthy
            port: web
            scheme: HTTP
          initialDelaySeconds: 30
          timeoutSeconds: 3
        name: prometheus
        ports:
        - containerPort: 9090
          name: web
          protocol: TCP
        readinessProbe:
          failureThreshold: 10
          httpGet:
            path: /-/ready
            port: web
            scheme: HTTP
          timeoutSeconds: 3
        resources:
          limits:
            memory: 1000Mi
          requests:
            memory: 400Mi
        terminationMessagePath: /dev/termination-log
        volumeMounts:
        - mountPath: /etc/prometheus
          name: prometheus-config-volume
        - mountPath: /prometheus
          name: prometheus-storage-volume
      serviceAccountName: prometheus-system
      terminationGracePeriodSeconds: 600
      volumes:
      - configMap:
          defaultMode: 420
          name: prometheus-scrape-config
        name: prometheus-config-volume
      - emptyDir: {}
        name: prometheus-storage-volume
  updateStrategy:
    type: RollingUpdate

---
apiVersion: v1
kind: Service
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: zipkin
  namespace: istio-system
spec:
  ports:
  - name: http
    port: 9411
  selector:
    app: zipkin
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    serving.knative.dev/release: "v0.8.0"
  name: zipkin
  namespace: istio-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zipkin
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
      labels:
        app: zipkin
        serving.knative.dev/release: "v0.8.0"
    spec:
      containers:
      - env:
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        - name: STORAGE_TYPE
          value: elasticsearch
        - name: ES_HOSTS
          value: elasticsearch-logging.knative-monitoring.svc.cluster.local:9200
        - name: ES_INDEX
          value: zipkin
        - name: ZIPKIN_UI_LOGS_URL
          value: http://localhost:8001/api/v1/namespaces/knative-monitoring/services/kibana-logging/proxy/app/kibana#/
        image: docker.io/openzipkin/zipkin:2.13.0
        imagePullPolicy: IfNotPresent
        name: zipkin
        ports:
        - containerPort: 9411
        resources:
          limits:
            memory: 1000Mi
          requests:
            memory: 256Mi

---
