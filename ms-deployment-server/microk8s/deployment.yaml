#apiVersion: serving.knative.dev/v1alpha1
apiVersion: apps/v1
#kind: Service
kind: Deployment
metadata:
  name: coalesce-deployment
  namespace: cs-apps-staging
spec:
  selector:
    matchLabels:
      app: coalesce-deployment
  replicas: 1
  template:
    metadata:
      labels:
        app: coalesce-deployment 
    spec:
      containers:
      - name: coalesce-deployment
        image: localhost:32000/coalesce-deployment:latest
        ports:
        - containerPort: 5000
          name: grpc
