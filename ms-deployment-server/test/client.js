const protoPath = __dirname + '/../proto/deployment.proto'
const grpc = require('grpc')
const protoLoader = require('@grpc/proto-loader')

const port = process.env.PORT || 5002
const packageDefinition = protoLoader.loadSync(
	protoPath,
	{ keepCase: true, longs: String, enums: String, defaults: true, oneofs: true })

const protoDescriptor = grpc.loadPackageDefinition(packageDefinition)

const yargs = require('yargs')
	.usage('Usage: $0 [options]')
	.option( 'h', { alias: 'host', demand: false, describe: 'Host address', type: 'string', default: '127.0.0.1' } )
	.option( 'p', { alias: 'port', demand: false, describe: 'Host port', type: 'number', default: 5002 } )
	.option( 'u', { alias: 'url', demand: true, describe: 'Git URL', type: 'string' } )
	.option( 'r', { alias: 'revision', demand: false, describe: 'Git revision', type: 'string', default: 'master' } )
    .option( 'i', { alias: 'image', demand: true, describe: 'Image name', type: 'string' } )
    .option( 't', { alias: 'tag', demand: false, describe: 'Image tag', type: 'string', default: 'latest' } )
    .option( 'a', { alias: 'always_pull', demand: false, describe: 'Always pull image', type: 'boolean', default: true } )
	.help('help')
	.example('$0 -u https://github.com/petari21/app1.git -i petari/app1 -t build')
	.wrap(null)
	.version('1.0.0')
    .epilog('\251 2019. Concurrent Systems')
    
const argv = yargs.argv

const client = new protoDescriptor.Deployment(argv.host + ':' + argv.port, grpc.credentials.createInsecure())

const req = {
    source: { url: argv.url, revision: argv.revision },
    image_information: { name: argv.image, tag: argv.tag, always_pull: argv.always_pull}
}

client.buildAndDeploy(req, (err, r) => {
    if (err) {
        console.error('Encountered error:', err)
        return
    }

    console.log('Successful buildAndDeploy request with name:', r.name)
})