# Deployment Server Change Log - (versions 1.2.3 to 0.4)

### Technical Changes

- Updated to Node 14
- Invalid REST requests will return HTTP `501 Not Implemented`, instead of HTTP `200 Not Implemented`
- **IMPORTANT:** Deployment server will create zero-downtime configuration for all new deployments

### Bug Fixes

- Deploy images with unique tags to prevent conflicts between namespaces (environments)
- Deployment server no longer crashes and restarts when attempting to clean up stale builds
