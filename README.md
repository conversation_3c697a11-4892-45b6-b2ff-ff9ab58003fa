# CoaleSCE For Developers

USSD Service Creation and Delivery Environment

CoaleSCE is broken up into 6 microservices (described below), each of which interact with one another on a docker service.

- ### Maria Db
  - Used by the GUI to store the menu configurations and components!
- ### Redis
  - Used by the Preprod and the Prod menu services to store the USSD requests sessions.
- ### ETCD
  - Used by the GUI to deploy the menu configurations, components, USSD short codes etc.
  - Used by the Menu server to get the launched manu data
- ### GUI (Frontend and Backend) Services
  - Used for creating the menu and components
  - Used for deploying the menu
- ### User Managmeent Service
  - Used to store the users and permissions for the GUI users.
- ### Menu Server (Previewer, Preprod and Prod) services
  - Previewer Menu Service: Used to serve the GUI menu simulator/previewer requests
  - Prepord Menu Service: Used to serve the all Preprod USSD requests
  - Prod Menu Service: Used to serve the all production USSD requests


The goal of CoaleSCE is to allow customers to create custom USSD services. These services are almost limitless, in that you can configure a service to speak with other services, to communicate with the outside world, or other network services at-will. The reason for this power is that you can write actual `Javascript` code on the frontend that is run for any given menu service you create.

---

# High-Level Working Flow of USSD Menu Management System

The USSD Menu Management System enables the creation, configuration, and deployment of text-based USSD menus. Users interact with these menus via shortcodes (numeric or alphanumeric), which trigger specific responses. The system is designed to support development, preproduction (preprod), and production environments, ensuring smooth updates and deployment of USSD menus and their logic.

## High-Level Workflow

### 1. **User Authentication**

- The **User Management Service** is responsible for authenticating users in the GUI, allowing them to create, modify, and manage USSD menus.
  
### 2. **Menu Creation in the GUI**

- In the **GUI**, users create **USSD menus** by defining text-based prompts and possible responses, which are saved into a **MariaDB** database.
- The menus may include shortcodes that map to different menu options or next steps.
- No graphical buttons are involved. Instead, the menu presents textual options (e.g., “Press 1 for option A, Press 2 for option B”).

### 3. **Pushing Menu to Preproduction**

- After the USSD menu is created, the user can **push** the menu to the **Preproduction Environment** (Preprod).
- When the menu is pushed to Preprod, the **GUI Backend Server** generates a **Menu Configuration JSON** containing the textual menu options and any associated logic (e.g., next steps or actions based on user input).
- If the menu references custom components (e.g., custom USSD logic or scripts), the **GUI Backend Server** gathers the relevant **JavaScript code** for the custom logic.
- The **Menu Configuration JSON** and the **component JavaScript code** are stored in **ETCD**, a distributed key-value store.

### 4. **ETCD Synchronization**

- The **Preprod & Prod Menu Services** listens for changes in **ETCD** related to the Preprod environment.
- When the **Menu Configuration JSON** or component scripts are updated in ETCD, the **Menu Service** fetches the changes and stores them in the **in-memory application variable**.

### 5. **Assigning USSD Shortcodes**

- After pushing the menu to Preprod, the user can assign **USSD Shortcodes** to the menu in the GUI by going to the **Launchpad**. These shortcodes represent the keys that users will use to interact with the USSD menu.
- The **USSD Shortcode** assignment is saved into **ETCD**, and the **Menu Service** listens for updates. Once the shortcode is assigned, the Preprod service updates its in-memory variables with the new shortcode mappings.

### 6. **Deploying to Production**

- Once the Preprod menu is validated, users can **launch** the menu to **Production**.
- The menu is stored in **ETCD** under the **Prod key**, and the **Prod Menu Service** listens for changes.
- When the Prod service detects an update in ETCD, it loads the menu configuration and updates its **in-memory application variable**.

### 7. **Updating Menus**

- If changes are made to the menu (e.g., updates to options, shortcodes, or configurations), these changes are saved into **ETCD**.
- Both the **Preprod Menu Service** and **Prod Menu Service** monitor ETCD for updates for relelvant menue deployment i.e preprod or prod and immediately apply changes to the in-memory variables.

### 8. **Handling USSD Code Requests**

- To process **USSD Code Requests**, a POST request with a **formatted XML body** is sent to the menu server via the API path `/RPC2`.
- The **menu server** first checks if the requested **USSD shortcode** is registered.
  - If the shortcode is registered, the menu server processes the request.
  - The system checks **Redis** to see if a **session** exists for the given **MSISDN** (Mobile Subscriber Integrated Services Digital Network number). The session ID is linked to the MSISDN.
    - **If a session exists**, the session's **expiration time** is refreshed.
    - **If no session exists**, a new session is created in **Redis**.
- Each subsequent request from the same MSISDN will refresh the session’s expiration time until the user finishes the menu interaction.
- The session data is maintained until the user either completes the menu interaction or the session expires.

### 9. **Session Expiry**

- Sessions in **Redis** automatically expire after a certain period of inactivity (defined by the **session timeout**). If the user does not complete the menu interaction in time, the session will be cleared.
- The system ensures that each MSISDN has a unique session, and it manages session state throughout the user’s interaction.

---

## Workflow Diagram

```plaintext
+--------------------+          +--------------------+         +----------------------+
|                    |          |                    |         |                      |
|   User Management  |  ----->  |      GUI Backend   |  -----> |   ETCD (Menu & Config)|
|   Service          |  (Auth)  |   Server           |  (Save) |                      |
|                    |          |                    |         |                      |
+--------------------+          +--------------------+         +----------------------+
                                                          |
+--------------------+          +--------------------+  |
|                    |          |                    |  |
|    MariaDB         |  <-----> |     GUI Backend    |  |
|  (Menu Storage)    |          |                    |  |
|                    |          |                    |  |
+--------------------+          +--------------------+  |
                                                          |
+--------------------+          +--------------------+  |
|                    |          |                    |  |
|  Preprod Menu      |  <-----> |     Redis (Session)|  |
|  Service (Listen)  |          |                    |  |
|                    |  <-----> |                    |  |
+--------------------+          +--------------------+  |
                                                          |
+--------------------+          +--------------------+  |
|                    |          |                    |  |
|  Prod Menu         |  <-----> |     Redis (Session)|  |
|  Service (Listen)  |          |                    |  |
|                    |          |                    |  |
+--------------------+          +--------------------+  |
                                                          |
+--------------------+          +--------------------+  |
|                    |          |                    |  |
| USSD Request (API) |  ----->  |  Preprod/Prod      |  |
| /RPC2 Endpoint     |   (Process)|  Menu Server      |  |
|                    |          |                    |  |
+--------------------+          +--------------------+  |
```

---

## Key Components

- **ETCD**: Stores and manages menu configurations, shortcodes, and component scripts.
- **MariaDB**: Stores all USSD menu definitions and associated metadata.
- **Preprod & Prod Menu Services**: These services run the menus in the **preproduction** and **production** environments. They listen for updates in **ETCD** and update the in-memory application variables when changes are detected.
- **Redis**: Stores and manages user **sessions** during USSD interactions.
- **GUI Backend Server**: Handles the generation of the **Menu Configuration JSON** and stores it into **ETCD** after user deployment actions.
- **USSD Shortcodes**: Numeric or alphanumeric codes that users dial to access specific USSD menus.
- **Menu Configuration JSON**: Defines the structure and logic of a USSD menu.



This high-level workflow ensures seamless management of **USSD menus** across Preprod and Prod environments using **MariaDB, ETCD, and Redis**, enabling real-time updates and interaction processing.


---

## NODE and NPM

NODE and NPM versions used for CoaleSCE products at the time of writing this document (2024-08-21)

```
$ node -v
v14.21.3

$ npm -v
6.14.18
```

If you are running incompatible versions, it is best to clear cache, and install the correct NPM version first:

```
$ npm cache clean --force
$ npm install -g npm@6
```

Thereafter re-install the node version (We use the Node Version Manager, as it's significantly easier working between versions: `nvm`):

```
nvm uninstall 14
nvm install 14
```

## Prerequisites to Running Coalesce

Both GUI and Menu Server (possibly other services too) need the following services to be running and available or they will not start (successfully).

- Maria Db
- Redis
- You have a running ETCd service, as per the configuration in this file `coalesce-gui/data/config/defaults.json` (see [Ephemeral ETCD Environment](#ETCD-Docker-Environment-for-Development) below)



### Docker Environment for All Services

All of the services requried envoirnments are set into the `/.env` file


---


# Setup Guide

Instructions on how to set up and use the USSD Menu Services.

## Running the Services

To run all the services, execute the following command:

```bash
docker compose up --build -d
```

### First-Time Setup

If this is your first time running the `docker compose build` command, follow these steps:

1. Wait for **20 seconds** after all services are running successfully.
2. Stop all services.
3. Run the `docker compose up -d` command.

This restart is required during the first run to ensure proper creation of database tables and ETCD configurations.

## Accessing the GUI

Once all services are running successfully, the GUI can be accessed at:

- **URL**: [http://localhost:5000](http://localhost:5000)
- **Credentials**:
  - **Email**: `<EMAIL>`
  - **Password**: `er1c$$on`

The GUI allows you to launch menus for **Preproduction** and **Production** environments.


## Sending USSD Menu Requests
Once you launched the module and assign the USSD code to it, then you can send the requests to the menu server.

### Preproduction Menu Server

- **URL**: [http://localhost:5007/RPC2](http://localhost:5007/RPC2)

### Production Menu Server

- **URL**: [http://localhost:5003/RPC2](http://localhost:5003/RPC2)

### Request Format

To send a USSD menu request, use an **HTTP POST** request with an XML body.


## Sample XML Request

```xml
<?xml version="1.0" encoding="utf-8"?>
<methodCall>
  <methodName>handleUSSDRequest</methodName>
  <params>
    <param>
      <value>
        <struct>
          <member>
            <name>TransactionId</name>
            <value><string>1398590536</string></value>
          </member>
          <member>
            <name>TransactionTime</name>
            <value><dateTime.iso8601>20250110T13:10:19</dateTime.iso8601></value>
          </member>
          <member>
            <name>MSISDN</name>
            <value><string>22899396764</string></value>
          </member>
          <member>
            <name>USSDServiceCode</name>
            <value><string>123</string></value>
          </member>
          <member>
            <name>USSDRequestString</name>
            <value><string>1</string></value>
          </member>
        </struct>
      </value>
    </param>
  </params>
</methodCall>
```

- **MSISDN**: The MSIDN number
- **USSDServiceCode**: The USSD code to which the request is sent.
- **USSDRequestString**: The user input command.

---

## GUI

The GUI is what the user sees, it allows a user to create a USSD service (called a Menu Module). It usually starts with a Menu, hence the name.

Each Menu Module is deployed as config data into the ETCD and menu server will auto get the updates about the deployment, the menu server that listens for USSD requests and then responds with a menu based on the internal logic and results of the components. It is capable of incorporating variables, and also `javascript` "components" which facilitate additional power for the respective Menu Module.

Once created, the GUI allows you to "deploy" any Menu Module and assign a short code to it, this is a "development" deployment. After a development deployment, you can "Launch" the same service into production with another Short Code so that it's accessible to subscribers on the MNO network. The MNO is the one who decides which short codes are available to which subscribers and for what deployments, and configures this routing outside of CoaleSCE.

The Menu Module deployments are saved in ETCD.

### Running the GUI

GUI Prerequisites (don't forget [Prerequisites to running CoaleSCE](#Prerequisites-to-Running-Coalesce)):
- You have a `MariaDB/MySQL` database with the database **ALREADY CREATED** as per the details in the below config file

```
$ cd coalesce-gui
$ npm i
```

#### Starting the GUI Backend and Frontend

Assuming you have the prerequisites met...

There are 2 parts to the GUI, the GUI Backend (BE), and the GUI Frontend (FE).

##### The BE is run as follows (starts on default port 5000):

First time run (**sets up ES and ETCd for us**):
```
$ npm run start-dev
...
...
...
[2024-08-21T14:46:39.059] [INFO]   ES LFC | Completed Initialisation of Elasticsearch Lifecycle Policies
```


Subsequent runs AFTER the first time:
```
$ npm run start-dev
...
...
...
[2024-08-21T14:49:03.978] [INFO]   ES LFC | "istio" Policy and Index Template exists
[2024-08-21T14:49:03.979] [INFO]   ES LFC | Affected indexe(s): istio*
[2024-08-21T14:49:03.979] [INFO]   ES LFC | Deleted after days: 7
[2024-08-21T14:49:03.979] [INFO]   ES LFC | Completed Initialisation of Elasticsearch Lifecycle Policies

---------------- IT'S POSSIBLE THESE FOLLOWING LINES WILL BE AFTER (they're run async)

[2024-08-21T14:49:03.990] [DEBUG]  === etcd Config === Found endpoint data
[2024-08-21T14:49:03.994] [DEBUG]   Retrieved current endpoint data
[2024-08-21T14:49:03.998] [DEBUG]  Database table 'global_settings' exists, updating (as needed).
```

##### The FE is run as follows (dynamic port set in the .env file):

You should be in the same folder as the BE `./coalesce-gui`, and the *Menu Server should already be started*:

> NOTE: This method starts the GUI for you, it opens a browser witht he GUI FE in it on the appropriate port (courtsey of Vue Serve)

```
$ npm run client

##
## IMPORTANT .... start the `menu-server` FIRST,
```

##### Known GUI FE Issue

When you first login, you login as `Admin`, but you will likely create a user for yourself a CAD ... otherwise you can't edit menu modules or make changes. So go ahead and create one...

- The INVITE page does not provide feedback when creating a user... i.e. it's created, but no visual response on the page.
- The INVITE page can be navigated to manually by a CAD, even though they don't have access to invite users `/invite`
- Logging out retains the `?redirect=....` in the URL, meaning switching users with differing permissions lands you on a `403` if they are not authorized for that page
- LaunchPAD on the GUI, gets information from deployment service, and by extension from kubernetes - which in DEV mode is generally not accessible. See `coalesce-gui/server/routes/launchpad.js`, the gRPC client toward the deployment service needs the deployment service running (requiring access to k8s as well).
- The GUI does not log the user out when it receives `401` for any asynchronous requests to the GUI backend, this means that a page refresh logs them out - what should happen, is the VueX Store should trigger a logout when any given request fails with "401 Unauthorized".
- When ADDING a user, if you don't use a valid email AND a valid SMTP server in the config file, then you can't get the *invitation* that is sent... So, you have to manually update the password (plaintext) in the user management database, for the respective database (default: `users`), it is `UPDATE users SET password='plaintext-password' WHERE email='<<EMAIL>>';`



### Components

Components in the GUI are designed to be run in the flow of the menu, for example, in the root of the menu, you could do a `GetBalanceAndDate` UCIP call to determine if the subscriber has sufficient balance to purchase any of the relevant bundles... Then you can display or hide parts of the menu based on this information.

Components are powerful pieces of javascript code that are created in the GUI and can be used with any menu module. Components make use of a `vars` and `ctx` global variable (*Context*) which are available to all components. This `ctx` stores information about the transaction currently under way, like `ctx.request.MSISDN` for the originating subscriber number. The `vars` stores variables available within the Menu Module that is making use of the respective component.

All components have access to libraries with `require(...)`, available libraries are found in the **MENU SERVER** here: `coalesce-menu-server/common/@coalesce/`

An example component:

```
/**
 * `ctx` and `vars` are available on the global scope of a component
 */

  /**
   * An example of the @coalesce library with a limited set of UCIP calls
   *   (check library in menu server code to see what limits)
   */
  const ucip = require("@coalesce/ucip");

  const params = {
    subscriberNumber: String(ctx.request.MSISDN),
    requestedInformationFlags: {
      requestMasterAccountBalanceFlag: true,
    },
    requestPamInformationFlag: true,
  };

  let response = null;

  try {
    response = await ucip.getAccountDetails(params, ctx);
  } catch (error){
    /**
     * RETHROW errors, important as it will prevent further processing when external connections fail, etc
     */
    throw new Error("= Air failed with exception: ");
  }

  console.trace("=== AIR Response ===\n", JSON.stringify(response));

  /**
   * Normal error handling, UCIP can return "successfully" but still have a responseCode with an error...
   */
  if (response.responseCode >= 100) {
    /**
     *
     */
    throw new Error(`= Air failed with code: ${String(response.responseCode)}`);
  }

  /**
   * variables available on the UCIP getAccountDetails call found in the `@coalesce/ucip` library
   *
   *  Notice, we assign to `vars` so they are available to later components or inside the Menu Module
   */
  vars.serviceclass = response.serviceClassCurrent;
  vars.languageID = response.languageIDCurrent;
  vars.language = vars.langmap[vars.languageID];
  vars.accountValue = response.accountValue1;
  vars.currency1 = response.currency1;
}
```

### Built-in Components

SOME **Build-in Components** are available to the GUI, see [Menu Server](#Menu-Server) section below.


## Menu Server

This is the "meat" of the CoaleSCE Product, all Menu Modules run by means of this, but it is also used to *test* menus directly from the GUI using the "Previewer".

The Menu server basically takes the JSON which is generated by the GUI, and uses it to import components, to display menu levels, and to go back and forth in the menu between menu levels. It controls the entire processing of a USSD menu with it's associated components.

The menu server has 2 code paths ... as seen here in `coalesce-menu-server/server/Server.js`:


```

const HTTP_SERVER_PATH = "/RPC2";                      // URL Path when communicating with the Menu Server with a USSD XMLRPC request
const HTTP_SERVER_PORT = process.env.PORT || 8080;     // On this port...

...
...

class Server {

...
...

  async start() {
    return new Promise(async (resolve, reject) => {
      try {

...
...

        if (isPreviewer) {
          previewerDebugger.start(); /// This path is taken if the request to test the menu comes from the GUI (Previewer)
        } else {
          ...
          ... /// This path is taken if the request comes as USSD XMLRPC
          ...
        }
```

The above is important to note, as it can cause bugs to show up only during USSD testing, rather than testing in the GUI.

### Running the Menu Server

```
$ cd coalesce-menu-server
$ cp .env.example .env           # There are environment variables here that *override* defaults
$ npm i
$ npm run start:dev
```

***IMPORTANT***

There are 2 ways to run Menu Server, (1) Such that it accepts XMLRPC requests with USSD (USSD request path is `/RPC2`), or (2) so that you can accept GUI requests to run in the 'previewer':

- Menu Server to accept XMLRPC requests:
```
$ cat .env | grep -v "^#" | grep CS_MENU_ENVIRONMENT
CS_MENU_ENVIRONMENT=preprod
```

- Menu Server to accept JSON requests from the GUI Previewer/Debugger:
```
$ cat .env | grep -v "^#" | grep CS_MENU_ENVIRONMENT
CS_MENU_ENVIRONMENT=previewer
```

### Built-in Components

Found in `coalesce-menu-server/server/components`, these components (not all) are made available to the GUI, they are "generic" components, but they are not the same as the GUI constructed components.

The visibility of 'built in' components to the GUI is decided in this file `coalesce-gui/server/routes/component.js`, if a component is manually added to the list AFTER the database retrieval (as seen with `common_properties` and others) then only is it visible on the GUI....

```
# Example of the MANUAL INCLUSION of a built-in component, if it is not added like this, it will not be visible in the GUI

    compList.push({
      code: '',
      description: '',
      id: -999,
      name: 'common_properties',
      key: 'common_properties',
      permission: 'private',
      shortcode: '',
      type: 'builtin',
      version: 1,
    });
```

> TAKE NOTE: If you include a BUILT IN component that doesn't exist in the menu-server, then likely this will just crash when importing that file...

Built In components include all the *scaffolding code* that is omitted in the GUI. In the GUI, only the contents of the `call` function are visible, not even the function wrapper itself. Whereas built in components are complete JS files and are not exposed or editable for users of the GUI.


### Error Handling

During a drive to improve error handling, we incorporated the `coalesce-menu-server/common/@coalesce/coalesceError.js` class. It's use can be seen throughout the menu server, please extend it and use existing examples to continue with improved error handling in the menu server.


## User Management

A Microservice that controls the user management for the GUI (**using gRPC as the API between them**), it uses JWT authentication. It simply stores roles/permissions that allow the users of the GUI to work with it. There are 2 primary permissions, and we have not enabled the ability to create more (though this may or may not change in the future). "CoaleSCE Admin" and "CoaleSCE App Developer", the Admin can do everything EXCEPT development deployment of Menu Modules, and the App Developer can write and deploy modules, but cannot "Launch" to production.

The User Management Microservice (sometimes referred to as UMMS for short), creates the database and tables if they do not exist in *MySQL/MariaDB*. If they do exist, then all "upgrading" is dont programatically (i.e. IN THE CODE) --- but NO VERSION NUMBER is used for this, which was an oversight. Keep this in mind, it means you would have to "evaluate" the current layout and decide if it needs to be updated or not. 

For the default configuration (if not overridden by environment variables), see `user-management/server/config/local.json`.

For the first time when the database is created, the FIRST user `<EMAIL>` uses a PLAINTEXT password directly injected into the database. Because UMMS compares hashed passwords, we decided to have a "once off" action , IF the password is PLAINTEXT and it matches the PLAINTEXT password in the database, then the password in the database is **updated** to it's HASHED form. This prevents the possibility of UNHASHED/PLAINTEXT passwords from remaining in the database. This step is performed ONLY on login. The password for the FIRST TIME login is `er1c$$on` - and **should be changed as soon as possible after your first login**.

### Running the UMMS

User Management Server Prerequisites (don't forget [Prerequisites to running CoaleSCE](#Prerequisites-to-Running-Coalesce)):
- You have a `MariaDB/MySQL` database connection available, you *DO NOT* need to create the database, see above config to specify the connection info and starting database (which will be auto-created if it does not exist)


```
$ cd user-management
$ npm i
$ npm run start
```

### Known Issues

Problem with token not being dynamically generated ... see `user-management/server/grpc/index.js` , look for **FIXME** line. The fix for this has already been implemented, however it has not been tested, so it is not enabled...

See other **FIXME** lines in the same file.