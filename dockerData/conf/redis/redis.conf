# Redis Configuration File

# Enable AOF persistence
appendonly yes

# Append logs to disk every second
appendfsync everysec

# Enable snapshot persistence along with AOF for redundancy
save 120 1   # Save a snapshot if at least one key changes in specific seconds

# Set max memory usage to avoid resource overuse (optional)
maxmemory 512mb
maxmemory-policy allkeys-lru

# Bind to all interfaces
bind 0.0.0.0

# Port for Redis service
port 6379