## Release Summary
| Property     | Value          |
| ------------ | -------------- |
| Product      | [[CoaleSCE]]   | 
| Release Date | TBD |
| Version      | 0.5.0        |
| Build Number | COMMIT-HASH-TBD |
| Status       | #final         |

## Enhancement

#### Menu Server Core
- `updateOffer` added to built in @coalesce/ucip.js library
- Support for USSD Length limit relaxation through component configuration


## General &amp; Bug Fixes

#### Menu Server Core
- Added socket error handling and additional debug logging
- Added node garbage collection for improved performance

#### GUI
- Fixed crashing bug on startup that required K8s to be available although it is not a dependency
- Previewer no longer fails a valid text response if it is not JSON, just returns it as a string

#### MS Deployment server
- Now logs if there is no build status found
- Removed subresources causing problems in k8s and tekton

#### User Management MS
- Fixed initial database creation script to correctly create the ROLE for the first admin
