## Release Summary
| Property     | Value          |
| ------------ | -------------- |
| Product      | [[CoaleSCE]]   | 
| Release Date | TBD |
| Version      | 0.5.2        |
| Build Number | COMMIT-HASH-TBD |
| Status       | #final         |

## General &amp; Bug Fixes

### Menu Server
- When building Components in the GUI and using try/catch blocks. Take Note:
  - Errors in the catch block of methods generated from `@coalesce/*` libraries will be of type `CoalesceError`, which means that there is a parameter within called `innerError` of type `extends Error|null`. It should be noted, some CAD components have assumed that `innerError` will _always_ be of type `extends Error`, which is not the case, it is quite possible and expected that it will at times be `null` (and has been this way since CoaleSCE `0.4.1`). Therefore CAD should always validate the type before proceeding.

For example, code that has a high potential to fail looks like this:

```
try { ... } catch(error) {
    // Will throw again if `innerError` is `null`
    console.log(error.innerError.code);
}
```

To correct this, we must validate `innerError` to ensure it is not null, like this:

```
try { ... } catch(error) {
    // Note the optional chaining operator `?.` 
    // Now if `innerError` is null, the result will be treated as `undefined` and will not throw an exception
    console.log(error.innerError?.code);
}
```

#### CAD Library Changes

##### @coalesce/xmlrpc
- Now adds `isXMLRPCFault:bool` to the response object for `parseResponse(...)` method

##### @coalesce/ucip
- Can now throw an `XMLRPCFaultException` which contains the `faultCode:number` and `faultString:string`

##### @coalesce/request
- When returning the GET or POST responses (`Promise.resolve(response.data)`), it now includes the axios status & statusText alongside the data:
  - Like this: `Promise.resolve({ status, statusText,  ...response.data })`
- Now ALWAYS throws a `ComponentRequestException` when axios comes back with a Network or HTTP >= 400 errors
  - The `ComponentRequestException` will contain the full axios response in the parameter called `response:any`, as well as:
    - `isAxiosError:bool`
    - `code:number|undefined`
    - `status:number|undefined`
    - `statusText:string|undefined`
    - The reason why some above may be `undefined`, is for network errors like TIMEOUT or ECONNRESET (etc) that may not provide a status or statusText

##### @coalesce/crediverse
- When HTTP 404 is now returned for the get agent details request, it ONLY returns `{code: 404}` to the CAD, it no longer returns the rest of the response body that came back from Crediverse (though it will be logged).
- When logging the END EXT line for requests, it now handles all possible HTTP status & statusText, not only 404 as was previously the case
