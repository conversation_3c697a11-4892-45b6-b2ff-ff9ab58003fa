## Release Summary
| Property     | Value          |
| ------------ | -------------- |
| Product      | [[CoaleSCE]]   | 
| Release Date | TBD |
| Version      | 0.5.1        |
| Build Number | COMMIT-HASH-TBD |
| Status       | #final         |

## General &amp; Bug Fixes

### Menu Server Core
- Removed unused socket connection debug logging
- Removed manual NodeJS garbage collection
- Improved logging - see details below
- Added environment toggle to allow switching to HTTP/2 express server (no change to the default using HTTP/1 express server)
- Reset timeouts back to default values (5s keepAliveTimeout, 60s headersTimeout), can use environment to change it
 - Envinroment variables are: `EXPRESS_HEADERS_TIMEOUT`, `EXPRESS_KEEP_ALIVE_TIMEOUT`
 - min value 5, max value 60, if keepAlive is more than headers, then they will be made equal
- FIX potential crash where setting an invalid log level may potentially have crashed the Menu Server
- FIX Gitea login, now uses uname/pword (HTTP Basic Auth), rather than API key, which is the same as the Deployment Server (prevents the need for special API key handling in the k8s environment)

#### Built In Components


- *hux_call* - Now logs (INFO) on entry and exit of call with all relevant request info and end to end duration

#### Libraries

- Log (INFO) on entry and exit of call with all relevant request info and end to end duration
 - *@coalesce/crediverse*
 - *@coalesce/hux*
 - *@coalesce/ucip*
