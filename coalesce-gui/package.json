{"name": "@csys/coalesce-gui", "version": "0.1.1-alpha.0", "author": "Concurrent Systems", "license": "UNLICENSED", "description": "Coalesce Studio GUI", "repository": {"type": "git", "url": "**************:csys/products/coalesce-studio/coalesce-gui.git"}, "config": {"docker": {"image_name": "docker.csys.eu.org/coalesce-gui-server", "file": "docker/Dockerfile"}}, "scripts": {"printenv": "env", "rimraf": "./node_modules/rimraf/bin.js", "dev": "npm run server | npm run vue", "debug": "node ./bin/www", "build": "rimraf dist && vue-cli-service build && copyfiles 'server/**/*.js' dist && copyfiles 'bin/www' dist && copyfiles 'conf/**' dist", "dev-start": "npm run build && node ./bin/www --config ./conf/config-dev.js", "start": "node ./bin/www --config ./conf/config-docker.js", "start-dev": "node ./bin/www --config ./conf/config-dev.js", "client": "./node_modules/.bin/vue-cli-service serve --port 8081 --host 0.0.0.0", "lint": "eslint --project . --fix", "lintfix": "vue-cli-service lint --fix", "testold": "npm run test:unit && npm run test:e2e", "test": "npm run test:unit", "test-dev": "vue-cli-service test:unit --watch", "test:unit:debug": "node --inspect-brk ./node_modules/jest/bin/jest.js --runInBand", "test:e2e": "vue-cli-service test:e2e", "test:unit": "vue-cli-service test:unit --passWithNoTests --reporters='default' --reporters='jest-html-reporter'", "predocker-build": "npm pack && shx cp csys-coalesce-gui-${npm_package_version}.tgz tmp/", "docker-build": "docker image build --force-rm -t ${npm_package_config_docker_image_name}:${npm_package_version} -t ${npm_package_config_docker_image_name}:latest -t ${npm_package_config_docker_image_name}:staging -f ${npm_package_config_docker_file} --build-arg COALESCEGUI_VERSION=${npm_package_version} docker", "docker-push": "npm run docker-build && docker push ${npm_package_config_docker_image_name}:latest && docker push ${npm_package_config_docker_image_name}:${npm_package_version}"}, "dependencies": {"@elastic/elasticsearch": "^7.8.0", "@fortawesome/fontawesome-free": "^5.13.0", "@fortawesome/fontawesome-svg-core": "^1.2.32", "@fortawesome/free-brands-svg-icons": "^5.15.1", "@fortawesome/free-regular-svg-icons": "^5.15.1", "@fortawesome/free-solid-svg-icons": "^5.15.1", "@fortawesome/vue-fontawesome": "^2.0.0", "@grpc/proto-loader": "^0.6.2", "@kubernetes/client-node": "^0.14.3", "@riophae/vue-treeselect": "^0.4.0", "argparse": "^1.0.10", "atob": "^2.1.2", "axios": "^0.21.1", "base64-arraybuffer": "^0.2.0", "body-parser": "^1.19.0", "bootstrap": "^4.4.1", "bootstrap-vue": "^2.9.0", "connect": "^3.7.0", "cookie-parser": "^1.4.5", "core-js": "^3.6.4", "cors": "^2.8.5", "debug": "^4.1.1", "dotenv": "^8.2.0", "etcd3": "^1.0.1", "express": "^4.17.1", "express-fileupload": "^1.1.6", "express-mysql-session": "^2.1.6", "express-session": "^1.17.0", "fast-safe-stringify": "^2.0.7", "file-saver": "^2.0.5", "fs": "0.0.1-security", "fs-extra": "^9.0.0", "grpc": "^1.24.10", "http-errors": "^1.7.3", "http2": "^3.3.7", "idle-vue": "^2.0.5", "ioredis": "^4.28.5", "js-cookie": "2.2.1", "jsonwebtoken": "^8.5.1", "knex": "^0.95.6", "lodash": "^4.17.21", "log4js": "^6.3.0", "moment": "^2.24.0", "monaco-editor": "^0.20.0", "monaco-editor-webpack-plugin": "^1.9.0", "monaco-themes": "^0.3.3", "mysql": "^2.18.1", "nano-assign": "^1.0.1", "nanoid": "^3.0.0", "node-sass": "^4.14.1", "nodemailer": "^6.6.1", "normalize.css": "8.0.1", "nprogress": "0.2.0", "passport": "^0.4.1", "passport-local": "^1.0.0", "path-to-regexp": "6.1.0", "popper.js": "^1.16.1", "sass-resources-loader": "^2.1.1", "serve-favicon": "^2.5.0", "shelljs": "^0.8.4", "simple-git": "^1.132.0", "sinon": "^9.0.2", "socket.io": "^4.1.2", "socket.io-client": "^4.1.2", "socksjs": "^0.5.0", "uuid": "^7.0.2", "v-tooltip": "^2.0.3", "vee-validate": "^3.2.5", "vue": "^2.6.12", "vue-animate-number": "^0.4.2", "vue-class-component": "^7.2.6", "vue-context-menu": "^2.0.6", "vue-count-to": "1.0.13", "vue-lazyload": "^1.3.3", "vue-property-decorator": "^8.4.1", "vue-router": "^3.5.1", "vue-toastification": "^1.0.0", "vuelidate": "^0.7.5", "vuetable-2": "^2.0.0-beta.4", "vuex": "^3.6.2", "websocket": "^1.0.32", "ws": "^7.5.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@semantic-release/gitlab-config": "^8.0.0", "@vue/cli-plugin-babel": "^4.2.3", "@vue/cli-plugin-eslint": "^4.2.3", "@vue/cli-plugin-unit-jest": "^4.5.9", "@vue/cli-service": "^4.2.3", "@vue/eslint-config-prettier": "^6.0.0", "@vue/test-utils": "^1.1.1", "autoprefixer": "^9.7.5", "babel-core": "7.0.0-bridge.0", "babel-eslint": "^10.1.0", "babel-jest": "^25.2.3", "chalk": "3.0.0", "chokidar": "3.3.1", "copyfiles": "^2.2.0", "cross-env": "^7.0.2", "eslint": "^7.8.0", "eslint-config-airbnb-base": "^14.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-decorator-position": "^5.0.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-vue": "^6.2.2", "html-webpack-plugin": "4.0.2", "husky": "^4.2.3", "jest-html-reporter": "^3.10.2", "lint-staged": "^10.0.9", "nodemon": "^2.0.4", "prettier-eslint": "^16.1.2", "rimraf": "^3.0.2", "sass-loader": "^7.3.1", "script-ext-html-webpack-plugin": "^2.1.4", "script-loader": "0.7.2", "semantic-release": "^17.4.4", "serve-static": "^1.14.1", "shx": "^0.3.2", "svg-sprite-loader": "4.2.1", "svgo": "1.3.2", "vue-template-compiler": "^2.6.12"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/prettier"], "rules": {"no-console": "off", "no-debugger": "off", "no-unused-vars": "off", "indent": ["error", 2]}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions"], "jest": {"reporters": ["default", ["./node_modules/jest-html-reporter", {"pageTitle": "Coalesce Studio GUI Test Report"}]], "moduleFileExtensions": ["js", "jsx", "json", "vue"], "transform": {"^.+\\.vue$": "vue-jest", ".+\\.(css|styl|less|sass|scss|svg|png|jpg|ttf|woff|woff2)$": "jest-transform-stub", "^.+\\.jsx?$": "babel-jest"}, "transformIgnorePatterns": ["/node_modules/"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1"}, "snapshotSerializers": ["jest-serializer-vue"], "testMatch": ["**/tests/unit/**/*.spec.(js|jsx|ts|tsx)|**/__tests__/*.(js|jsx|ts|tsx)", "<rootDir>/src/views/**/__tests__/*.(js|jsx|ts|tsx)", "<rootDir>/src/components/**/__tests__/*.(js|jsx|ts|tsx)"], "testURL": "http://localhost/", "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "collectCoverage": true}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,vue}": ["vue-cli-service lint", "git add"]}}