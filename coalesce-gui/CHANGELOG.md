# GUI Change Log - (versions 0.3 to 0.4)

## Preliminary Notes

All modules will need to be PUSH/LAUNCH'ed.

### Features

- UCIP Endpoints have become 'Endpoint Connection Pool's. They allow the Admin to add multiple connections to an endpoint pool, the active connection will _only be cycled if it fails_.

### Technical Changes

- When components throw errors in the menu server, the `line number` specified now **matches** the line number in the respective GUI component (it was previously offset by +3).
  - The GUI is responsible for 'wraping' the code sent to the menu server which caused the offset, so the fix happened in the GUI wrapping code.

<br />
<br />
<br />
<br />

# GUI Change Log - (versions 1.2.3 to 0.3)

## Preliminary Notes

All modules will need to be PUSH/LAUNCH'ed.

### Technical Changes

- Updated to Node 14

### Bug Fixes

- Menu Modules "Add Menu Item" dialogue no longer strips newlines from languages then the `Item ID` is updated
- Corrected _name_ validation issues when creating new components
- **NOTEWORTHY:** PUSH and Launched menu modules now use a "tag" for the current Menu Server version instead of `latest`.
