== Project setup

First, the vue CLI. 
*NOTE:* If this fails with a root permission issue, google how to setup NPM to work without `root` rather than running the command with `sudo ...`
----
npm install -g @vue/cli
----

Install the Concurrent npm registry (don't forget to put the correct token in)
----
mv ~/.npmrc ~/.npmrc.orig
echo "@csys:registry=https://nexus.csys.eu.org/repository/csys-npm/
//nexus.csys.eu.org/repository/csys-npm/:_authToken=NpmToken.<NPM-TOKEN-HERE>" > ~/.npmrc
If you don't have a token, login with `npm login --registry=https://nexus.csys.eu.org/repository/csys-npm/` and use your IPA username and password
----

Then finally, install dependencies
----
npm install
----

== Run GUI as if running in production
----
To run the production version of the GUI, use `npm run dev-start`
----

== Releasing
----
## create rc
semver -i prerelease --preid rc "$(jq -r .version package.json)"
npm version --preid=rc prerelease && npm publish
## after 
semver -i preminor --preid beta "$(jq -r .version package.json)"
npm version --preid=beta preminor && npm publish

## create next beta
semver -i prerelease --preid beta "$(jq -r .version package.json)"
npm version --preid=beta prerelease && npm publish

## create next alpha
semver -i prerelease --preid alpha "$(jq -r .version package.json)"
npm version --preid=alpha prerelease && npm publish
----

== Compiles and hot-reloads for development
This runs the webpack development server which will automatically update the page contents for you.  Point your browser to http://localhost:8081/ once webpack is running.

----
npm run dev
----

== Compiles and minifies for production

----
npm run build
----

== Run your tests
----
npm run test
----

== Lints and fixes files
----
npm run lint
----

== Run your end-to-end tests
----
npm run test:e2e
----

== Run your unit tests
----
npm run test:unit
----
