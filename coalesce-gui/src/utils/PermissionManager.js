export default class PermissionManager {
  constructor(userPermissions) {
    if (!Array.isArray(userPermissions)) {
      throw new Error('Invalid userPermissions array in request');
    }
    this._flattenPermissions(userPermissions);
  }

  _flattenPermissions(permissions) {
    this.userPermissions = [];
    permissions.forEach(groupedPermission => {
      if (groupedPermission.permissions && Array.isArray(groupedPermission.permissions)) {
        groupedPermission.permissions.forEach(p => {
          this.userPermissions.push(groupedPermission.group + p);
        });
      }
    });
    this.userPermissions = [...new Set(this.userPermissions)]; // remove duplicates
  }

  hasPermission(permissionObject) {
    return this.hasOne([permissionObject]);
  }

  hasOne(permissionObjectArray) {
    if (!Array.isArray(permissionObjectArray)) {
      throw new Error('Parameter must be array of permission Objects for method hasOne(Parameter).');
    }

    const has = permissionObjectArray.findIndex(permissionObject => {
      const { can, a, an, all, forGroup, any } = permissionObject;
      const permission = can;
      // one to be used at a time ::: a|forGroup|any|all
      // get the non-empty one
      const group = a || an || all || any || forGroup || '';
      if (!can || !group) {
        throw new Error("Invalid 'can' permission or a group parameter 'a', 'an', 'all', 'forGroup', or 'any' - in the permissionObject.");
      }

      return this.userPermissions.includes(group + permission);
    });

    return has !== -1;
  }

  hasAll(permissionObjectArray) {
    if (!Array.isArray(permissionObjectArray)) {
      throw new Error('Parameter must be array of permission Objects for method hasOne(Parameter).');
    }

    let hasAll = true;

    permissionObjectArray.forEach(permissionObject => {
      const { can, a, all, forGroup, any } = permissionObject;
      const permission = can;
      // one to be used at a time ::: a|forGroup|any|all
      // get the non-empty one
      const group = a || all || any || forGroup || '';
      if (!can || !group) {
        throw new Error("Invalid 'can' permission or a group parameter 'a', 'all', 'forGroup', or 'any' - in the permissionObject.");
      }

      const hasPermission = this.userPermissions.includes(group + permission);

      if (hasAll === true && !hasPermission) {
        hasAll = false;
      }
    });

    return hasAll;
  }

  can(permission) {
    this._permission = String(permission);

    // have set permission ... if group is also set - return validation
    if (this._group && this._group.length !== 0) {
      return this.hasOne({ can: this._permission, forGroup: this._group });
    }

    return this;
  }

  all(group) {
    return this.forGroup(group);
  }

  any(group) {
    return this.forGroup(group);
  }

  a(group) {
    return this.forGroup(group);
  }

  forGroup(group) {
    this._group = String(group);

    // have set group ... if permission is also set - return validation
    if (this._permission && this._permission.length !== 0) {
      return this.hasOne({ can: this._permission, forGroup: this._group });
    }

    return this;
  }
}
