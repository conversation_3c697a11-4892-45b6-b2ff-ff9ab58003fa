/* eslint-disable import/no-unresolved */
import axios from 'axios';
import { getToken, setTokenExpiry, setToken } from '@/utils/auth';

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 5000 * 100, // request timeout
});

// request interceptor
service.interceptors.request.use(
  config => {
    // debugger;
    const token = getToken();
    // eslint-disable-next-line no-param-reassign
    if (token) config.headers.Authorization = `Bearer ${token}`;

    return config;
  },
  error => {
    // do something with request error
    console.error(error); // for debug
    return Promise.reject(error);
  },
);

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    const res = response.data;

    if (res.token && res.expires) {
      setToken(res.token);
      setTokenExpiry(res.expires);
    }

    if (res.code === 20000) return res;

    // if the custom code is not 20000, it is an error.

    try {
      switch (res.code) {
        // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
        case 50008:
        case 50012:
        case 50014:
          // TODO : find a better way to reload the page if the token is illegal/in-use/expired
          window.location.href = '/';
          break;
        /*
          TODO MessageBox.confirm(
              "You have been logged out, you can cancel to stay on this page, or log in again",
              "Confirm logout",
              {
                confirmButtonText: "Re-Login",
                cancelButtonText: "Cancel",
                type: "warning"
              }
            ).then(() => {
              store.dispatch("user/resetToken").then(() => {
                location.reload();
              });
            });
            break;
            */
        default:
          console.log('request.js: Attention required, result data:', res);
          if (res.message) return Promise.reject(new Error(`${res.message.toString()}`));
          else if (res.output) return Promise.reject(new Error(`output: ${String(res.output)}`));
      }
      throw new Error("Error in 'request.js' :/");
    } catch (err) {
      console.log('catch error in request.js', err);
      return Promise.reject(new Error(res.message || err));
    }
  },
  error => {
    console.error('error : ', error); // for debug
    /* TODO Message({
      message: error.message,
      type: "error",
      duration: 5 * 1000
    }); */
    return Promise.reject(new Error(error));
  },
);

export default service;
