/* eslint-disable no-param-reassign */
import Vue from 'vue';
import { VTooltip, VPopover, VClosePopover } from 'v-tooltip';

Vue.directive('tooltip', VTooltip);
Vue.directive('close-popover', VClosePopover);
Vue.component('v-popover', VPopover);

export function hideTooltip(whichTooltip) {
  // ---- Represents the "object" for which the tooltip "visible" state can be managed
  //      NOTE: It must contain 'visible' and 'timeout' sub-objects
  //            where 'visible' is a boolean that can be set, and 'timeout' is a timeout object that can be cleared
  if ((typeof whichTooltip === 'undefined' || typeof whichTooltip.visible === 'undefined') && console) {
    console.log('WARNING: the tooltip we are trying to hide does not exist (or invalid options were presented)', whichTooltip);
  } else {
    clearTimeout(whichTooltip.timeout);
    whichTooltip.visible = false;
  }
}

export function showTooltip(
  tooltipObject, // ---- Represents the "object" for which the tooltip "visible" state can be managed
  //      NOTE: It must contain 'visible', 'timeout', 'type', 'content', 'invisibleInSeconds'
  //            visible:  the state of the tooltip (true|false)
  //            timeout:  holds the timeout object (for use with clearTimeout() method)
  //            type:     string, one of the cs alert types (cs-info, cs-warning, etc)
  //            content:  string, the text displayed in the tooltip
  //            invisibleInSeconds:
  //                  number, 0 < x < 20
  //                  if 0, then the tooltip will not dissappear until 'timeout' is cleared (using hideTimeout method)
  //                  if greater than 20, will be reduced to 20.
  type,
  content,
  timeoutInSeconds = 0,
) {
  const validTooltipTypes = ['cs-info', 'cs-success', 'cs-warning', 'cs-alert', 'cs-error'];
  const t = JSON.parse(JSON.stringify(tooltipObject)); // make a copy of the object

  if (
    console &&
    (typeof t.visible !== 'boolean' ||
      typeof t.timeout === 'undefined' ||
      typeof t.type !== 'string' ||
      typeof t.content !== 'string' ||
      typeof type !== 'string' ||
      typeof content !== 'string' ||
      !validTooltipTypes.includes(type) ||
      content === '' ||
      (typeof t.classes !== 'string' && typeof t.classes !== 'undefined'))
  ) {
    console.log('WARNING: the tooltip we are trying to show does not exist (or invalid params were provided)!', t);
  } else {
    tooltipObject.type = `${type} ${t.classes}`;
    tooltipObject.content = content;
    tooltipObject.visible = true;
    let tooltipTimeout = 0;
    if (!isNaN(timeoutInSeconds) && timeoutInSeconds > 0 && timeoutInSeconds < 20) {
      tooltipTimeout = timeoutInSeconds;
    } else if (!isNaN(t.invisibleInSeconds) && t.invisibleInSeconds > 0 && t.invisibleInSeconds < 20) {
      tooltipTimeout = t.invisibleInSeconds;
    }

    if (tooltipTimeout > 0) {
      tooltipObject.timeout = setTimeout(() => {
        hideTooltip(tooltipObject);
      }, tooltipTimeout * 1000);
    }
  }
}
