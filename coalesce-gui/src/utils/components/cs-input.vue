<template>
  <div class="row no-gutters" :class="classes">
    <div id="label" :class="labelClasses" :for="inputId" v-if="label">
      {{ label }}
      <required-asterisk id="required" class="pl-1" v-if="showAsterisk" /><span v-if="tooltip" class="pl-2"><info :content="tooltip"></info></span>
    </div>
    <div id="inputParent" :class="inputGroupClasses">
      <div class="input-group hostname mx-auto mx-sm-0">
        <div class="input-group-prepend" v-if="!!$slots.prepend">
          <slot name="prepend"></slot>
        </div>
        <slot>
          <input
            :id="inputId"
            :value="value"
            @input="
              $emit('input', $event.target.value);
              $emit('touch');
            "
            @blur="$emit('touch')"
            :type="type || 'text'"
            class="form-control"
            :class="inputClass"
            :placeholder="placeholder"
            :autocomplete="autoCompleteValue"
          />
        </slot>
        <div class="input-group-append" v-if="!!$slots.append">
          <slot name="append"></slot>
        </div>
      </div>
      <div id="messages" v-if="requiredText || errorText" class="error-messages">
        {{ requiredText ? requiredText : errorText }}
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved, import/extensions */
/* eslint-disable space-before-function-paren, func-names */
import infoTooltip from '@/utils/components/info-tooltip.vue';
import requiredAsteriskComponent from '@/utils/components/required.vue';
import '@/utils/common.js';

export default {
  name: 'CSInput',
  components: {
    requiredAsterisk: requiredAsteriskComponent,
    info: infoTooltip,
  },
  props: {
    // NOTE - If inputId is used, it causes a "duplicate ID" browser issue
    //        VUE internally creates 2 <inputs> and deletes 1, during which the browser complaints about duplicate ID
    inputId: {
      type: String,
      default: undefined,
    },
    classes: {
      type: String,
      default: '',
    },
    label: {
      type: String,
      default: '',
    },
    options: {
      type: Object,
      default() {
        return {};
      },
    },
    required: {
      type: Boolean,
      default: false,
    },
    // eslint-disable-next-line vue/require-prop-types
    value: {
      required: true,
      validator: function(value) {
        return typeof value === 'string' || typeof value === 'number' || value === null;
      },
    },
    type: {
      type: String,
      default: 'text',
      validator: function(value) {
        // The value must match one of these strings
        return ['text', 'number', 'password'].indexOf(value) !== -1;
      },
    },
    autocomplete: {
      type: Boolean,
      default: undefined,
    },
  },
  data() {
    return {
      defaults: {
        slotPrependClasses: 'border border-left-0',
        slotAppendClasses: 'border border-right-0',
        maxWidth: '20em',
        tooltip: '',
        placeholder: '',
        labelClassesPrefix: 'd-flex align-items-center justify-content-center justify-content-sm-end col-sm-4 pr-sm-3',
        inputGroupClassesPrefix: 'input-fields-height col-sm-12',
        inputGroupClassesPrefixWithLabel: 'input-fields-height col-sm-8',
      },
    };
  },
  computed: {
    showAsterisk() {
      const showAsterisk = typeof this.options.showAsterisk === 'undefined' ? this.required : this.options.showAsterisk;

      return showAsterisk;
    },
    errorText() {
      return this.options.errorMessage || '';
    },
    requiredText() {
      const { showRequired } = this.options;
      let text = typeof showRequired !== 'string' && showRequired ? `${this.label} is required` : '';
      if (typeof this.options.showRequired === 'string') {
        text = this.options.showRequired; // allowed to be empty!
      }

      return text;
    },
    autoCompleteValue() {
      if (typeof this.autocomplete === 'undefined') return undefined;

      const ac = !!this.autocomplete;

      return ac ? 'on' : 'off';
    },
    maxWidth() {
      let mw = this.defaults.maxWidth;
      const { maxWidth } = this.options;

      if (typeof maxWidth === 'string') {
        mw = maxWidth;
      }

      return mw;
    },
    tooltip() {
      let tip = this.defaults.tooltip;
      const { tooltip } = this.options;

      if (typeof tooltip === 'string') {
        tip = tooltip;
      }

      return tip;
    },
    placeholder() {
      let p = this.defaults.placeholder;
      const { placeholder } = this.options;

      if (typeof placeholder === 'string') {
        p = placeholder;
      }

      return p;
    },
    inputClass() {
      const { slotPrependClasses, slotAppendClasses } = this.defaults;
      const { prepend, append } = this.$slots;
      let { classes } = this;

      if (prepend) {
        classes = `${classes} ${slotPrependClasses}`;
      }
      if (append) {
        classes = `${classes} ${slotAppendClasses}`;
      }

      return classes;
    },
    labelClasses() {
      let classes = this.defaults.labelClassesPrefix;
      const { labelClasses } = this.options;

      if (labelClasses) {
        classes = `${labelClasses.match(/col-/) ? classes.replace(/col-[^\s]*/, '') : classes} ${labelClasses}`;
      }

      return classes;
    },
    inputGroupClasses() {
      let classes = this.label ? this.defaults.inputGroupClassesPrefixWithLabel : this.defaults.inputGroupClassesPrefix;
      const { inputGroupClasses } = this.options;

      if (inputGroupClasses) {
        classes = `${inputGroupClasses.match(/col-/) ? classes.replace(/col-[^\s]*/, '') : classes} ${inputGroupClasses}`;
      }

      return classes;
    },
  },
  watch: {},
  created() {},
  methods: {},
};
</script>

<style lang="scss"></style>
<style lang="scss" scoped>
@import '@/styles/cs-styles.scss';

.error-messages {
  width: 100%;
  margin-left: 0.25rem;
  font-size: 80%;
  color: #dc3545;
}

.input-message {
  width: 100%;
  margin-left: 0.25rem;
  font-size: 80%;
  .warning {
    color: #dc3545;
  }
  .error {
    color: #dc3545;
  }
}
</style>
