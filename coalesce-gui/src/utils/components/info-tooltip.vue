<template>
  <font-awesome-icon
    v-if="content"
    v-tooltip="{ content, offset: 7, placement }"
    icon="info-circle"
    class="text-primary cursor-help"
  ></font-awesome-icon>
</template>

<script>
export default {
  name: 'InfoTooltip',
  props: {
    content: {
      type: String,
      default: '',
    },
    placement: {
      type: String,
      default: 'top',
    },
  },
};
</script>

<style lang="scss" scoped></style>
