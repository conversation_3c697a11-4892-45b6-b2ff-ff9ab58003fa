// ---- This is used by modules and components mainly - mostly to prepare the way for kubernetes friendly names
//      Names are converted to a friendly character set (excluding the 'lower case' action)
String.prototype.normalizeName = function normalizeName() {
  const str = String(this);
  return str
    .replace(/[^A-Za-z0-9_-\s]+/g, '') // REMOVE all non-alphanumeric and hyphen characters
    .replace(/[\s_-]+/g, ' ') // replace spaces and underscores and hyphens with a single space
    .trim();
  /*
    .replace(/(^-+|-+$)/g, "");         // REMOVE all prefixed and trailing hyphen characters
    */
};

// ---- This is used by connectors mainly, so that we can convert "user input" names to valid Javascript variable names
String.prototype.toJSName = function toJSName() {
  const str = String(this);
  return str
    .toLowerCase() // INTENTIONAL lowercase for JS friendly names
    .normalizeName() // First 'Normalize'... then
    .replace(/^[0-9]+\s*/g, '') // REMOVE numeric prefixes
    .replace(/[\s-]+/g, '_'); // REPLACE spaces and hyphens with underscores
};

// ---- This is used to provide kubernetes friendly names - that can be used as pod or service names
String.prototype.toK8Name = function toK8Name() {
  const str = String(this);
  return str
    .normalizeName()
    .replace(/^[0-9]+\s*/g, '') // REMOVE numeric prefixes
    .replace(/\s+/g, '-') // REPLACE spaces with hyphens
    .toLowerCase();
};

// ---- Very basic hostname/ip checker -- only 1-3 digits between periods (set of 4) and a alphanumeric word with 1 or more periods between text
String.prototype.isHostnameOrIP = function isHostnameOrIP() {
  const validIp = () => {
    const segment = this.split('.');
    if (segment.length !== 4) return false;

    let hasInvalidSegment = false;
    segment.find(s => {
      const numericSegment = parseInt(s, 10);
      if (isNaN(numericSegment) || numericSegment < 0 || numericSegment > 255) {
        hasInvalidSegment = true;
        return true;
      }
      return false;
    });

    return !hasInvalidSegment;
  };

  const validHostname = () => {
    return (
      this.trim()
        .toLowerCase()
        .match(/^(([a-z0-9]|[a-z0-9][a-z0-9-]*[a-z0-9]).)*([a-z0-9]|[a-z0-9][a-z0-9-]*[a-z0-9])$/) &&
      // Hostname MUST NOT be only numeric (see https://tools.ietf.org/html/rfc1123#page-13 - end of point 2.1 DISCUSSION)
      !this.trim().match(/^([0-9]+\.?)+$/) &&
      this.trim().length <= 253
    );
  };

  return !!(validHostname() || validIp());
};

export {};
