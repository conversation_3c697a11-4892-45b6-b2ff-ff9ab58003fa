/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export const isExternal = path => {
  return /^(https?:|mailto:|tel:)/.test(path);
};

/**
 * @param {string} str
 * @returns {Boolean}
 */
export const validPassword = str => {
  return str.length >= 6;
};

/**
 * @param {string} str
 * @returns {Boolean}
 */
export const validUsername = str => {
  const validMap = ['admin', 'editor'];
  return validMap.indexOf(str.trim()) >= 0;
};
