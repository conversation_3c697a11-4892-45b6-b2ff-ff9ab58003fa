import Cookies from 'js-cookie';

const RememberKey = 'coalesce_lab_remember_me';
const TokenKey = 'coalesce_lab_token';
const TokenExpiryKey = 'coalesce_lab_token_expiry';

export function getTokenExpiry() {
  return Cookies.get(TokenExpiryKey);
}
export function getToken() {
  return Cookies.get(TokenKey);
}
export function getRememberToken() {
  const rToken = Cookies.get(RememberKey);

  // ---- it has expired if it is invalid, remove it to clean up
  if (!rToken) removeRememberToken(false /* don't log */);

  return Cookies.get(RememberKey);
}

export function setTokenExpiry(expiry) {
  return Cookies.set(TokenExpiryKey, expiry);
}
export function setToken(token) {
  return Cookies.set(TokenKey, token);
}
export function setRememberToken(value) {
  let expires = 7; // days

  // ---- For testing :)
  // ---- 1/1440 == 1 minute in cookie time .......... this multiplied by <number of minutes> ...
  //       makes the remember token only valid for a certain amount of time (so it can be tested)
  const minuteIncrement = 1 / 1440;
  const amTesting = false;
  if (amTesting) expires = minuteIncrement * 1;

  return Cookies.set(RememberKey, value, { expires });
}

export function removeRememberToken(doLog = true) {
  if (doLog) console.debug("Removing 'Remember' token from Cookies.");

  Cookies.remove(RememberKey);
}
export function removeToken() {
  console.debug('Removing auth token from Cookies.');
  Cookies.remove(TokenKey);
  Cookies.remove(TokenExpiryKey);
}
