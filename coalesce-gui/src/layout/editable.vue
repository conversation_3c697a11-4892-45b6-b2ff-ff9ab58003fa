<template>
  <div>
    <b-link @click="setBreadCrumb" class="child-nav-link nav-link" :to="node.data.url ? node.data.url : { name: node.data.route }">
      <span
        :id="`span-${module.id}`"
        @contextmenu.prevent="onModuleRightClick"
        v-tooltip.top-end="{
          classes: tooltips.moduleName.type,
          content: tooltips.moduleName.content,
          trigger: 'manual',
          offset: 5,
          show: tooltips.moduleName.visible,
        }"
        >{{ node.title }}</span
      >
    </b-link>
    <input
      ref="input"
      v-model.trim="module.displayName"
      :id="module.id"
      type="text"
      class="field-value form-control cs-input"
      style="display: none"
      @blur="onBlur"
      @keydown.enter="$event.target.blur()"
      @input="isValid"
    />
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved */
import '@/utils/common';
import { mapGetters } from 'vuex';
import { hideTooltip, showTooltip } from '@/utils/tooltip-alerts';

export default {
  name: 'Editable',
  // eslint-disable-next-line vue/require-prop-types
  props: ['node', 'moduleId', 'openMenu', 'toggleShowItem', 'select'],
  data() {
    return {
      module: {
        displayName: this.node.title,
        name: this.node.title,
        id: this.node.data.id,
        gitrepo: this.node.data.gitrepo ? this.node.data.gitrepo : null,
        type: this.node.data.type,
      },
      valid: false,
      canRename: false,
      tooltips: {
        moduleName: {
          classes: 'module-name-warning',
          visible: false,
          timeout: null,
          type: '',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
      },
    };
  },
  computed: {
    ...mapGetters(['modules']),
  },
  methods: {
    setBreadCrumb() {
      this.$store.dispatch('app/setSidebarLocation', [{ title: this.module.name }]);
    },
    onModuleRightClick() {
      hideTooltip(this.tooltips.moduleName);
      this.$emit('openMenu', this.node.data.id, this.node.data.gitrepo, this.node.title, this.node.data.type);
      this.$refs.input.focus();
    },
    focusField(id) {
      const span = document.getElementById(`span-${id}`);
      span.style.display = 'none';
      const input = document.getElementById(id);
      input.style.display = 'block';
      input.focus();
    },
    isValid() {
      hideTooltip(this.tooltips.moduleName);
      this.module.name = this.module.displayName.normalizeName();
      // console.log("**************name", this.module.name);
      // console.log("------------------------");
      if (this.module.name === '') {
        this.valid = false;
        return;
      }
      let valid = true;

      const moduleList = this.modules.modules;
      if (moduleList.length > 0) {
        for (const i in moduleList) {
          // check whether or not the module is being pushed to staging ? if its already on staging, dont allow users to rename
          if (moduleList[i].id === this.module.id) {
            this.canRename = !moduleList[i].gitrepo ? true : moduleList[i].gitrepo === '';
            if (!this.canRename) {
              showTooltip(
                this.tooltips.moduleName,
                'cs-warning',
                `Module "<strong>${moduleList[i].name}</strong>" cannot be renamed at this stage`,
                5,
              );
            }

            break;
          }
        }

        if (this.canRename) {
          for (const i in moduleList) {
            // ---- using 'toK8Name' allows us to compare them both in their lower case form
            if (this.module.id !== moduleList[i].id && this.module.name.toK8Name() === moduleList[i].name.toK8Name()) {
              valid = false;
              showTooltip(
                this.tooltips.moduleName,
                'cs-warning',
                `The module "<strong>${moduleList[i].name}</strong>" has the same or too similar <span class="nowrap">a name :/</span>`,
                5,
              );
              break;
            }
          }
        }
      }

      this.valid = valid;
      // console.log("this.valid", this.valid);
    },
    onBlur() {
      this.$emit('toggleShowItem', true);
      hideTooltip(this.tooltips.moduleName);
      const input = document.getElementById(this.moduleId);
      input.style.display = 'none';
      const span = document.getElementById(`span-${this.moduleId}`);
      span.style.display = 'block';

      const data = {
        prevName: this.node.title,
        newName: this.module.name,
        id: this.module.id,
        type: this.module.type,
      };
      // check if module name changed or not ? if its similar, dont make the rename request
      this.valid = this.module.name === this.node.title ? false : this.valid;
      // console.log("this.canRename", this.canRename);
      // console.log("this.valid-onblur", this.valid);
      if (this.valid && this.canRename) {
        this.$store
          .dispatch('modules/rename', data)
          .then(response => {
            console.log('rename Module response ', response);
            if (Number(this.$route.params.moduleId) === Number(this.module.id)) {
              this.setBreadCrumb();
            }
          })
          .catch(err => {
            this.module.displayName = this.node.title;
            this.module.name = this.node.title;

            if (err.message === 'Error: Request failed with status code 403') {
              showTooltip(this.tooltips.moduleName, 'cs-warning', 'Sorry, you do not have permission to perform this action', 5);
            }
          });
      } else {
        this.module.displayName = this.node.title;
        this.module.name = this.node.title;
        // eslint-disable-next-line no-useless-return
        return;
      }
    },
  },
};
</script>
<style lang="scss">
.module-name-warning .tooltip-inner {
  min-width: 18em;
}
</style>
<style scoped>
.cs-input {
  background-color: #211e1e !important;
}
.cs-input:focus {
  color: white;
}

a.nav-link {
  color: #fff;
}

a.nav-link,
a.nav-link.child-nav-link {
  padding: 0.07em 0;
}

a.nav-link i {
  font-size: 1.2rem;
  margin-right: 10px;
  min-width: 25px;
}

a.nav-link:hover {
  color: rgba(133, 124, 124, 0.833);
}
.sl-vue-tree-title .child-nav-link {
  padding: 0;
}
</style>
