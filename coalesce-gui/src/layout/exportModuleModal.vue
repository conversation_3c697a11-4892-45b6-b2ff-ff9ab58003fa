<template>
  <div>
    <b-modal
      class="modal fade"
      :title="`Export Module: (${module.name})`"
      size="lg"
      id="exportModal"
      ref="exportModal"
      centered
      ok-only
      ok-variant="outline-success"
      ok-title="Export Module"
      @ok="exportFile"
      @shown="onModalShow"
      @hide="onModalHide"
    >
      <div v-if="moduleDependencies.subModules.length || moduleDependencies.components.length" class="delete-modal px-4 delete-modal-font">
        <div class="mb-2">
          The module <strong>{{ module.name }} </strong> has following dependencies:
        </div>
        <div v-if="moduleDependencies.subModules.length"><strong>Sub Modules:</strong> {{ moduleDependencies.subModules }}</div>
        <div v-if="moduleDependencies.components.length" class="mt-2"><strong>Components:</strong> {{ moduleDependencies.components }}</div>
      </div>
      <div class="mt-2">
        Please choose the dependencies you want to export alongwith this module
      </div>
      <div class="mt-2">
        <b-form-checkbox v-if="moduleDependencies.subModules.length" v-model="exportSubModules" id="exportSubModules" name="exportSubModules">
          Export All sub-modules
        </b-form-checkbox>
        <b-form-checkbox v-if="moduleDependencies.components.length" v-model="exportComponents" id="exportComponents" name="exportComponents">
          Export All components
        </b-form-checkbox>
        <b-form-checkbox v-model="exportConnectors" id="exportConnectors" name="exportConnectors">
          Export All connectors
        </b-form-checkbox>
      </div>
    </b-modal>
  </div>
</template>

<script>
import FileSaver from 'file-saver';
import { mapGetters } from 'vuex';

export default {
  name: 'ExportModuleModal',
  props: {
    module: {
      type: Object,
      required: true,
      default() {
        return {
          id: null,
          gitrepo: '',
          name: '',
          type: '',
        };
      },
    },
  },
  data() {
    return {
      moduleToSave: null,
      fileData: {},
      subModulesToBeExported: [],
      componentsToBeExported: [],

      moduleDependencies: {
        subModules: {},
        components: {},
        connectors: {},
      },
      exportSubModules: false,
      exportComponents: false,
      exportConnectors: false,
    };
  },
  computed: {
    ...mapGetters(['modules', 'components', 'connectorEndpoints']),
  },
  methods: {
    executExportFunc(moduleName) {
      console.log('moduleName', moduleName);
      // debugger;
      const stack = []; // using stack DS for nested-modules
      const visitedModules = [];
      const components = [];

      stack.push(moduleName); // first module being pushed into the stack would be main-module/root-module

      const extractModuleDependencies = (module, code) => {
        const newNode = code[3];

        if (newNode) {
          newNode.forEach(item => {
            // eslint-disable-next-line default-case
            switch (true) {
              case typeof item[0] === 'string' && item[0] === 'component': {
                if (!components.includes(item[1].name) && item[1].comptype !== 'builtin') {
                  components.push(item[1].name);
                }
                break;
              }
              case typeof item[0] === 'string' && item[0] === 'menuModule': {
                if (!visitedModules.includes(item[1].name)) {
                  stack.push(item[1].name);
                }
                break;
              }
              case Array.isArray(item):
                extractModuleDependencies(module, item);
                break;

              // TODO default case
            }
          });
        }
      };

      while (stack.length) {
        const menuModule = stack.pop(); // extract the last pushed item from stack and search its dependencies
        const { modules } = this.modules;
        for (let i = 0; i < modules.length; i++) {
          if (modules[i].name === menuModule) {
            // debugger;
            // const module = JSON.stringify(this.moduleToSave.code);
            let parsedModule = JSON.parse(modules[i].code);
            if (typeof parsedModule !== 'object') parsedModule = JSON.parse(parsedModule);

            extractModuleDependencies(menuModule, parsedModule);
            visitedModules.push(menuModule);
          }
        }
      }

      visitedModules.shift(); // removes the first element which will be the rootModule

      const response = {
        rootModule: moduleName,
        subModules: visitedModules,
        guiComponents: components,
      };
      return response;
    },
    createFileData(rootModule, modulesToExport, componentsToExport) {
      const data = {
        rootModule: {},
        subModules: {},
        components: {},
        connectors: [],
      };
      const { modules } = this.modules;

      // add main-Module (always)
      for (let i = 0; i < modules.length; i++) {
        if (modules[i].name === rootModule) {
          data.rootModule = modules[i];
        }
      }

      // add sub-Modules as per User's preference
      if (modulesToExport && this.exportSubModules) {
        for (const module of modulesToExport) {
          for (let i = 0; i < modules.length; i++) {
            if (modules[i].name === module) {
              data.subModules[module] = modules[i];
            }
          }
        }
      }
      // add components as per User's preference
      if (componentsToExport && this.exportComponents) {
        for (const comp of componentsToExport) {
          const { components } = this;

          for (let i = 0; i < components.length; i++) {
            if (components[i].name === comp) {
              data.components[comp] = components[i];
              this.componentsToBeExported.push(components[i].name);
            }
          }
        }
      }
      if (this.exportConnectors) {
        data.connectors = this.connectorEndpoints;
      }

      return JSON.stringify(data);
    },
    exportModule() {
      console.log('saveModule');

      const { modules } = this.modules;

      for (let i = 0; i < modules.length; i++) {
        if (modules[i].id === Number(this.module.id)) {
          this.moduleToSave = modules[i];
        }
      }

      // console.log('module', JSON.parse(this.moduleToSave.code));

      const { subModules, guiComponents } = this.executExportFunc(this.moduleToSave.name);

      this.moduleDependencies.subModules = subModules;
      this.moduleDependencies.components = guiComponents;

      console.log('subModules', subModules);
      console.log('guiComponents', guiComponents);
    },
    showModal() {
      this.$refs.exportModal.show();
      this.exportModule();
    },

    exportFile() {
      const { subModules, components } = this.moduleDependencies;

      const fileData = this.createFileData(this.module.name, subModules, components);

      console.log('parsed file', JSON.parse(fileData));
      this.fileData = JSON.parse(fileData);
      const blob = new Blob([JSON.stringify(this.fileData)], { type: 'application/json' });
      FileSaver.saveAs(blob, this.module.name);
    },

    onModalShow() {
      this.exportSubModules = false;
      this.exportComponents = false;
      this.exportConnectors = false;
    },
    onModalHide() {
      this.exportSubModules = false;
      this.exportComponents = false;
      this.exportConnectors = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.delete-modal {
  background-color: #fffaf3;
  color: #573a08;
  border-radius: 5px;
  padding: 1em 1.5em;
  border: 1px solid #573a08;
}
.delete-modal-font {
  font-size: 0.9em;
}
</style>
