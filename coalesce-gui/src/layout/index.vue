<template>
  <div class="app-wrapper">
    <context-menu id="context-menu" ref="menu">
      <span
        v-tooltip.left="hideRenameButton && { content: 'This Module has already being pushed to staging, it cannot be renamed now', offset: -140 }"
      >
        <li :class="[hideRenameButton ? 'disabled' : 'ctx-menu-overwrite']" @click="contextMenuItemClick">Rename</li>
      </span>
      <li class="ctx-menu-overwrite" @click="showDeleteModal">Delete</li>

      <!-------- xport Functionality (disabled for now but working) ------->

      <!-- <li class="ctx-menu-overwrite" @click="showExportModuleModal">Export Module</li>
      <exportModuleModal :module="module" ref="exportModuleModal" /> -->
      <!------------------------------------------------------------------------------------->
    </context-menu>
    <b-modal
      @shown="onModalShow"
      @hide="onModalHide"
      class="modal fade"
      size="lg"
      id="deleteModal"
      ref="deleteModal"
      centered
      hide-footer
      hide-header
    >
      <div>
        <p>
          <strong>Are you absolutely sure? </strong>
        </p>
      </div>

      <div class="delete-modal px-4 delete-modal-font">
        <div class="mb-2">This action <strong>CANNOT</strong> be undone.</div>
        <div>
          This operation will permanently delete the
          <strong>{{ module.gitrepo ? module.gitrepo : module.name }}</strong> module, including its code, repository, and all other associations.
        </div>
      </div>

      <div class="my-3">
        <label for="delete-modal-input" class="delete-modal-font"
          >Please type <strong>{{ module.gitrepo ? module.gitrepo : module.name }}</strong> to confirm:</label
        >
        <b-form-input id="delete-modal-input" v-model="module.deleteModalInput" trim></b-form-input>
      </div>

      <div class="d-flex justify-content-around">
        <div
          class="mt-2 d-flex flex-column justify-content-center w-25 text-right pr-2"
          v-tooltip.right="{
            classes: 'mb-3 ' + tooltips.deleteModal.type,
            content: tooltips.deleteModal.content,
            trigger: 'manual',
            offset: 10,
            show: tooltips.deleteModal.visible,
          }"
        >
          &nbsp;
        </div>

        <div class="w-75 d-flex flex-row">
          <div class="pb-3 w-50"></div>

          <div class="w-50 pb-3 d-flex justify-content-end">
            <button type="button" class="btn cs-btn cs-btn-outline-warning mx-2" @click="$bvModal.hide('deleteModal')">
              <i class="fas fa-times"></i>
              &nbsp;Cancel
            </button>

            <span v-tooltip.top="isDeleteButtonDisabled && { content: `Please fill out the input field`, offset: 10 }">
              <button
                type="button"
                class="btn cs-btn btn-md"
                :class="[isDeleteButtonDisabled ? 'cs-btn-inactive' : 'cs-btn-danger ']"
                @click="deleteModule"
                :disabled="isDeleteButtonDisabled"
              >
                Delete Module
              </button>
            </span>
          </div>
        </div>
      </div>
    </b-modal>
    <navbar class="navbar-container" />
    <div v-if="sidebar" class="cs-sidebar mt-2">
      <!--div id="sidebar-filter" class="m-2 d-flex align-items-center p-0 border-bottom border-light">
        <i class="fas fa-filter p-1" style="font-size:1.3rem"></i><b-input class="border-0 rounded-0"></b-input>
      </div-->
      <sl-vue-tree :key="SlVueTreekey" ref="treeview" v-model="nodes" :allow-multiselect="false" @select="onSelectItem">
        <template slot="title" slot-scope="{ node }">
          <span class="item-icon" :class="[node.data.smalltitle ? 'small' : '']" v-if="!node.isLeaf">
            <b-link class="nav-link" :to="node.data.url ? node.data.url : { name: node.data.route }">
              <!-- {{  node.data  }} -->
              <i
                v-if="node.data.type !== 'connector'"
                :class="[node.data.icon && node.data.type !== 'connector' ? node.data.icon : node.data.type ? '' : 'fa fa-file']"
              ></i>
              <span class="text" :class="[node.data.class ? node.data.class : '']">{{ node.title }}</span>
              <span v-if="!node.data.noNodeAction">
                <i class="ml-1 pl-1 node-action fas fa-plus-circle" />
              </span>
            </b-link>
          </span>
          <span class="item-icon" :class="[node.data.smalltitle ? 'small' : '', 'level' + node.data.level]" v-if="node.isLeaf">
            <!-- <i class="child-link"></i> -->
            <!-- <span class="text" :class="[node.data.class ? node.data.class : '']"> -->
            <!-- {{ node.title }} -->
            <div v-if="node.title != 'Dashboards' && node.data.url.includes('/menu/menu-editor')" style="margin-left: -5px">
              <editable @openMenu="showContextMenu" @toggleShowItem="onSelectItemFlag" ref="editable" :node="node" :module-id="module.id" />
            </div>
            <div v-else>
              <b-link class="child-nav-link nav-link" :to="node.data.url ? node.data.url : { name: node.data.route }">
                <!-- {{ node.data }} -->
                <div :style="[node.data.url.includes('/connector/endpoint') ? { 'margin-left': '-20px' } : { 'margin-left': '-5px' }]">
                  {{ node.title }}
                </div>
              </b-link>
            </div>
            <!-- </span> -->
          </span>
        </template>

        <template slot="toggle" slot-scope="{ node }">
          <slot name="toggle" :node="node">
            <span class="expander" v-if="!node.isLeaf">
              <font-awesome-icon v-if="node.isExpanded && node.children.length > 0" icon="chevron-up" class="cursor-pointer"></font-awesome-icon>
              <font-awesome-icon v-if="!node.isExpanded && node.children.length > 0" icon="chevron-down" class="cursor-pointer"></font-awesome-icon>
            </span>
          </slot>
        </template>

        <!-- eslint-disable-next-line vue/no-unused-vars -->
        <template slot="sidebar" slot-scope="{ node }"></template>

        <template slot="draginfo"></template>
      </sl-vue-tree>
    </div>
    <!-- <sidebar class="sidebar-container" /> -->
    <div class="main-container" :class="[sidebarClass]">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <app-main />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved */
import contextMenu from 'vue-context-menu';
import { mapGetters } from 'vuex';
import { hideTooltip, showTooltip } from '@/utils/tooltip-alerts';
import { Navbar, AppMain, SlVueTree } from './components';
import ResizeMixin from './mixin/ResizeHandler';
import editable from './editable.vue';
// import exportModuleModal from './exportModuleModal.vue';

let fixedNodes = [
  /*
  sample node configuration, see https://github.com/holiber/sl-vue-tree
  { title: "Item1", isLeaf: true },
  { title: "Item2", isLeaf: true, data: { visible: false } },
  { title: "Folder1" },
  {
    title: "Folder2",
    isExpanded: true,
    children: [
      { title: "Item3", isLeaf: true },
      { title: "Item4", isLeaf: true }
    ]
  },
  */
  {
    title: 'Dashboards',
    type: 'filter',
    isDraggable: false,
    isLeaf: false,
    isExpanded: false,
    hasChildren: false,
    data: {
      route: 'dashboard',
      icon: 'fa fa-columns text-center mr-2',
      noNodeAction: true,
    },
    version: 0,
  },
  {
    title: 'MODULES',
    type: 'modules',
    isDraggable: false,
    isLeaf: false,
    isExpanded: false,
    hasChildren: true,
    data: { route: 'newmodule', icon: 'fa fa-sitemap text-center mr-2' },
    version: 0,
  },
  {
    title: 'COMPONENTS',
    type: 'components',
    isDraggable: false,
    isLeaf: false,
    isExpanded: false,
    hasChildren: true,
    data: { route: 'newcomponent', icon: 'fa fa-laptop-code text-center mr-2' },
    version: 0,
  },
  {
    title: 'CONNECTORS',
    type: 'connectors',
    isDraggable: false,
    isLeaf: false,
    isExpanded: false,
    hasChildren: true,
    data: { route: '', icon: 'fa fa-plug text-center mr-2', noNodeAction: true },
    version: 0,
  },
  {
    title: 'LAUNCHPAD',
    type: 'launchpad',
    isDraggable: false,
    isLeaf: false,
    isExpanded: false,
    hasChildren: false,
    data: { route: 'launchpad', icon: 'fa fa-paper-plane text-center mr-2' },
    version: 0,
  },

  // { title: "Dashboard", isLeaf: true, data: { route: 'dashboard', icon: 'fas fa-tachometer-alt' } },
  // { title: "Menu Editor", isLeaf: true, data: { route: 'menueditor', icon: 'far fa-edit' } },
  // { title: "Modules", isLeaf: true, data: { route: 'newmodule', icon: 'far fa-edit' } },
  // { title: "Invite User", isLeaf: true, data: { url: '/inviteuser', icon: 'fas fa-envelope-open-text' } }
];

function getSidebarPath(nodes, route /* , currentPath */) {
  let result = [];
  let offset = 0;
  for (const node in nodes) {
    const mypath = [];
    mypath.push(offset);
    const currentNode = nodes[node];
    if (typeof currentNode.data !== 'undefined') {
      if (typeof currentNode.data.name === 'string' && currentNode.data.name === route.name) {
        result.push(offset);
        break;
      } else if (typeof currentNode.data.path === 'string' && currentNode.data.path === route.path) {
        result.push(offset);
        break;
      } else if (typeof currentNode.data.url === 'string' && currentNode.data.url === route.path) {
        // debugger
        result.push(offset);
        break;
      }
    }
    if (typeof currentNode.children !== 'undefined') {
      const path = getSidebarPath(currentNode.children, route, mypath);
      if (path.length > 0) {
        result.push(offset);
        result = result.concat(path);
        break;
      }
    }
    offset++;
  }
  return result;
}

export default {
  name: 'HTTP',
  components: {
    Navbar,
    AppMain,
    SlVueTree,
    editable,
    contextMenu,
    // exportModuleModal,
  },
  mixins: [ResizeMixin],
  data() {
    return {
      deletionTimeout: null,
      SlVueTreekey: 1,
      item: null,
      currentModuleId: this.$route.params.moduleId,
      // moduleToSave: {},
      // uploadedModule: [],
      module: {
        id: null,
        gitrepo: '',
        name: '',
        type: '',
        deleteModalInput: '',
      },
      selectItem: true,
      setBreadCrumOnReload: false,
      hideRenameButton: false,
      tooltips: {
        deleteModal: {
          visible: false,
          timeout: null,
          type: '',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
      },
    };
  },
  computed: {
    ...mapGetters(['modules', 'components', 'connectorEndpoints']),
    url() {
      return this.$route.params.moduleId;
    },
    isDeleteButtonDisabled() {
      const name = this.module.gitrepo ? this.module.gitrepo : this.module.name;
      if (name !== this.module.deleteModalInput) return true;

      return false;
    },
    nodes: {
      get() {
        // debugger;
        const moduleList = this.modules.modules;
        const compList = this.components;
        const connectorsList = this.connectorEndpoints;
        // console.log('connectorsList', connectorsList);
        // console.log('moduleList', moduleList);

        if (connectorsList.length > 0) {
          for (let i = 0; i < fixedNodes.length; i++) {
            const node = fixedNodes[i];
            if (node.type === 'connectors') {
              node.children = [];
              for (let j = 0; j < connectorsList.length; j++) {
                const connector = connectorsList[j];
                const connectorEndpoints = [];
                // eslint-disable-next-line camelcase
                const { connector_type } = connector;

                // connector-endpoints nodes data
                for (let k = 0; k < connectorsList[j].endpoints.length; k++) {
                  const endpoint = connectorsList[j].endpoints[k];
                  connectorEndpoints.push({
                    title: endpoint.name,
                    description: endpoint.description,
                    isDraggable: false,
                    isExpanded: true,
                    isLeaf: true,
                    isSelectable: true,
                    isSelected: false,
                    isVisible: true,
                    data: {
                      // eslint-disable-next-line camelcase
                      url: `/connector/endpoint/${connector_type}/edit/${endpoint.name}`,
                      level: 2,
                      id: endpoint.id,
                    },
                  });
                }
                node.children.push({
                  title: connector_type,
                  isDraggable: false,
                  isLeaf: false,
                  data: {
                    // eslint-disable-next-line camelcase
                    url: `/connector/endpoint/${connector_type}/create`,
                    level: 1,
                    id: connector.id,
                    type: 'connector',
                  },
                  children: connectorEndpoints,
                });
              }
              node.isDraggable = false;
              node.isExpanded = true;
              node.isLeaf = false;
              node.version++;
              break;
            }
          }
        }
        // if (connectorsList.length > 0) {
        //   for (let i = 0; i < fixedNodes.length; i++) {
        //     const node = fixedNodes[i];
        //     if (node.type === 'connectors') {
        //       node.children = [];
        //       for (let j = 0; j < connectorsList.length; j++) {
        //         const connector = connectorsList[j];
        //         node.children.push({
        //           title: connector.name,
        //           description: connector.description,
        //           isLeaf: false,
        //           data: {
        //             url: `/connector/edit/${connector.id}`,
        //             level: 2,
        //             id: connector.id,
        //             icon: '',
        //             type: 'connector',
        //           },
        //           children: connector.data.children ? connector.data.children : [],
        //         });
        //       }
        //       node.isExpanded = true;
        //       node.isLeaf = false;
        //       node.version++;
        //       break;
        //     }
        //   }
        // }
        if (compList.length > 0) {
          for (let i = 0; i < fixedNodes.length; i++) {
            const node = fixedNodes[i];
            if (node.type === 'components') {
              node.children = [];
              for (let j = 0; j < compList.length; j++) {
                const comp = compList[j];
                // eslint-disable-next-line no-continue
                if (comp.id < 0) continue;
                node.children.push({
                  title: comp.name,
                  description: comp.description,
                  isLeaf: true,
                  data: {
                    url: `/component/comp-editor/${comp.id}`,
                    level: 1,
                    id: comp.id,
                  },
                });
              }
              node.isDraggable = false;
              node.isExpanded = true;
              node.isLeaf = false;
              node.version++;
              break;
            }
          }
        }
        if (moduleList.length > 0) {
          for (let i = 0; i < fixedNodes.length; i++) {
            const node = fixedNodes[i];
            if (node.type === 'modules') {
              node.children = [];
              for (let j = 0; j < moduleList.length; j++) {
                const module = moduleList[j];
                node.children.push({
                  title: module.name,
                  description: module.description,
                  isLeaf: true,
                  isDraggable: false,
                  data: {
                    url: `/menu/menu-editor/${module.id}`,
                    level: 1,
                    id: module.id,
                    gitrepo: module.gitrepo,
                    type: module.type,
                  },
                });
              }
              node.isDraggable = false;
              node.isExpanded = true;
              node.isLeaf = false;
              node.version++;
              break;
            }
          }
        } else {
          for (let i = 0; i < fixedNodes.length; i++) {
            const node = fixedNodes[i];
            if (node.type === 'modules') {
              node.children = [];
              node.isDraggable = false;
              node.isExpanded = false;
              node.isLeaf = false;
              break;
            }
          }
        }
        return fixedNodes;
      },
      set(newNodes) {
        fixedNodes = newNodes;
      },
    },
    device() {
      return this.$store.state.app.device;
    },
    sidebarClass() {
      // debugger
      let resultClass = 'with-sidebar';
      if (typeof this.$route.meta.rightbar !== 'undefined' && this.$route.meta.rightbar) {
        resultClass = this.$store.state.app.sidebar.opened ? 'with-sidebar' : 'without-sidebar';
      } else {
        resultClass = this.$store.state.app.sidebar.opened ? 'without-rightbar' : 'without-bothbar';
      }
      return resultClass;
    },
    sidebar() {
      // debugger;
      return this.$store.state.app.sidebar.opened;
    },
    fixedHeader() {
      return this.$store.state.settings.fixedHeader;
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile',
      };
    },
  },
  watch: {
    nodes(oldValue, newValue) {
      //   debugger
      const path = getSidebarPath(newValue, this.$route, []);
      if (path.length > 0 && this.$refs.treeview) this.$refs.treeview.select(path);
      else if (this.$refs.treeview) this.$refs.treeview.select([1]);
    },
  },
  created() {
    this.$store.dispatch('modules/list', this.$route.params);
    this.$store.dispatch('component/list', this.$route.params);
    this.$store.dispatch('connectorEndpoint/list', this.$route.params);
    this.setBreadCrumOnReload = true;
  },
  methods: {
    showExportModuleModal() {
      this.$refs.exportModuleModal.showModal();
    },
    importModule(e) {
      const file = e.target.files[0];
      const reader = new FileReader(file);
      reader.onload = event => {
        const obj = JSON.parse(event.target.result);
        console.log('obj', obj);
        this.uploadedModule = obj;
        this.updateModule();
      };
      reader.readAsText(file);
    },
    async updateModule() {
      try {
        await this.$store.dispatch('modules/updatemenucode', {
          data: this.uploadedModule,
          moduleId: this.$route.params.moduleId,
        });
        location.reload();
      } catch (error) {
        console.log('error onModuleUpload', error);
      }
    },
    onModalShow() {
      hideTooltip(this.tooltips.deleteModal);
      this.module.deleteModalInput = '';
    },
    onModalHide() {
      clearTimeout(this.deletionTimeout);
      hideTooltip(this.tooltips.deleteModal);
      this.module.deleteModalInput = '';
    },
    showDeleteModal() {
      this.module.gitrepo = this.modules.modules.find(x => x.id === this.module.id).gitrepo;
      this.$refs.deleteModal.show();
    },

    deleteModule() {
      this.module.deleteModalInput = '';
      const data = {
        id: this.module.id,
        moduleName: this.module.gitrepo,
      };
      this.$store
        .dispatch('modules/delete', data)
        .then(response => {
          console.log('delete Module response ', response);
          if (response.affectedRows && response.affectedRows > 0) {
            this.$router.push({ path: '/menu/newmodule' });
            this.SlVueTreekey = Math.floor(Math.random() * 100); // returns a random integer from 0 to 99
            setTimeout(() => {
              showTooltip(this.tooltips.deleteModal, 'cs-success', response.msg, 4);
            }, 300);
          } else {
            setTimeout(() => {
              showTooltip(this.tooltips.deleteModal, 'cs-warning', response.msg, 4);
            }, 300);
          }
          this.deletionTimeout = setTimeout(() => {
            this.$refs.deleteModal.hide();
          }, 4000);
        })
        .catch(err => {
          const error = err.message === 'Error: Request failed with status code 403' ? "You're not authorized to perform this action" : err.message;
          setTimeout(() => {
            showTooltip(this.tooltips.deleteModal, 'cs-error', error);
          }, 300);
        });
    },
    showContextMenu(id, gitrepo, name, moduleType) {
      if (gitrepo && gitrepo !== '') this.hideRenameButton = true;
      else this.hideRenameButton = false;

      this.module.id = id;
      this.module.gitrepo = gitrepo;
      this.module.name = name;
      this.module.type = moduleType;

      this.$refs.menu.open();
    },
    contextMenuItemClick() {
      this.$refs.editable.focusField(this.module.id);
      this.selectItem = false;
    },
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false });
    },
    onSelectItemFlag(value) {
      this.selectItem = value || this.selectItem;
    },
    onSelectItem() {
      // upon reload only
      if (this.setBreadCrumOnReload) {
        const { moduleId } = this.$route.params;

        const { modules } = this.modules;
        for (let i = 0; i < modules.length; i++) {
          if (Number(modules[i].id) === Number(moduleId)) {
            this.$store.dispatch('app/setSidebarLocation', [{ title: modules[i].name }]);
            this.setBreadCrumOnReload = false;
            break;
          }
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
#sidebar-filter {
  border: transparent;
  input {
    background-color: transparent;
  }
  border-bottom: 2px solid white;
  border-radius: 0;
}
/* override default settings for view tree -- nav-children should not decrease the size of the icon */
.sl-vue-tree-title .child-link.fas,
.sl-vue-tree-title .child-link.far {
  font-size: inherit;
}
.cs-sidebar {
  width: 250px;
  height: 100%;
  position: fixed;
  background-color: black;
  padding-top: 57px;
  overflow-y: auto;
}

.without-rightbar,
.with-sidebar {
  margin-left: 250px;
}

.main-container {
  width: calc(100% - 250px - 450px); // reduced by width of ALL sidebars (default).
  &.without-sidebar {
    width: calc(
      100% - 450px
    ); // reduced by width of RIGHT sidebar ONLY (dynamic LEFT sidebar reduction). FIXME: Right sidebar to be dynamically adjusted
  }
  &.without-rightbar {
    width: calc(
      100% - 250px
    ); // reduced by width of RIGHT sidebar ONLY (dynamic LEFT sidebar reduction). FIXME: Right sidebar to be dynamically adjusted
  }
  &.without-bothbar {
    width: calc(100%); // reduced by width of RIGHT sidebar ONLY (dynamic LEFT sidebar reduction). FIXME: Right sidebar to be dynamically adjusted
  }
  padding-top: 57px;
}

.navbar-container {
  position: fixed;
}

.sl-vue-tree.sl-vue-tree-root {
  background-color: black;
  border: none;
}

a.nav-link {
  color: #fff;
}

a.nav-link,
a.nav-link.child-nav-link {
  padding: 0.07em 0;
}

a.nav-link i {
  font-size: 1.2rem;
  margin-right: 10px;
  min-width: 25px;
}

a.nav-link:hover {
  color: rgba(133, 124, 124, 0.833);
}

.sl-vue-tree-node .small a {
  font-size: 0.8rem;
}

.sl-vue-tree-node .small a .text {
  margin-left: 10px;
}

.sl-vue-tree-title:hover .node-action {
  font-size: 1rem;
  display: inline-block;
  color: #fff;
}

.sl-vue-tree-title .node-action {
  display: none;
}

.sl-vue-tree-title {
  border: none;
}

.sl-vue-tree-title a {
  outline: 0;
}

.sl-vue-tree-title .child-link {
  font-size: 0.8rem;
  margin-right: -15px;
}

.sl-vue-tree-title .launchpad {
  margin-left: 25px;
}

.sl-vue-tree-title .child-nav-link {
  padding: 0;
}
.disabled {
  pointer-events: none; //This makes it not clickable
  opacity: 0.6; //This grays it out to look disabled
  padding: 10px;
}

.ctx-menu-overwrite {
  min-width: 120px;
  padding: 10px;
  cursor: pointer;
  width: 100%;
  margin-bottom: 0px;
}
.ctx-menu-overwrite:hover {
  background-color: rgba(185, 179, 179, 0.833);
}
.delete-modal {
  background-color: #fffaf3;
  color: #573a08;
  border-radius: 5px;
  padding: 1em 1.5em;
  border: 1px solid #573a08;
}
.delete-modal-font {
  font-size: 0.9em;
}
</style>
