<template>
  <div
    class="sl-vue-tree"
    :class="{ 'sl-vue-tree-root': isRoot }"
    @mousemove="onMousemoveHandler"
    @mouseleave="onMouseleaveHandler"
    @dragend="onDragendHandler(null, $event)"
  >
    <div ref="nodes" class="sl-vue-tree-nodes-list">
      <div v-for="(node, nodeInd) in nodes" :key="node.version" :class="{ 'sl-vue-tree-selected': node.isSelected }" class="sl-vue-tree-node">
        <div
          class="sl-vue-tree-cursor sl-vue-tree-cursor_before"
          :style="{
            visibility:
              cursorPosition && cursorPosition.node.pathStr === node.pathStr && cursorPosition.placement === 'before' ? 'visible' : 'hidden',
          }"
          @dragover.prevent
        >
          <!-- suggested place for node insertion  -->
        </div>

        <div
          class="sl-vue-tree-node-item"
          :path="node.pathStr"
          :class="{
            'sl-vue-tree-cursor-hover': cursorPosition && cursorPosition.node.pathStr === node.pathStr,

            'sl-vue-tree-cursor-inside': cursorPosition && cursorPosition.placement === 'inside' && cursorPosition.node.pathStr === node.pathStr,
            'sl-vue-tree-node-is-leaf': node.isLeaf,
            'sl-vue-tree-node-is-folder': !node.isLeaf,
          }"
          @mousedown="onNodeMousedownHandler($event, node)"
          @mouseup="onNodeMouseupHandler($event, node)"
          @contextmenu="emitNodeContextmenu(node, $event)"
          @dblclick="emitNodeDblclick(node, $event)"
          @click="emitNodeClick(node, $event)"
          @dragover="onExternalDragoverHandler(node, $event)"
          @drop="onExternalDropHandler(node, $event)"
        >
          <div v-for="(gapInd, idx) in gaps" :key="idx" class="sl-vue-tree-gap"></div>

          <div v-if="level && showBranches" class="sl-vue-tree-branch">
            <slot name="branch" :node="node">
              <span v-if="!node.isLastChild"> {{ String.fromCharCode(0x251c) }}{{ String.fromCharCode(0x2500) }}&nbsp; </span>
              <span v-if="node.isLastChild"> {{ String.fromCharCode(0x2514) }}{{ String.fromCharCode(0x2500) }}&nbsp; </span>
            </slot>
          </div>

          <div class="sl-vue-tree-title">
            <!-- {{ node }} -->
            <!-- <div v-if="node.title === `CONNECTORS`">{{ node.children }}</div> -->
            <!-- <div v-if="node.title === `HuX`">{{ node.children }}</div> -->
            <slot name="title" :node="node">{{ node.title }}</slot>

            <slot v-if="!node.isLeaf && node.children.length == 0 && node.isExpanded" :node="node" name="empty-node"></slot>

            <div class="sl-vue-tree-sidebar">
              <slot name="sidebar" :node="node"></slot>
            </div>
          </div>

          <span v-if="!node.isLeaf" class="sl-vue-tree-sidebar float-right" @click="onToggleHandler($event, node)">
            <slot name="toggle" :node="node">
              <span>
                {{ !node.isLeaf ? (node.isExpanded ? '-' : '+') : '' }}
              </span>
            </slot>
          </span>
        </div>

        <sl-vue-tree
          v-if="node.children && node.children.length && node.isExpanded"
          :value="node.children"
          :level="node.level"
          :parent-ind="nodeInd"
          :allow-multiselect="allowMultiselect"
          :allow-toggle-branch="allowToggleBranch"
          :edge-size="edgeSize"
          :show-branches="showBranches"
          @dragover.prevent
        >
          <!-- eslint-disable vue/no-template-shadow -->
          <template slot="title" slot-scope="{ node }">
            <slot name="title" :node="node">{{ node.title }}</slot>
          </template>

          <template slot="toggle" slot-scope="{ node }">
            <slot name="toggle" :node="node">
              <span>
                {{ !node.isLeaf ? (node.isExpanded ? '-' : '+') : '' }}
              </span>
            </slot>
          </template>

          <template slot="sidebar" slot-scope="{ node }">
            <slot name="sidebar" :node="node"></slot>
          </template>

          <template slot="empty-node" slot-scope="{ node }">
            <!-- eslint-disable-next-line vue/attributes-order -->
            <slot name="empty-node" :node="node" v-if="!node.isLeaf && node.children.length == 0 && node.isExpanded"> </slot>
          </template>
        </sl-vue-tree>

        <div
          class="sl-vue-tree-cursor sl-vue-tree-cursor_after"
          @dragover.prevent
          :style="{
            visibility: cursorPosition && cursorPosition.node.pathStr === node.pathStr && cursorPosition.placement === 'after' ? 'visible' : 'hidden',
          }"
        >
          <!-- suggested place for node insertion  -->
        </div>
      </div>

      <div v-show="isDragging" v-if="isRoot" ref="dragInfo" class="sl-vue-tree-drag-info">
        <slot name="draginfo"> Items: {{ selectionSize }} </slot>
      </div>
    </div>
  </div>
</template>

<script src="./sl-vue-tree.js"></script>

<style src="./sl-vue-tree-dark.css"></style>
