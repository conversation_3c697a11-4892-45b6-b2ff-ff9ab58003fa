<template>
  <nav class="navbar fixed-top cs-navbar-dark flex-md-nowrap p-0 shadow">
    <div class="container-fluid">
      <span class="float-left">
        <img src="@/assets/coalesce-logo-sign.png" class="coalesce-logo-sign" />
        <img src="@/assets/coalesce-logo-text.png" class="coalesce-logo-text" />
        <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
      </span>
    </div>

    <!-- <form class="language-select navbar-form navbar-right" role="language">
      <div class="form-group">
        <select @input="selectLanguage" class="selectpicker">
          <option v-for="{ name, code, title, text, id } in langs" :key="id" :value="code">{{ title }}</option>
        </select>
      </div>
    </form> -->
    <a
      class="settings-icon mr-3 "
      v-if="
        pm &&
          pm.hasOne([
            { can: 'list-general-settings', forGroup: 'globalsettings' },
            { can: 'list-messages', forGroup: 'globalsettings' },
            { can: 'list', forGroup: 'globalsettings' },
          ])
      "
      href="#/globalsettings"
      v-b-tooltip.hover
      title="settings"
    >
      <!--b-icon scale="1.5" icon="gear-fill" aria-hidden="true"></b-icon-->
      <font-awesome-icon icon="cog" style="font-size:1.8em"></font-awesome-icon>
      <!-- <a class="link cs-text-primary-on-hover px-4">Global Settings</a> -->
    </a>

    <!-- <span class="customer-title"><h2>Moov Togo</h2></span> -->
    <span class="customer-title"
      ><h2>{{ operatorName }}</h2></span
    >
    <!-- <b-button variant="outline-light">Light</b-button> -->

    <ul class="navbar-nav">
      <li class="nav-item text-nowrap">
        <a
          class="user-dropdown-btn cursor-pointer"
          href="#"
          v-tooltip.bottom-end="{
            classes: tooltips.dropdownBtn.type,
            content: tooltips.dropdownBtn.content,
            trigger: 'manual',
            offset: 10,
            show: tooltips.dropdownBtn.visible,
          }"
        >
          <div class="mr-3" style="">
            <img v-show="user.picture" class="navbar-btn-click picture" :src="user.picture" />
            <h6 v-show="!user.picture" class="navbar-btn-click bg-dark mb-0 text-light rounded-circle" style="padding:0.8rem;">
              <!-- eslint-disable-next-line vue/no-v-html -->
              <span class="navbar-btn-click" v-html="userInitials"></span>
            </h6>
          </div>
        </a>
      </li>
    </ul>
    <transition name="fade">
      <div class="user-dropdown border pt-3 pb-2 flex-column" v-show="userDropdownOpen" ref="dropDownMenu">
        <div class="px-4 py-2 mb-2 d-flex flex-row align-items-center">
          <div class="px-1 mb-1">
            <input style="display:none" ref="selectFile" type="file" @input="onFileSelect" />

            <div
              @click="$refs.selectFile.click()"
              class="cursor-pointer picture-upload-btn"
              v-tooltip.left="{
                classes: tooltips.updatePicture.type,
                content: tooltips.updatePicture.content,
                trigger: 'manual',
                offset: 10,
                show: tooltips.updatePicture.visible,
              }"
            >
              <div class="upload-icon-overlay justify-content-center align-items-center">
                <i class="fas fa-image" style="font-size:3em;"></i>
              </div>
              <h5 v-show="!user.picture" class="bg-info p-4 rounded-circle mb-0">
                <!-- eslint-disable-next-line vue/no-v-html -->
                <span v-html="userInitials"></span>
              </h5>
              <img v-show="user.picture" class="picture" name="picture" :src="user.picture" />
            </div>
          </div>
          <div class="d-flex flex-column px-3 pr-5">
            <div class="">{{ user.firstname + ' ' + user.lastname }}</div>
            <div class="">{{ user.email }}&nbsp;</div>
            <!--div class="">
              <a class="cs-text-primary cs-text-primary-on-hover bold" href="#"
                >Privacy</a
              >
            </div-->
          </div>
        </div>
        <div
          class="mx-3 border-bottom d-flex flex-column"
          v-if="
            pm &&
              pm.hasAll([
                { can: 'invite', a: 'user' },
                { can: 'list', forGroup: 'user' },
              ])
          "
        ></div>
        <div class="py-2 d-flex flex-column">
          <a v-if="pm && pm.hasPermission({ can: 'invite', a: 'user' })" href="#/inviteuser" class="link cs-text-primary-on-hover px-4"
            >Invite People</a
          >
          <a v-if="pm && pm.hasPermission({ can: 'list', forGroup: 'user' })" href="#/usermanagement" class="link cs-text-primary-on-hover px-4"
            >User Management</a
          >
        </div>
        <div class="mx-3 border-bottom d-flex flex-column"></div>
        <div class="py-2 ">
          <a href="#/userprofile" class="link cs-text-primary-on-hover px-4">My Profile</a>
        </div>
        <div class="mx-3 border-bottom d-flex flex-column"></div>
        <div class="py-2">
          <a class="link cs-text-primary-on-hover px-4" @click="logout" href="#">Log Out</a>
        </div>
      </div>
    </transition>
  </nav>
</template>

<script>
/* eslint-disable import/no-unresolved */
import { mapGetters } from 'vuex';
import { hideTooltip, showTooltip } from '@/utils/tooltip-alerts';
import Hamburger from '@/components/Hamburger';
import PermissionManager from '@/utils/PermissionManager';

export default {
  components: {
    Hamburger,
  },
  data() {
    return {
      defaultOperatorName: 'Operator',
      uploadedPicture: '',
      userDropdownOpen: false,
      tooltips: {
        dropdownBtn: {
          visible: false,
          timeout: null,
          type: '',
          classes: 'user-dropdown-btn with-arrow',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
        updatePicture: {
          visible: false,
          timeout: null,
          type: '',
          classes: 'with-arrow',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
      },
    };
  },
  computed: {
    ...mapGetters(['user', 'sidebar', 'langs', 'userPermissions', 'globalSettings']),
    pm() {
      if (!this.userPermissions) return undefined;

      const pm = new PermissionManager(this.userPermissions);
      return pm;
    },
    userInitials() {
      if (this.user) {
        const firstnameInitial = this.user.firstname ? this.user.firstname.slice(0, 1) : '';
        const lastnameInitial = this.user.lastname ? this.user.lastname.slice(0, 1) : '';
        return firstnameInitial + lastnameInitial;
      }

      return '&emsp;';
    },
    operatorName() {
      let name = '';
      // eslint-disable-next-line vue/no-async-in-computed-properties
      setTimeout(() => {
        if (!name) name = this.defaultOperatorName;
      }, 1000);

      this.globalSettings.generalSettings.forEach(setting => {
        if (setting.name.match(/operator name/i)) {
          name = setting.value;
        }
      });
      return name;
    },
  },
  mounted() {
    if (typeof this.$route.query['first-time'] !== 'undefined') this.firstTime = true;

    if (this.firstTime) {
      showTooltip(this.tooltips.dropdownBtn, 'cs-success', 'Add a profile photo');
      console.log("first login -- offering 'change picture' tooltip");
    }
  },
  created() {
    document.addEventListener('click', this.documentClick);
  },
  destroyed() {
    // Clean Up!
    document.removeEventListener('click', this.documentClick);
  },
  methods: {
    uploadPicture() {
      const user = {
        // ---- only submit Email, because ID (if submitted) will be used as precedence over email
        email: this.$store.state.user.user.email,
        picture: this.uploadedPicture,
      };
      console.log('user picture to update', user);
      this.$store
        .dispatch('user/updateUser', user)
        .then(response => {
          console.log('response', response);
        })
        .catch(err => {
          console.log('err', err);
        });
    },
    onFileSelect(events) {
      // debugger;
      const img = events.target.files[0];
      console.log('picture Img:', img);

      if (!img.type.match(/^image\/(png|jpe?g)$/)) {
        showTooltip(this.tooltips.updatePicture, 'cs-warning', 'Only PNG and JPG are supported, Please choose the correct file type.', 5);
        return;
      }

      if (isNaN(parseInt(img.size, 10)) || img.size > 256 * 1024) {
        showTooltip(this.tooltips.updatePicture, 'cs-warning', 'Maximum file size is 256KB', 5);
        return;
      }

      const reader = new FileReader();
      reader.addEventListener('load', () => {
        this.uploadedPicture = reader.result;
        this.uploadPicture();
        hideTooltip(this.tooltips.updatePicture);
        this.firstTime = false;
      });

      reader.readAsDataURL(img);
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar');
    },
    async logout() {
      await this.$store.dispatch('user/logout');
      // ---- remove firsttime if logging out - prevents the "choose picture" from displaying AGAIN if they logout
      const fullPath = this.$route.fullPath.replace(/\?first-time=1/, '');
      this.$router.push(`/login?redirect=${fullPath}`);
    },
    documentClick(e) {
      const el = this.$refs.dropDownMenu;

      /*
       *  DROP DOWN handling (order of criteria is important)
       */

      // ---- If the dropdown button is clicked -- "toggle" dropdown
      if (e.target.classList.contains('navbar-btn-click')) {
        this.userDropdownOpen = !this.userDropdownOpen;
        hideTooltip(this.tooltips.dropdownBtn);
        if (this.firstTime) {
          showTooltip(
            this.tooltips.updatePicture,
            'cs-info',
            "<div class='d-flex flex-column justify-content-center'>" +
              "<span><i class='fas fa-image fa-2x'></i></span>" +
              '<span>Select a photo from your computer</span>' +
              '</div>',
          );
        }
      } else if (e.target.classList.contains('link') || (el !== e.target && !el.contains(e.target))) {
        // ---- If a link is clicked - OR - Anywhere else on the page (outside dropdown) - then CLOSE dropdown
        this.userDropdownOpen = false;
        hideTooltip(this.tooltips.updatePicture);
      }
    },
    selectLanguage(e) {
      this.$store.dispatch('app/setLanguage', e.target.value);
      window.MenuPlus.curLang = e.target.value;
      window.MenuPlus.redrawWithNewConfig();
    },
    // ---- Otherwise... anywhere IN the dropdown, that is NOT a link -- don't do anything (i.e. leave dropdown open)
  },
};
</script>

<style lang="scss">
$coalesce_green: #39b54a;

.tooltip.user-dropdown-btn .tooltip-arrow {
  left: 110px !important;
}

a:hover {
  text-decoration: none;
}

.language-select {
  margin: 10px 20px 0 0;
}

.language-select .selectpicker {
  padding: 4px;
  border-radius: 3px;
  background-color: #eeeeee;
  width: 140px;
}

.customer-title h2 {
  margin-right: 1rem;
  margin-top: 5px;
  white-space: nowrap;
  color: #eeeeee;
  // border: 1px solid white;
  border-radius: 6px;

  padding: 2px 20px;
  font-size: 25px;
  font-weight: 400;
  border: 1px solid white;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  height: 35px;
  cursor: default;
}

.bold {
  font-weight: bold;
}

.navbar-nav {
  .user-dropdown-btn .picture {
    border-radius: 50%;
    height: 45px;
    width: 45px;
  }
}
.user-dropdown {
  position: absolute;
  right: 1rem;
  top: 5rem;
  background-color: white;
  color: black;
  display: flex;

  .picture {
    /*padding: 0.4em 0.9em 0.4em 0;*/ /* Cool image Warping with padding effects */
    padding: 0;
    border-radius: 50%;
    width: 90px;
    height: 90px;
  }
  .link {
    display: block;
    width: 100%;
    padding: 0.2rem 0.5rem;
    color: inherit;
    text-decoration: none;
    border-radius: 4px;
    &:hover {
      background: #f1f1f1;
    }
  }
}

#overlay {
  position: fixed; /* Sit on top of the page content */
  display: none; /* Hidden by default */
  width: 100%; /* Full width (cover the whole page) */
  height: 100%; /* Full height (cover the whole page) */
  top: 57px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: white; /* Black background with opacity */
  z-index: 2; /* Specify a stack order in case you're using a different order for other elements */
  cursor: pointer; /* Add a pointer on hover */
}
</style>

<style lang="scss" scoped>
.picture-upload-btn {
  position: relative;
  .upload-icon-overlay {
    transition: all;
    transition-duration: 0.4s;
    display: none;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
  }
  &:hover {
    opacity: 0.8;
    .upload-icon-overlay {
      opacity: 0.5;
      display: flex;
    }
  }
}
.cs-navbar-dark {
  background-color: #434343 !important;
}
.navbar {
  height: 57px;
  /*overflow: hidden;*/
  position: fixed;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .coalesce-logo-sign {
    margin-top: 8px;
    margin-left: 10px;
    width: 32px;
    height: 32px;
    float: left;
    vertical-align: middle;
  }

  .coalesce-logo-text {
    margin-left: 8px;
    margin-top: 14px;
    height: 18px;
    float: left;
    vertical-align: middle;
  }

  .settings-icon {
    color: white;
    cursor: pointer;
  }
}
</style>
