<template>
  <div style="overflow-y:hidden;">
    <div class="pb-4 d-flex flex-column">
      <b-form class="d-flex flex-row justify-content-between w-100 my-3" @submit.prevent="reloadTable()">
        <div class="d-flex">
          <b-form-input
            style="min-width:17em"
            class="mx-1"
            placeholder="Search for MSISDN-A"
            v-model="vuetableConfig.appendParams.filter"
          ></b-form-input>
          <button class="btn cs-btn cs-btn-submit py-0 mx-2" type="submit" style="min-width:7rem;">
            Search&nbsp;<i class="ml-1 fas" size="sm" :class="searchIcon"></i>
          </button>
        </div>
        <!-- REMOVE THIS WHEN THE BELOW SECTION (FILTERS) IS ENABLED -->
        <div class="mx-2" v-if="true">
          <b-form-select style="min-width:4rem;" v-model="perPageSelected" :options="perPageOptions"></b-form-select>
        </div>
      </b-form>
      <!-- dummy buttons -->
      <div class="d-flex justify-content-between" v-if="false">
        <div class="d-flex flex-sm-column flex-lg-row">
          <b-dropdown disabled id="dropdown-1" text="Today" variant="outline-secondary" class="my-1">
            <b-dropdown-item>First Action</b-dropdown-item>
            <b-dropdown-item>Second Action</b-dropdown-item>
            <b-dropdown-item>Third Action</b-dropdown-item>
            <b-dropdown-divider></b-dropdown-divider>
            <b-dropdown-item active>Active action</b-dropdown-item>
            <b-dropdown-item disabled>Disabled action</b-dropdown-item>
          </b-dropdown>
          <b-dropdown disabled id="dropdown-2" text="All Applications" variant="outline-secondary" class="ml-3 my-1"> </b-dropdown>
          <b-dropdown disabled id="dropdown-3" text="All Short Codes" variant="outline-secondary" class="ml-3 my-1"> </b-dropdown>
          <button disabled class="ml-4 my-1 btn cs-btn cs-btn-inactive"><span>Apply Filters</span>&emsp;<i class="fas fa-filter"></i></button>
        </div>
        <div class="mx-2">
          <b-form-select style="min-width:4rem;" v-model="perPageSelected" :options="perPageOptions"></b-form-select>
        </div>
      </div>
    </div>
    <div>
      <vuetable
        :css="css.table"
        v-if="fields.length !== 0"
        @vuetable:loaded="tableLoaded"
        @vuetable:load-error="handleLoadError"
        ref="vuetable"
        api-url="/elasticsearch/search/vuetable"
        :append-params="vuetableConfig.appendParams"
        :http-options="vuetableConfig.httpOptions"
        :fields="fields"
        @vuetable:pagination-data="onPaginationData"
        pagination-path=""
      ></vuetable>
      <div class="alert alert-warning mb-4" v-if="errorText.length !== 0">{{ errorText }}</div>
    </div>
    <div class="d-flex justify-content-between" v-if="fields.length !== 0 && !hasLoadError && errorText.length === 0">
      <vuetable-pagination-info ref="paginationInfo"></vuetable-pagination-info>
      <vuetable-pagination class="pagination-comp" ref="pagination" @vuetable-pagination:change-page="onChangePage"></vuetable-pagination>
    </div>
    <div v-else-if="errorText.length === 0">
      <h5 class="p-4 text-bold text-info">Loading...</h5>
    </div>
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved */
import { getToken } from '@/utils/auth';
import { mapActions } from 'vuex';
import { Vuetable, VuetablePaginationInfo } from 'vuetable-2';
import moment from 'moment';
import VuetablePagination from './VuetablePaginationBootstrap4.vue';
import CssConfig from './VuetableBootstrap4Config';

export default {
  name: 'Dashboard',
  components: {
    Vuetable,
    VuetablePagination,
    VuetablePaginationInfo,
  },
  data() {
    return {
      css: CssConfig,
      hasLoadError: false,
      errorText: '',
      allFields: [],
      fields: [],
      perPageSelected: 5,
      perPageOptions: [
        { value: 5, text: '5' },
        { value: 25, text: '25' },
        { value: 50, text: '50' },
      ],
      vuetableConfig: {
        activeSearch: false,
        fieldMapping: [
          {
            name: 'timestamp',
            title: 'Date & Time',
            formatter: value => {
              const v = value || null;
              if (!v) return '';
              const d = moment(`${v}`).format('YYYY-MM-DD HH:mm:ss.SSS');
              // const converted = d
              return d;
            },
          },
          {
            name: 'application',
            title: 'Application',
          },
          {
            name: 'msisdn',
            title: 'MSISDN-A',
          },
          {
            name: 'USSDServiceCode',
            title: 'USSD Service Code',
          },
          {
            name: 'USSDRequestString',
            title: 'USSD Request String',
          },
        ],
        httpOptions: {
          baseURL: '/',
          headers: {
            Authorization: getToken(),
          },
        },
        appendParams: {
          sort: 'timestamp|desc',
          per_page: 5,
          filter: '',
        },
      },
    };
  },
  computed: {
    searchIcon() {
      return this.vuetableConfig.activeSearch ? 'fa-circle-notch fa-spin' : 'fa-search';
    },
  },
  watch: {
    perPageSelected(current) {
      if (this.vuetableConfig.appendParams.per_page !== current) {
        this.vuetableConfig.appendParams.per_page = current;
        this.$refs.vuetable.refresh();
      }
    },
  },
  created() {
    this.getFields()
      .then(res => {
        const { fields } = res;
        const existingFields = [];
        this.vuetableConfig.fieldMapping.forEach(mappedField => {
          existingFields.push(mappedField.name);
        });
        fields.forEach(field => {
          let formatter;
          if (field.type === 'date') {
            console.debug(`Field type for '${field.name}' is ${field.type}, using customer formatter`);
            formatter = value => {
              const v = value || null;
              if (!v) return '';
              const d = moment(`${v}`).format('YYYY-MM-DD HH:mm:ss.SSS');
              // const converted = d
              return d;
            };
          } else {
            formatter = value => value;
          }
          if (!existingFields.includes(field.name)) {
            this.vuetableConfig.fieldMapping.push({
              name: field.name,
              formatter,
            });
          }
        });
        this.allFields = this.vuetableConfig.fieldMapping;
        this.fields = [...this.allFields];
        console.debug('getFields() result:\n', res);
      })
      .catch(err => {
        this.errorText = "We've encountered an issue retrieving the TDR's";
        console.error('getFields() error:\n', err);
      });
  },
  methods: {
    reloadTable() {
      this.vuetableConfig.activeSearch = true;
      this.$refs.vuetable.refresh();
    },
    tableLoaded() {
      const { tableData } = this.$refs.vuetable;

      let fields = [];
      tableData.forEach(row => {
        fields = [...Object.keys(row), ...fields];
      });

      // make a copy
      this.fields = [...this.allFields];

      this.fields.forEach((field, idx) => {
        if (!fields.includes(field.name)) {
          console.debug(`Removed unused field from table header: ${field.name}`);
          delete this.fields[idx];
        }
      });
      this.$refs.vuetable.normalizeFields();
      this.vuetableConfig.activeSearch = false;
    },
    ...mapActions({ getFields: 'elasticSearch/fields' }),
    transform: paginationData => {
      console.debug(paginationData);
      return paginationData;
    },
    handleLoadError(error) {
      this.hasLoadError = true;
      console.debug('Vuetable Load Error Reason:\n', error);
    },
    onPaginationData(paginationData) {
      this.$refs.pagination.setPaginationData(paginationData);
      this.$refs.paginationInfo.setPaginationData(paginationData);
    },
    onChangePage(page) {
      this.$refs.vuetable.changePage(page);
    },
  },
};
</script>

<style lang="scss" scoped>
// .btn {
//   padding: 0.375rem 5rem !important;
// }

.pagination-comp {
  margin-top: 2rem;
  cursor: pointer;
  font-size: 18px;
}
.dashboard {
  &-container {
    margin: 30px;
  }
  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}

.editor {
  width: 1200px;
  height: 600px;
}
</style>
<style lang="scss">
.vuetable-head-wrapper table.vuetable th.sortable {
  cursor: pointer;
}
</style>
