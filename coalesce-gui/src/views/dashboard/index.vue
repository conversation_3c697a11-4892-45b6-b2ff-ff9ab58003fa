<template>
  <div class="dashboard-container">
    <div class="dashboard-text pb-4 d-flex align-items-center">
      <i class="fas fa-columns fa-2x"></i>
      <h2 class="ml-3 mt-1">Dashboards</h2>
    </div>
    <b-tabs active-nav-item-class="active-tab-text" content-class="mt-3" fill>
      <b-tab title="TDRs" title-link-class="h6 mb-0" active>
        <p class="p-4">
          <tdrs></tdrs>
        </p>
      </b-tab>
      <b-tab title="EDRs" title-link-class="h6 mb-0">
        <p class="p-4">
          <b-alert class="m-4" show variant="warning">Not Implemented Yet</b-alert>
        </p>
      </b-tab>
      <b-tab title="System Logs" title-link-class="h6 mb-0">
        <p class="p-4">
          <b-alert class="m-4" show variant="warning">Not Implemented Yet</b-alert>
        </p>
      </b-tab>
    </b-tabs>
  </div>
</template>

<script>
// eslint-disable-next-line import/no-unresolved
import tdrs from './tdrs.vue';

export default {
  name: 'Dashboard',
  components: {
    tdrs,
  },
  data() {
    return {};
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
@import '../../styles/cs-styles.scss';

.dashboard {
  &-container {
    margin: 30px;
  }
  &-text {
    font-size: 30px;
    line-height: 46px;
    color: $cs_color_primary;
  }
}

.editor {
  width: 1200px;
  height: 600px;
}
</style>
