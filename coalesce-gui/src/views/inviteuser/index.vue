<template>
  <div class="invite-user-container" tabindex="0" @keydown.esc="escape">
    <div class="row">
      <div class="col" style="max-width:800px; margin:auto;">
        <div class="row">
          <div class="col-11 invite-user-text">User Management</div>
          <div class="col-1" style="padding-top:0px; color:#666666;">
            <button
              class="invite-user-button-unstyled invite-user-fullscreen_modal__close"
              type="button"
              @click="escape"
              aria-label="Close"
              style="padding-top:4px;"
            >
              <div style="text-align:center; font-size:45px; line-height:50px;">
                &times;
              </div>
              <div class="cs-text-primary bold" style="position:relative; top:-14px; margin:auto;">
                esc
              </div>
            </button>
          </div>
        </div>

        <h5 class="cs-heading" style="margin:10px 0 10px 0;">
          Invite New User
        </h5>

        <div class="row" style="margin-bottom:20px;">
          <div class="col">
            <img style="max-width:450px;" src="@/assets/images/users.png" />
          </div>
        </div>

        <form @submit="sendInvite">
          <div class="container-fluid">
            <div class="form-group row" :class="{ 'form-group--error': $v.data.email.$error }">
              <label for="inviteuser-email" autofocus class="col-12 col-sm-auto col-form-label"><div style="width:150px;">User's Email</div></label>
              <div class="col-12 col-sm">
                <input
                  type="email"
                  @input="delayTouch($v.data.email)"
                  class="form-control"
                  id="email"
                  name="email"
                  ref="email"
                  placeholder="Email address"
                  :class="{
                    'dirty-valid': $v.data.email.$dirty && !$v.data.email.$error,
                  }"
                  v-model.trim="data.email"
                  @blur="$v.data.email.$touch()"
                />
                <div class="error" v-if="!$v.data.email.required">
                  Field is required
                </div>
                <div class="error" v-if="!$v.data.email.email">
                  Must be a valid email address
                </div>
              </div>
            </div>
            <div class="form-group row" :class="{ 'form-group--error': $v.data.firstname.$error || $v.data.lastname.$error }">
              <label for="firstname" class="col-12 col-sm-auto col-form-label"><div style="width:150px;">User's Name</div></label>
              <div class="col-12 col-sm">
                <input
                  type="text"
                  @input="delayTouch($v.data.firstname)"
                  class="form-control"
                  id="firstname"
                  name="firstname"
                  placeholder="First name (required)"
                  :class="{
                    'dirty-valid': $v.data.firstname.$dirty && !$v.data.firstname.$error,
                  }"
                  v-model.trim="$v.data.firstname.$model"
                />
              </div>
              <div class="col-12 col-sm">
                <input
                  type="text"
                  @input="delayTouch($v.data.lastname)"
                  class="form-control"
                  id="lastname"
                  name="lastname"
                  placeholder="Last name (required)"
                  :class="{
                    'dirty-valid': $v.data.lastname.$dirty && !$v.data.lastname.$error,
                  }"
                  v-model.trim="$v.data.lastname.$model"
                />
              </div>
            </div>

            <hr />

            <h5 class="cs-heading" style="margin:20px 0 20px 0;">
              Account Type
            </h5>

            <div>
              <b-form-group label="">
                <div v-for="role in data.roles" :key="role">
                  <b-form-radio v-model="data.account_type" name="some-radios" :value="role">
                    {{ role.replace(/^./, role[0].toUpperCase()) }}
                  </b-form-radio>
                </div>
              </b-form-group>
            </div>

            <!--div class="form-group row" style="margin-bottom:0;">
              <label
                for="inviteuser-type-system-admin"
                class="col-12 col-sm-auto col-form-label"
                ><div style="width:150px;">System Admin</div></label
              >
              <div class="col-12 col-sm">
                <label class="switch">
                  <input
                    id="inviteuser-type-system-admin"
                    type="checkbox"
                    v-model="$v.data.account_type.system_admin.$model"
                  />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>
            <div class="form-group row" style="margin-bottom:0;">
              <label
                for="inviteuser-type-system-admin"
                class="col-12 col-sm-auto col-form-label"
                ><div style="width:150px;">Customer Care</div></label
              >
              <div class="col-12 col-sm">
                <label class="switch">
                  <input
                    id="inviteuser-type-system-admin"
                    type="checkbox"
                    v-model="$v.data.account_type.customer_care.$model"
                  />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>
            <div class="form-group row" style="margin-bottom:0;">
              <label
                for="inviteuser-type-system-admin"
                class="col-12 col-sm-auto col-form-label"
                ><div style="width:150px;">Menu Designer</div></label
              >
              <div class="col-12 col-sm">
                <label class="switch">
                  <input
                    id="inviteuser-type-system-admin"
                    type="checkbox"
                    v-model="$v.data.account_type.menu_designer.$model"
                  />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>
            <div class="form-group row" style="margin-bottom:0;">
              <label
                for="inviteuser-type-system-admin"
                class="col-12 col-sm-auto col-form-label"
                ><div style="width:150px;">Service Developer</div></label
              >
              <div class="col-12 col-sm">
                <label class="switch">
                  <input
                    id="inviteuser-type-system-admin"
                    type="checkbox"
                    v-model="$v.data.account_type.service_developer.$model"
                  />
                  <span class="slider round"></span>
                </label>
              </div>
            </div-->

            <hr />

            <div class="form-group row">
              <label for="inviteuser-type-system-admin" class="col-12 col-sm-auto col-form-label"><div style="width:150px;"></div></label>
              <div class="col-12 col-sm">
                <span
                  v-tooltip.right="
                    (!data.firstname || !data.lastname || !data.email || !data.account_type) && { content: 'Missing Fields', offset: 10 }
                  "
                >
                  <button
                    type="submit"
                    :disabled="$v.$invalid && !data.sending"
                    class="btn btn-primary cs-btn"
                    v-tooltip.right="{
                      classes: data.tooltips.sendButton.type,
                      content: data.tooltips.sendButton.content,
                      trigger: 'manual',
                      offset: 0,
                      show: data.tooltips.sendButton.visible,
                    }"
                  >
                    Send Invitation &nbsp;&nbsp;<i
                      class="fa"
                      :class="{
                        'fa-long-arrow-alt-right': !data.sending,
                        'fa-spinner fa-spin': data.sending,
                      }"
                    ></i>
                  </button>
                </span>
                <span :class="{ 'd-none': !data.show.msg_success }"
                  >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="badge badge-success cs-msg-label">Invitation successfully sent</span></span
                >
                <span :class="{ 'd-none': !data.show.msg_failure }"
                  >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="badge badge-danger cs-msg-label">Failed to send invitation</span></span
                >
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved */
import { mapGetters } from 'vuex';
import { required, email } from 'vuelidate/lib/validators';
import { hideTooltip, showTooltip } from '@/utils/tooltip-alerts';

const touchMap = new WeakMap();

export default {
  name: 'InviteUser',
  data() {
    return {
      data: {
        roles: [],
        tooltips: {
          sendButton: {
            visible: false,
            timeout: null,
            type: 'cs-error',
            invisibleInSeconds: 0, // default is 0, will not clear the tooltip
            content: '',
          },
        },
        firstname: '',
        lastname: '',
        email: '',
        account_type: '',
        sending: false,
        show: {
          msg_success: false,
          msg_failure: false,
        },
        failure: '',
        lastSidebarState: null,
      },
    };
  },
  validations: {
    data: {
      email: {
        required,
        email,
      },
      firstname: { required },
      lastname: { required },
      account_type: { required },
    },
  },
  computed: {
    ...mapGetters(['sidebar']),
  },
  mounted() {
    // ---- Maintain sidebar state if we "ESCAPE" from this page
    // (i.e. go back to 'closed' if it was 'closed' ... or 'open' if it was 'open', see 'escape()' method)
    this.lastSidebarState = this.sidebar.opened;

    // ---- Default is to close the sidebar if we load the 'invite' page.
    this.sidebar.opened = false;
    this.$refs.email.focus();
    this.$store
      .dispatch('user/listRoles')
      .then(res => {
        res.roles.forEach(roleObject => {
          this.data.roles.push(roleObject.name);
        });
      })
      .catch(err => {
        console.log('Could not retrieve roles from the server :/\n', err.message || err);
      });
  },
  methods: {
    delayTouch($v) {
      hideTooltip(this.data.tooltips.sendButton);
      $v.$reset();
      if (touchMap.has($v)) {
        clearTimeout(touchMap.get($v));
      }
      touchMap.set($v, setTimeout($v.$touch, 700));
    },
    escape() {
      hideTooltip(this.data.tooltips.sendButton);
      // this.$parent.closeInviteUser();
      this.sidebar.opened = this.lastSidebarState;
      this.$router.go(-1);
    },
    sendInvite(e) {
      hideTooltip(this.data.tooltips.sendButton);
      e.preventDefault();

      if (!this.$v.$invalid) {
        this.data.sending = 1;

        const postData = {
          email: this.data.email,
          firstname: this.data.firstname,
          lastname: this.data.lastname,
          role: [this.data.account_type],
        };

        this.$store
          .dispatch('user/inviteUser', postData)
          .then(() => {
            this.data.email = '';
            this.data.firstname = '';
            this.data.lastname = '';
            this.data.account_type = '';
            this.$v.$reset();
            showTooltip(this.data.tooltips.sendButton, 'cs-success', 'Invitation Successfully Sent!', 5);
            this.$refs.email.focus();
          })
          .catch(err => {
            if (String(err.message).match(/403/)) {
              showTooltip(this.data.tooltips.sendButton, 'cs-warning', 'Sorry, you do not have permission to perform this action', 5);
            } else {
              showTooltip(this.data.tooltips.sendButton, 'cs-error', err.message || err, 15);
            }
          })
          .then(() => {
            this.data.sending = 0;
          });
      }
    },
    onSubmit() {
      // this.$message('submit!')
    },
    onCancel() {
      this.$message({
        message: 'cancel!',
        type: 'warning',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.invite-user {
  &-container {
    margin: 30px;
    outline: none;
  }
  &-text {
    font-size: 30px;
    line-height: 46px;
  }

  &-fullscreen_modal__back:hover,
  &-fullscreen_modal__close:hover {
    background-color: #ddd;
    color: #1d1c1d;
  }
  &-button-unstyled:active,
  &-button-unstyled:focus,
  &-button-unstyled:hover {
    outline: none;
  }
  &-fullscreen_modal__close {
    margin-left: 16px;
    right: 15px;
  }
  &-fullscreen_modal__back,
  &-fullscreen_modal__close {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    transition: opacity 0.2s ease-out;
    align-items: center;
    border-radius: 100%;
    box-shadow: none;
    color: #616061;
    display: flex;
    flex-direction: column;
    height: 64px;
    justify-content: center;
    position: absolute;
    text-align: center;
    vertical-align: middle;
    width: 64px;
    top: 15px;
    -webkit-app-region: no-drag;
  }
  &-button-unstyled {
    background: none;
    border: 0;
    color: inherit;
    font: inherit;
    margin: 0;
    line-height: inherit;
    overflow: initial;
    padding: 0;
    text-align: initial;
    vertical-align: initial;
    cursor: pointer;
  }
}
</style>
