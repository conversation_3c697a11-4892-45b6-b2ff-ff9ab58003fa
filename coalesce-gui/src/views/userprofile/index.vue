<template>
  <div class="user-profile-container">
    <div class="row">
      <div class="col user-profile-page">
        <div class="row no-gutters mt-2">
          <div class="d-flex w-100 flex-column text-center flex-xl-row">
            <div class="m-3 mx-4">
              <h1 class="mb-3">User Profile</h1>
              <div class="d-flex justify-content-center">
                <input style="display:none" ref="selectFile" type="file" @input="onFileSelect" />
                <div
                  @click="$refs.selectFile.click()"
                  class="align-self-start cursor-pointer picture-upload-btn"
                  v-tooltip.bottom-start="{
                    classes: data.tooltips.updatePicture.type,
                    content: data.tooltips.updatePicture.content,
                    trigger: 'manual',
                    offset: 10,
                    show: data.tooltips.updatePicture.visible,
                  }"
                >
                  <div class="upload-icon-overlay justify-content-center align-items-center">
                    <i class="fas fa-image" style="font-size:3em;"></i>
                  </div>
                  <h5
                    v-show="!user.picture || user.picture == ''"
                    class="picture-initials bg-info rounded-circle flex-row justify-content-center align-items-center mb-0"
                  >
                    <!-- eslint-disable-next-line vue/no-v-html -->
                    <span v-html="user.firstname.slice(0, 1) + user.lastname.slice(0, 1)"></span>
                  </h5>
                  <div v-show="user.picture">
                    <img :src="user.picture" class="picture" />
                  </div>
                </div>
                <div class="d-flex flex-column justify-content-center px-3 user-profile-info">
                  <h5 class="bold mb-0">{{ `${user.firstname} ${user.lastname}` }}</h5>
                  <div class="text-secondary">{{ user.email }}</div>
                </div>
              </div>
            </div>
            <div class="my-3 flex-grow-1 border border-right-0 border-top-0 border-bottom-0">
              <div class="forms-div col-12">
                <div class="d-flex d-xl-none mr-4 border-top">
                  &nbsp;
                </div>
                <form
                  @submit.prevent="onUpdateDetails"
                  class="profile-info-form col-12 d-flex flex-column pl-4 pb-5 mt-3"
                  style="max-width:800px;"
                  ref="profileInfoForm"
                >
                  <div class="d-flex flex-row pb-4">
                    <h4 class="cs-text-primary">Profile Details</h4>
                  </div>
                  <div class="row no-gutters py-2">
                    <div class="col-12 col-lg-3 d-flex justify-content-start justify-content-lg-end pr-3">
                      <label class="m-0 align-self-center bold text-center text-lg-right">Email</label>
                    </div>
                    <div class="col-12 col-lg-9 justify-content-center justify-content-lg-start pr-3">
                      <div class="m-0 pl-1 text-left">{{ data.infoUpdateForm.email }}</div>
                    </div>
                  </div>
                  <div class="row no-gutters py-2">
                    <div class="col-12 col-lg-3 d-flex justify-content-start justify-content-lg-end pr-3">
                      <label class="m-0 align-self-center bold text-center text-lg-right">Name</label>
                    </div>
                    <div class="col-12 col-lg-9">
                      <div class="row">
                        <div class="col-12 col-lg-6 pt-2 pt-lg-0">
                          <input
                            id="firstname"
                            name="firstname"
                            class="form-control"
                            @click="borderControl"
                            @input="borderControl"
                            :class="data.classes.firstname"
                            placeholder="First Name"
                            v-model.trim="data.infoUpdateForm.firstname"
                          />
                        </div>
                        <div class="col-12 col-lg-6 pt-2 pt-lg-0">
                          <input
                            id="lastname"
                            name="lastname"
                            class="form-control"
                            @click="borderControl"
                            @input="borderControl"
                            :class="data.classes.lastname"
                            placeholder="Last Name"
                            v-model.trim="data.infoUpdateForm.lastname"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row no-gutters py-2">
                    <div class="col-12 col-lg-3 d-flex justify-content-start justify-content-lg-end pr-3">
                      <label class="m-0 align-self-center bold text-center text-lg-right">Position</label>
                    </div>
                    <div class="col-12 col-lg-9">
                      <div class="row no-gutters">
                        <div class="col-12 col-lg-10">
                          <input
                            id="position"
                            name="position"
                            class="form-control"
                            @click="borderControl"
                            @input="borderControl"
                            :class="data.classes.position"
                            placeholder="Job title or role"
                            v-model.trim="data.infoUpdateForm.position"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row no-gutters py-2">
                    <div class="col-12 col-lg-3 d-flex justify-content-start justify-content-lg-end pr-3">
                      <label class="m-0 align-self-center bold text-center text-lg-right">Mobile</label>
                    </div>
                    <div class="col-12 col-lg-9">
                      <div class="row no-gutters">
                        <div class="col-4 col-lg-3 pr-1">
                          <input
                            id="mobile_code"
                            name="mobile_code"
                            class="form-control"
                            @click="borderControl"
                            @input="borderControl"
                            :class="data.classes.mobile_code"
                            placeholder="Code"
                            v-model.trim="data.infoUpdateForm.mobile_code"
                          />
                        </div>
                        <div class="col-8 col-lg-7 pl-1">
                          <input
                            id="mobile_number"
                            name="mobile_number"
                            class="form-control"
                            @click="borderControl"
                            @input="borderControl"
                            :class="data.classes.mobile_number"
                            placeholder="Number"
                            v-model.trim="data.infoUpdateForm.mobile_number"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row no-gutters py-2">
                    <div class="col-12 col-lg-3 pr-3">
                      &nbsp;
                    </div>
                    <div class="col-12 col-lg-9 d-flex flex-column flex-sm-row justify-content-start">
                      <span
                        v-tooltip.right="
                          !hasprofileDetailsFormChanged &&
                            !data.tooltips.updateDetails.visible && {
                              content: 'Nothing on the form has changed',
                              offset: 10,
                            }
                        "
                      >
                        <button
                          type="submit"
                          class="mr-3 btn cs-btn"
                          :class="hasUpdated"
                          v-tooltip.right-start="{
                            classes: data.tooltips.updateDetails.type,
                            content: data.tooltips.updateDetails.content,
                            trigger: 'manual',
                            offset: 10,
                            show: data.tooltips.updateDetails.visible,
                          }"
                          :disabled="data.detailsUpdateButtonDisabled || !hasprofileDetailsFormChanged"
                        >
                          Update details
                        </button>
                      </span>
                    </div>
                  </div>
                </form>
                <form
                  @submit.prevent="onUpdatePassword"
                  action="#"
                  class="profile-pw-change-form border-top col-12 d-flex flex-column pl-4 pb-5 pt-4"
                  style="max-width:800px;"
                >
                  <div class="d-flex flex-row pb-4">
                    <h4 class="cs-text-primary">Change Password</h4>
                  </div>
                  <div class="row no-gutters py-2">
                    <div class="col-12 col-lg-4 d-flex justify-content-start justify-content-lg-end pr-3">
                      <label class="m-0 align-self-center bold text-center text-lg-right">Current Password</label>
                    </div>
                    <div class="col-12 col-lg-8">
                      <div class="input-group pword-input-group">
                        <input
                          id="current_password"
                          name="current_password"
                          :type="data.currentPasswordType"
                          @input="setUpdatablePW"
                          :class="data.classes.current_password"
                          class="form-control"
                          placeholder="current password"
                          v-model.trim="data.passwordUpdateForm.current_password"
                        />
                        <div class="input-group-append">
                          <div class="input-group-text border border-left-0 bg-light">
                            <!-- :class="classes.passwordInput"-->
                            <span class="show-pwd cursor-pointer" @click="showCurrentPwd()">
                              <font-awesome-icon
                                :icon="data.currentPasswordType === 'password' ? ['far', 'eye-slash'] : ['far', 'eye']"
                              ></font-awesome-icon>
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row no-gutters py-2">
                    <div class="col-12 col-lg-4 d-flex justify-content-start justify-content-lg-end pr-3">
                      <label class="m-0 align-self-center bold text-center text-lg-right">New Password</label>
                    </div>
                    <div class="col-12 col-lg-8">
                      <div class="input-group pword-input-group">
                        <input
                          id="password"
                          name="password"
                          :type="data.passwordType"
                          class="form-control"
                          @input="setUpdatablePW"
                          placeholder="new password"
                          v-model.trim="data.passwordUpdateForm.password"
                        />
                        <div class="input-group-append">
                          <div class="input-group-text border border-left-0 bg-light">
                            <span class="show-pwd cursor-pointer" @click="showPwd()">
                              <font-awesome-icon :icon="data.passwordType === 'password' ? ['far', 'eye-slash'] : ['far', 'eye']"></font-awesome-icon>
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row no-gutters py-2">
                    <div class="col-12 col-lg-4 pr-3">
                      &nbsp;
                    </div>
                    <div class="col-12 col-lg-8 d-flex flex-row justify-content-between align-items-center">
                      <div
                        v-tooltip.right="
                          (!data.passwordUpdateForm.current_password || !data.passwordUpdateForm.password) && {
                            content: 'Missing fields',
                            offset: 10,
                          }
                        "
                      >
                        <button
                          type="submit"
                          class="btn cs-btn"
                          :class="updatablePassword"
                          :disabled="data.pwUpdateButtonDisabled"
                          v-tooltip.right="{
                            classes: data.tooltips.updatePassword.type,
                            content: data.tooltips.updatePassword.content,
                            trigger: 'manual',
                            offset: 70,
                            show: data.tooltips.updatePassword.visible,
                          }"
                        >
                          Update password
                        </button>
                      </div>
                      <div
                        class="cs-warning p-2 rounded mx-4 position-absolute"
                        style="right:0; top:0; max-width:15em;"
                        v-show="data.tooltips.minimumRequirements.visible"
                      >
                        Minimum password requirements not met.&nbsp;<span
                          v-tooltip.bottom-end="{
                            classes: data.tooltips.minimumRequirements.type,
                            content: data.tooltips.minimumRequirements.content,
                            offset: 14,
                          }"
                        >
                          <font-awesome-icon icon="info-circle" class="text-primary"></font-awesome-icon>
                        </span>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable camelcase */
/* eslint-disable import/no-unresolved */
import { mapGetters } from 'vuex';
import { required, email } from 'vuelidate/lib/validators';
// import { debuggerStatement, isUpdateExpression } from '@babel/types';
import { hideTooltip, showTooltip } from '@/utils/tooltip-alerts';

export default {
  name: 'UserProfile',
  data() {
    return {
      data: {
        warningTimeout: null,
        picture: '',
        tooltips: {
          hasMinimumReqmts: true,
          minimumRequirements: {
            visible: false,
            timeout: null,
            type: '',
            invisibleInSeconds: 0, // default is 0, will not clear the tooltip
            content: '',
          },
          updateDetails: {
            visible: false,
            timeout: null,
            type: '',
            invisibleInSeconds: 0, // default is 0, will not clear the tooltip
            content: '',
          },
          updatePassword: {
            visible: false,
            timeout: null,
            type: '',
            invisibleInSeconds: 0, // default is 0, will not clear the tooltip
            content: '',
          },
          updatePicture: {
            visible: false,
            timeout: null,
            type: '',
            classes: 'with-arrow',
            invisibleInSeconds: 0, // default is 0, will not clear the tooltip
            content: '',
          },
        },
        classes: {
          firstname: '',
          lastname: '',
          email: '',
          mobile_code: '',
          mobile_number: '',
          position: '',
          current_password: '',
          password: '',
          borderWarning: '',
        },
        detailsUpdateButtonDisabled: true,
        pwUpdateButtonDisabled: true,
        updatablePassword: false,
        canUpdate: false,
        infoUpdateForm: {
          firstname: '',
          lastname: '',
          email: '',
          mobile_code: '',
          mobile_number: '',
          position: '',
        },
        invalidFields: [],
        pwMinLength: 8,
        currentPasswordType: 'password',
        passwordType: 'password',
        passwordUpdateForm: {
          current_password: '',
          password: '',
        },
        account_type: {
          system_admin: false,
          customer_care: false,
          menu_designer: false,
          service_developer: false,
        },
        prefix: {
          failure: 'Oops! ',
          warning: 'Uh ...',
          success: 'Done. ',
        },
        show: {
          successPrefix: true,
          failurePrefix: true,
          warningPrefix: true,
          // used for clearing timeouts in order to prevent msg-over-msg issues before timers run out
        },
        failure: '',
        success: '',
        warning: '',
      },
    };
  },
  validations: {
    data: {
      email: {
        required,
        email,
      },
      firstname: {},
      lastname: {},
      account_type: {
        system_admin: {},
        customer_care: {},
        menu_designer: {},
        service_developer: {},
      },
    },
  },
  computed: {
    ...mapGetters(['user']),
    hasprofileDetailsFormChanged() {
      let changed = false;
      let { firstname, lastname, position, mobile_code, mobile_number } = this.$store.state.user.user;

      firstname = firstname || '';
      lastname = lastname || '';
      position = position || '';
      mobile_code = mobile_code || '';
      mobile_number = mobile_number || '';

      if (
        this.data.infoUpdateForm.firstname !== firstname ||
        this.data.infoUpdateForm.lastname !== lastname ||
        this.data.infoUpdateForm.position !== position ||
        this.data.infoUpdateForm.mobile_code !== mobile_code ||
        this.data.infoUpdateForm.mobile_number !== mobile_number
      ) {
        changed = true;
      }
      return changed;
    },

    hasUpdated() {
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.data.detailsUpdateButtonDisabled = !this.data.canUpdate;
      return this.data.canUpdate && this.hasprofileDetailsFormChanged ? 'cs-btn-submit' : 'cs-btn-inactive';
    },
    updatablePassword() {
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.data.pwUpdateButtonDisabled = !this.data.updatablePassword;
      return this.data.updatablePassword ? 'cs-btn-submit' : 'cs-btn-inactive';
    },
  },
  created() {
    const { user } = this.$store.state.user;

    this.data.infoUpdateForm.firstname = user.firstname ? user.firstname : '';
    this.data.infoUpdateForm.lastname = user.lastname ? user.lastname : '';
    this.data.infoUpdateForm.email = user.email ? user.email : '';
    this.data.infoUpdateForm.position = user.position ? user.position : '';
    this.data.infoUpdateForm.mobile_code = user.mobile_code ? user.mobile_code : '';
    this.data.infoUpdateForm.mobile_number = user.mobile_number ? user.mobile_number : '';
  },
  methods: {
    onUpdatePicture() {
      const user = {
        // ---- only submit Email, because ID (if submitted) will be used as precedence over email
        email: this.$store.state.user.user.email,
        picture: this.data.picture,
      };
      console.log('user picture to update', user);
      this.$store
        .dispatch('user/updateUser', user)
        .then(response => {
          console.log('response', response);
        })
        .catch(err => {
          console.log('err', err);
        });
    },
    onFileSelect(events) {
      // debugger;
      const img = events.target.files[0];

      console.log(img);
      if (!img.type.match(/^image\/(png|jpe?g)$/)) {
        showTooltip(this.data.tooltips.updatePicture, 'cs-warning', 'Only PNG and JPG are supported :/ Please choose the correct file type', 5);
        return;
      }

      if (isNaN(parseInt(img.size, 10)) || img.size > 256 * 1024) {
        showTooltip(this.data.tooltips.updatePicture, 'cs-warning', 'Maximum file size is 256KB', 5);
        return;
      }
      const reader = new FileReader();
      reader.addEventListener('load', () => {
        this.data.picture = reader.result;
        this.onUpdatePicture();
        hideTooltip(this.data.tooltips.updatePicture);
      });

      reader.readAsDataURL(img);
    },
    showCurrentPwd() {
      this.data.currentPasswordType = this.data.currentPasswordType === 'password' ? 'text' : 'password';
    },
    showPwd() {
      this.data.passwordType = this.data.passwordType === 'password' ? 'text' : 'password';
    },
    setUpdatablePW() {
      hideTooltip(this.data.tooltips.updatePassword);

      this.data.classes.current_password = '';
      hideTooltip(this.data.tooltips.minimumRequirements);
      this.data.tooltips.hasMinimumReqmts = true;

      const { current_password, password } = this.data.passwordUpdateForm;
      if (
        current_password !== password &&
        current_password.length >= this.data.pwMinLength &&
        password.length >= this.data.pwMinLength &&
        password.match(/[a-z]/g) &&
        password.match(/[A-Z]/g) &&
        password.match(/[0-9]/g) &&
        password.match(/[^a-zA-Z\d]/g)
      ) {
        hideTooltip(this.data.tooltips.updatePassword);
        this.data.updatablePassword = true;
        this.data.pwUpdateButtonDisabled = false;
      } else {
        this.data.updatablePassword = false;
        this.data.pwUpdateButtonDisabled = true;

        this.data.tooltips.updatePassword.timeout = setTimeout(() => {
          if (current_password === password) showTooltip(this.data.tooltips.updatePassword, 'cs-warning', 'Cannot set the same password?');
          else {
            this.data.tooltips.hasMinimumReqmts = false;
            showTooltip(
              this.data.tooltips.minimumRequirements,
              'cs-info',
              `${this.data.pwMinLength} or more characters, 1x number, 1x uppercase, 1x lowercase and 1x special`,
            );
          }
        }, 1000);
      }

      // ---- IF...
      //  Both empty -- then no hint
      //  pw's differ and not below min-lengths -- then no hint
      if (password.length <= 0 || current_password.length <= 0) {
        hideTooltip(this.data.tooltips.updatePassword);
      }
    },
    // ---- Only for inputs of the "Details" form, NOT for password fields
    setUpdatable() {
      this.data.canUpdate = false;
      for (const key in this.data.infoUpdateForm) {
        // ---- Only checking that they are not the same (i.e. differs from existing data)
        if (this.data.infoUpdateForm[key] !== this.$store.state.user.user[key]) {
          this.data.canUpdate = true;
          break;
        }
      }
      // ---- Here we ensure that if invalid fields exist, then we can't update
      if (Array.isArray(this.data.invalidFields) && this.data.invalidFields.length !== 0) this.data.canUpdate = false;
    },
    // ---- Only for inputs of the "Details" form, NOT for password fields
    borderControl(eventOrFieldName) {
      const fieldName = typeof eventOrFieldName === 'string' ? eventOrFieldName : eventOrFieldName.target.name;
      const isUpdatable = this.$store.state.user.user[fieldName] !== this.data.infoUpdateForm[fieldName];

      let returnClass = isUpdatable ? 'cs-field-active-border' : '';

      hideTooltip(this.data.tooltips.updateDetails);

      const isEmpty = field => {
        return field.length === 0;
      };

      const setResponse = (msg, invalidFieldName) => {
        returnClass = 'border-danger';
        warningMsg = msg || 'Cannot have empty fields';
        this.data.invalidFields.push(invalidFieldName);
      };

      let warningMsg = '';

      const field = this.data.infoUpdateForm[fieldName] || '';

      switch (fieldName) {
        case 'mobile_number':
          if (isEmpty(field)) {
            setResponse('', fieldName);
          } else if (field.length < 6 || field.length > 15) {
            setResponse('Mobile number should be from 6 to 15 characters', fieldName);
          } else if (!field.match(/^([0-9]{1,}[-\s]{0,1})+[0-9]+$/)) {
            setResponse(
              'Invalid mobile number.<br /><span class="bold">Only Allowed:</span> numbers, spaces, single dashes between numbers',
              fieldName,
            );
          } else {
            this.data.invalidFields = this.data.invalidFields.filter(value => {
              return value !== fieldName;
            });
          }
          break;
        case 'mobile_code':
          if (isEmpty(field)) {
            setResponse('', fieldName);
          } else if (!field.match(/^\+?[0-9]{2,5}$/)) {
            setResponse(
              'Invalid mobile code.<br /><span class="bold">Only Allowed:</span> (optional) <strong>+</strong>, a few numbers (2-5)',
              fieldName,
            );
          } else {
            this.data.invalidFields = this.data.invalidFields.filter(value => {
              return value !== fieldName;
            });
          }
          break;
        default:
          if (field.length === 0) {
            setResponse('', fieldName);
          } else {
            this.data.invalidFields = this.data.invalidFields.filter(value => {
              return value !== fieldName;
            });
          }
          break;
      }

      this.setUpdatable();

      if (this.warningTimeout !== null) {
        clearTimeout(this.warningTimeout);
      }

      if (warningMsg !== '') {
        this.warningTimeout = setTimeout(() => {
          showTooltip(this.data.tooltips.updateDetails, 'cs-warning', warningMsg);
        }, 800);
      }

      this.data.classes[fieldName] = returnClass;
    },
    onUpdatePassword() {
      if (this.data.updatablePassword) {
        const { current_password, password } = this.data.passwordUpdateForm;
        const toSubmit = {
          // ---- only submit Email, because ID (if submitted) will be used as precedence over email
          email: this.$store.state.user.user.email,
          current_password,
          password,
        };

        this.$store
          .dispatch('user/updateUser', toSubmit)
          .then(() => {
            this.data.passwordUpdateForm.current_password = '';
            this.data.passwordUpdateForm.password = '';
            this.data.pwUpdateButtonDisabled = true;
            this.data.updatablePassword = false;
            showTooltip(this.data.tooltips.updatePassword, 'cs-success', 'Password update successful!', 6);
          })
          .catch(err => {
            // ---- Hiding the tooltip also clears timeouts - just in case
            hideTooltip(this.data.tooltips.updatePassword);

            let errMsg = err.message || err;

            if (err.message && err.message.match(/invalid password/i)) {
              // ---- Focus on the current password, that's the culprit for this error
              this.data.classes.current_password = 'border border-danger';
              this.data.updatablePassword = false;
              errMsg = 'I could not validate your password';
            }

            showTooltip(this.data.tooltips.updatePassword, 'cs-error', errMsg);
          });
      }
    },
    onUpdateDetails() {
      if (this.data.canUpdate) {
        const toSubmit = {
          // ---- only submit Email, because ID (if submitted) will be used as precedence over email
          email: this.$store.state.user.user.email,
        };
        for (const key in this.data.infoUpdateForm) {
          if (this.data.infoUpdateForm[key] !== this.$store.state.user.user[key]) toSubmit[key] = this.data.infoUpdateForm[key];
        }

        this.$store
          .dispatch('user/updateUser', toSubmit)
          .then(() => {
            for (const key in this.data.infoUpdateForm) {
              if (toSubmit[key]) {
                this.data.infoUpdateForm[key] = toSubmit[key];
                this.borderControl(key);
              }
            }
            showTooltip(this.data.tooltips.updateDetails, 'cs-success', 'Update Successful!', 5);
          })
          .catch(err => {
            showTooltip(this.data.tooltips.updateDetails, 'cs-error', err.message || 'Request Error');
          });
      }
    },
    onCancel() {
      this.$message({
        message: 'cancel!',
        type: 'warning',
      });
    },
  },
};
</script>

<style lang="scss">
body * {
  transition-property: opacity;
  transition-duration: 3s;
}
.form-control:focus {
  border-color: #ced4da;
  box-shadow: none;
}
</style>

<style lang="scss" scoped>
@import '../../styles/cs-styles.scss';
.cs-warning {
  background-color: $cs_color_warning_bg;
  color: $cs_color_warning;
  border: 1px solid rgba(50, 50, 50, 0.1);
}
.bold {
  font-weight: bold;
}
.picture-upload-btn {
  position: relative;
  .upload-icon-overlay {
    transition: all;
    transition-duration: 0.4s;
    display: none;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
  }
  &:hover {
    opacity: 0.8;
    .upload-icon-overlay {
      opacity: 0.5;
      display: flex;
    }
  }
}

.profile-border {
  border-bottom: 3px solid grey;
  @media only screen and (min-width: 1200px) {
    border-right: 3px solid grey;
    border-bottom: 0;
  }
}

.user-profile-page {
  color: inherit;
  padding: 25px 25px 25px 5px;
}
.picture-initials {
  display: flex;
  height: 90px;
  width: 90px;
}
.picture {
  border-radius: 50%;
  height: 90px;
  width: 90px;
}
.admin-info {
  margin-top: 24px;
  padding-left: 6px;
}
.user-info-top-margin {
  margin-top: 10px;
}

.text-bold {
  font-size: 32px;
  font-weight: bold;
  color: #000000;
}

.text-email {
  font-size: 14px;
  color: #848080;
}
.user-profile-info {
  overflow-wrap: break-word;
  word-wrap: break-word;
  hyphens: auto;
}
</style>
