<template>
  <div class="container-fluid">
    <div class="component-title row">
      <div class="col-sm-12">
        <div class="component-text">
          <i class="fas fa-laptop-code" />
          Provide Component Details
        </div>
      </div>
    </div>

    <div class="component-form row">
      <div class="col-sm-12">
        <form class="form-inline" @submit.prevent="onSubmit">
          <div class="col-sm-2">
            <label class="float-right mb-4" for="name">Component Name</label>
          </div>
          <div class="col-sm-10">
            <input
              type="text"
              class="form-control mb-0"
              id="name"
              v-model.trim="component.displayName"
              placeholder="Name"
              v-tooltip.right-end="{
                classes: tooltips.componentName.type,
                content: tooltips.componentName.content,
                trigger: 'manual',
                offset: 5,
                show: tooltips.componentName.visible,
              }"
            />
            <div class="note ml-1 mb-3" :class="component.displayName !== '' && !$v.component.displayName.startsWithAlpha ? 'text-danger' : ''">
              <strong>Note:</strong> can only start with <strong>"letters"</strong> (a-z or A-Z)
            </div>
          </div>

          <div class="col-sm-2">
            <label class="float-right" for="description">Description</label>
          </div>
          <div class="col-sm-10">
            <input type="text" class="form-control description" id="description" v-model.trim="component.description" placeholder="Description" />
          </div>

          <div class="col-sm-2"></div>
          <div class="col-sm-10 mt-2">
            <span
              v-tooltip.right="
                (pm &&
                  !pm.hasPermission({ can: 'create', a: 'component' }) && {
                    content: 'You do not have permission to create components',
                    offset: 10,
                  }) ||
                  ((!$v.component.displayName.required || !$v.component.description.required) && { content: 'Missing Fields', offset: 10 })
              "
            >
              <button
                type="submit"
                :disabled="(pm && !pm.hasPermission({ can: 'create', a: 'component' })) || $v.component.$invalid || submitBusy"
                class="btn cs-btn"
                :class="pm && pm.hasPermission({ can: 'create', a: 'component' }) && !$v.component.$invalid ? 'cs-btn-submit' : 'cs-btn-inactive'"
              >
                Submit
              </button>
            </span>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved */
import PermissionManager from '@/utils/PermissionManager';
import '@/utils/common';
import { hideTooltip, showTooltip } from '@/utils/tooltip-alerts';
import { mapGetters } from 'vuex';
import { required } from 'vuelidate/lib/validators';

// const touchMap = new WeakMap();

export default {
  name: 'Newcomponent',
  data() {
    return {
      submitError: '',
      tooltips: {
        submitButton: {
          classes: '',
          visible: false,
          timeout: null,
          type: '',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
        componentName: {
          classes: 'component-name-warning',
          visible: false,
          timeout: null,
          type: '',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
      },
      component: {
        displayName: '',
        name: '',
        description: '',
      },
      valid: false,
      submitBusy: false,
    };
  },
  validations: {
    component: {
      displayName: {
        required,
        startsWithAlpha: function startsWithAlpha(name) {
          return !!name.match(/^[a-zA-Z]/);
        },
        validIfNotExists: function validIfNotExists(name) {
          const existsIndex = this.components.findIndex(component => {
            return name.toK8Name() === component.name.toK8Name();
          });

          return existsIndex === -1;
        },
      },
      description: {
        required,
      },
    },
  },
  computed: {
    ...mapGetters(['components', 'userPermissions']),
    pm() {
      if (!this.userPermissions) return undefined;

      const pm = new PermissionManager(this.userPermissions);

      return pm;
    },
  },
  watch: {
    component: {
      deep: true,
      handler(newValue) {
        this.component.name = newValue.displayName.normalizeName();
      },
    },
    $v: {
      deep: true,
      handler() {
        hideTooltip(this.tooltips.componentName);
        const startsWithAlpha = !this.$v.component.displayName.startsWithAlpha;
        const componentAlreadyExists = !this.$v.component.displayName.validIfNotExists;
        if (!startsWithAlpha && componentAlreadyExists) {
          this.tooltips.componentName.timeout = setTimeout(() => {
            showTooltip(
              this.tooltips.componentName,
              'cs-warning',
              "<strong>Sorry</strong>, the name you've chosen is the same or too similar to another component",
            );
          }, 700);
        }
      },
    },
  },
  mounted() {},
  methods: {
    onSubmit() {
      this.submitBusy = true;
      this.$store
        .dispatch('component/create', this.component)
        .then(resp => {
          console.log(JSON.stringify(resp));
          this.$router.push({ path: `/component/comp-editor/${resp.id}` });
        })
        .catch(err => {
          if (console) console.log(err.message);
          self.submitError = err.message;
          if (err.message === 'Error: Request failed with status code 403') {
            showTooltip(this.tooltips.componentName, 'cs-warning', 'Sorry, you do not have permission to perform this action', 5);
          }
        });
    },
  },
};
</script>

<style lang="scss">
.component-name-warning .tooltip-inner {
  min-width: 18em;
}
</style>
<style lang="scss" scoped>
.component {
  &-title,
  &-text {
    margin: 30px;
    font-size: 1.2rem;
    color: rgb(26, 197, 3);
  }
  &-text i {
    font-size: 2rem;
    color: black;
    margin: 0 20px 0 70px;
  }
  &-form {
    margin: 0 0 0 0;
  }
}

.component-form .permission {
  width: 200px;
}

.component-form .description {
  width: 600px;
}

.form-inline select,
.form-inline input {
  margin: 0 0 8px 0;
}
.note {
  font-size: 12px;
  color: #666666;
}
</style>
