<template>
  <div>
    <b-modal
      class="modal fade"
      :title="`Please resolve following conflicts before proceeding`"
      size="lg"
      id="errorModal"
      ref="errorModal"
      centered
      ok-only
      ok-variant="outline-success"
      ok-title="Resolve Conflicts"
      @ok="resolveConflicts"
      @shown="onModalShow"
      @hide="onModalHide"
      scrollable
    >
      <!-- Main Module -->
      <div v-if="Object.keys(conflicts.rootModule).length">
        <b-alert show variant="danger">Main Module</b-alert>
        <div v-for="(conflict, index) in conflicts.rootModule" :key="index">
          <Dropdown @onChange="onDropdownValueChange" type="modules" :options="options" :conflict="conflict" />
        </div>
      </div>

      <!-- Sub-Modules -->
      <div v-if="Object.keys(conflicts.subModules).length">
        <b-alert show variant="danger">Sub Modules</b-alert>
        <div v-for="(conflict, index) in conflicts.subModules" :key="index">
          <Dropdown @onChange="onDropdownValueChange" type="modules" :options="options" :conflict="conflict" />
        </div>
      </div>

      <!-- components -->
      <div v-if="Object.keys(conflicts.components).length">
        <b-alert show variant="danger">Components</b-alert>

        <div v-for="(conflict, index) in conflicts.components" :key="index">
          <Dropdown @onChange="onDropdownValueChange" type="components" :options="options" :conflict="conflict" />
        </div>
      </div>

      <!-- connectors -->
      <div v-if="Object.keys(conflicts.connectors).length">
        <b-alert show variant="danger">Connector Endpoints</b-alert>

        <div v-for="(conflict, index) in conflicts.connectors" :key="index">
          <Dropdown @onChange="onDropdownValueChange" type="connectors" :options="options" :conflict="conflict" />
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import Dropdown from './dropdown.vue';

export default {
  name: 'ConflictsResolutionModal',
  components: {
    Dropdown,
  },
  props: {
    conflicts: {
      type: Object,
      required: true,
      default() {
        return {
          components: {},
          connectors: {},
          rootModule: {},
          subModules: {},
        };
      },
    },
  },
  data() {
    return {
      options: [
        // { value: null, text: 'Please select some item' },
        { value: 'update', text: 'Update (replace with incoming data)' },
        { value: 'keep-existing-data', text: 'Do not update (keep existing data)' },
      ],

      doNotUpdate: {
        modules: [],
        components: [],
        connectors: [],
      },
    };
  },
  computed: {
    ...mapGetters(['modules', 'components', 'connectorEndpoints']),
  },
  methods: {
    showModal() {
      this.$refs.errorModal.show();
      // this.exportModule();
    },
    onDropdownValueChange({ update, name, type }) {
      if (!update) {
        if (type === 'modules') {
          this.doNotUpdate.modules.push(name);
        } else if (type === 'components') {
          this.doNotUpdate.components.push(name);
        } else if (type === 'connectors') {
          const arr = name.split(':');
          this.doNotUpdate.connectors.push({ endpoint: arr[1], connector: arr[0] });
        }
      }
    },
    resolveConflicts() {
      this.$emit('conflictsResolved', this.doNotUpdate);
    },
    onModalShow() {},
    onModalHide() {},
  },
};
</script>
<style lang="scss" scoped>
.delete-modal {
  background-color: #fffaf3;
  color: #573a08;
  border-radius: 5px;
  padding: 1em 1.5em;
  border: 1px solid #ece1ce;
}
.delete-modal-font {
  font-size: 0.9em;
}
</style>
