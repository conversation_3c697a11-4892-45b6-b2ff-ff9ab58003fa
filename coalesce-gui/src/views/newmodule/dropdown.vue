<template>
  <div>
    <div class="container mb-3 row">
      <div class="col-sm-4 mt-1">
        <strong>{{ conflict.name }} </strong>

        <span v-if="conflict.connector"> ({{ conflict.connector }})</span>
      </div>
      <div class="col-sm-6">
        <b-form-select @change="onChange" v-model="selected" :options="options" class="mb-3"></b-form-select>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Dropdown',
  props: {
    conflict: {
      type: Object,
      required: true,
      default() {
        return {
          name: '',
          reason: '',
          message: '',
        };
      },
    },
    type: {
      type: String,
      required: true,
      default: '',
    },
    options: {
      type: Array,
      required: true,
      default() {
        return [
          {
            value: '',
            text: '',
          },
        ];
      },
    },
  },
  data() {
    return {
      selected: 'update',
    };
  },

  methods: {
    onChange(value) {
      const res = {
        type: this.type,
        name: this.conflict.connector ? `${this.conflict.connector}:${this.conflict.name}` : this.conflict.name,
        update: value === 'update',
      };
      this.$emit('onChange', res);
    },
  },
};
</script>
<style lang="scss" scoped>
.delete-modal {
  background-color: #fffaf3;
  color: #573a08;
  border-radius: 5px;
  padding: 1em 1.5em;
  border: 1px solid #573a08;
}
.delete-modal-font {
  font-size: 0.9em;
}
</style>
