<template>
  <div class="container-fluid">
    <div class="module-title row">
      <div class="col-sm-12">
        <div class="module-text">
          <i class="fas fa-sitemap" />
          Create a new Module
        </div>
      </div>
    </div>

    <div class="module-form row">
      <div class="col-sm-12">
        <form class="form-inline" @submit="onSubmit">
          <div class="col-sm-2">
            <label class="float-right mb-4" for="modulename">Module Name</label>
          </div>
          <div class="col-sm-10">
            <input
              @input="isValid()"
              type="text"
              class="form-control mb-0"
              id="modulename"
              v-model.trim="module.displayName"
              placeholder="Module Name"
              v-tooltip.right-end="{
                classes: tooltips.moduleName.type,
                content: tooltips.moduleName.content,
                trigger: 'manual',
                offset: 5,
                show: tooltips.moduleName.visible,
              }"
            />
            <div class="note ml-1 mb-3"><strong>Note:</strong> numbers are not allowed as a <strong>"prefix"</strong></div>
          </div>

          <div class="col-sm-2">
            <label class="float-right" for="description">Description</label>
          </div>
          <div class="col-sm-10">
            <input
              type="text"
              class="form-control description"
              id="description"
              @input="isValid()"
              v-model.trim="module.description"
              placeholder="Description"
            />
          </div>
          <div class="col-sm-2"></div>
          <div class="col-sm-10 mt-4">
            <span
              v-tooltip.right="
                (pm && !pm.hasPermission({ can: 'create', a: 'menu' }) && { content: 'You do not have permission to create modules', offset: 10 }) ||
                  (!valid && !tooltips.moduleName.visible && { content: 'Missing Fields', offset: 10 })
              "
            >
              <button
                type="submit"
                :disabled="(pm && !pm.hasPermission({ can: 'create', a: 'menu' })) || !valid || isDisabled"
                class="submit-button btn cs-btn"
                :class="pm && pm.hasPermission({ can: 'create', a: 'menu' }) && valid ? 'cs-btn-submit' : 'cs-btn-inactive'"
              >
                Submit
              </button>
            </span>
          </div>
        </form>
      </div>
    </div>
    <!-- <hr class="mt-4" />

    <div class="module-title row">
      <div class="col-sm-12">
        <div class="module-text">
          <i class="fas fa-sitemap" />
          Import a Module
        </div>
      </div>
    </div>

    <div class="row ">
      <div class="col-sm-2">
        <label class="float-right mt-1" for="file">Upload a file</label>
      </div>
      <div class="ml-3 col-sm-6">
        <b-form-file
          @input="uploadFile($event)"
          browse-text="Upload Module"
          ref="fileImport"
          accept="application/JSON"
          id="file"
          size="md"
          :disabled="pm && !pm.hasPermission({ can: 'create', a: 'menu' })"
        ></b-form-file>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-2"></div>
      <div class="col-sm-10 mt-4 mb-5">
        <span
          v-tooltip.right="
            (pm && !pm.hasPermission({ can: 'create', a: 'menu' }) && { content: 'You do not have permission to import modules', offset: 10 }) ||
              (!fileUploadedAndIsvalid && { content: 'Please upload valid module', offset: 10 })
          "
        >
          <button
            placeholder="Module Import"
            v-tooltip.right-end="{
              classes: tooltips.moduleImport.type,
              content: tooltips.moduleImport.content,
              trigger: 'manual',
              offset: 20,
              show: tooltips.moduleImport.visible,
            }"
            type="submit"
            :disabled="(pm && !pm.hasPermission({ can: 'create', a: 'menu' })) || !fileUploadedAndIsvalid"
            class="submit-button btn cs-btn ml-3"
            :class="pm && pm.hasPermission({ can: 'create', a: 'menu' }) && fileUploadedAndIsvalid ? 'cs-btn-submit' : 'cs-btn-inactive'"
            @click="showImportModuleModal"
          >
            Import Module
          </button>
        </span>
      </div>
    </div>

    <ImportModuleModal @reset="resetImportModule" :uploaded-module-data="uploadedModule" ref="importModuleModal" /> -->
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved */
import PermissionManager from '@/utils/PermissionManager';
import '@/utils/common';
import { mapGetters } from 'vuex';
import { hideTooltip, showTooltip } from '@/utils/tooltip-alerts';
// import ImportModuleModal from './importModuleModal.vue';

export default {
  name: 'Newmodule',
  components: {
    // ImportModuleModal,
  },
  data() {
    return {
      uploadedModule: {
        components: {},
        connectors: {},
        rootModule: {},
        subModules: {},
      },
      uploadedModuleValid: false,
      submitError: '',
      module: {
        displayName: '',
        name: '',
        description: '',
        shortcode: '*100#',
        permission: 'private',
      },
      tooltips: {
        moduleName: {
          firstTime: true,
          classes: 'module-name-warning',
          visible: false,
          timeout: null,
          type: '',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
        moduleImport: {
          firstTime: true,
          classes: 'module-import-warning',
          visible: false,
          timeout: null,
          type: '',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
      },
      valid: false,
      isDisabled: false,
    };
  },
  computed: {
    ...mapGetters(['modules', 'userPermissions']),
    pm() {
      if (!this.userPermissions) return undefined;

      const pm = new PermissionManager(this.userPermissions);
      return pm;
    },
    fileUploadedAndIsvalid() {
      if (this.uploadedModuleValid) return true;

      return false;
    },
  },
  mounted() {
    // debugger;
    this.init();
  },
  methods: {
    isValid() {
      hideTooltip(this.tooltips.moduleName);
      this.module.name = this.module.displayName.normalizeName();

      if (this.tooltips.moduleName.firstTime || this.module.name === '') {
        this.valid = false;
        this.tooltips.moduleName.firstTime = false;
        return;
      }

      let valid = true;
      const moduleList = this.modules.modules;
      if (moduleList.length > 0) {
        for (const i in moduleList) {
          // ---- using 'toK8Name' allows us to compare them both in their lower case k8s friendly format
          if (this.module.name.toK8Name() === moduleList[i].name.toK8Name()) {
            // let moduleType = moduleList[i].type.replace(/^\w/, c => c.toUpperCase()); // capitalize first letter
            // moduleType = moduleType === 'Menu' ? 'module' : moduleType;

            valid = false;
            this.tooltips.moduleName.timeout = setTimeout(() => {
              showTooltip(
                this.tooltips.moduleName,
                'cs-warning',
                "<strong>Sorry</strong>, the name you've chosen is the same or too similar to another module",
              );
            }, 600);
            break;
          }
        }
      }

      this.valid = valid;

      switch (this.valid) {
        case this.module.name.length <= 0:
          console.log('name invalid :/');
          this.valid = false;
          break;
        case typeof this.module.description !== 'string' || this.module.description.length <= 0:
          console.log('desc invalid :/');
          this.valid = false;
          break;
        default:
          break;
      }
    },
    onSubmit(event) {
      event.preventDefault();
      // alert(JSON.stringify(this.module));
      this.isDisabled = true;
      this.$store
        .dispatch('modules/create', this.module)
        .then(resp => {
          console.log(JSON.stringify(resp));
          /*
          {
            "ok":true,
            "code":20000,
            "data":{
              "name":"Test One",
              "description":"Simple test module",
              "shortcode":"*100#",
              "permission":"private",
              "id":1
            }
          }
          */
          /* this.$router.push({ path: this.redirect || "/" }); */
          // debugger;
          this.$store.dispatch('app/setSidebarLocation', [{ title: this.module.name }]);
          this.$router.push({ path: `/menu/menu-editor/${resp.id}` });
          // this.$router.push({ name: "menueditor" });
          // /menu/menu-editor/:moduleId
        })
        .catch(err => {
          if (console) console.error(err.message);
          self.submitError = err.message;
          if (err.message === 'Error: Request failed with status code 403') {
            showTooltip(this.tooltips.moduleName, 'cs-warning', 'Sorry, you do not have permission to perform this action', 5);
          } else {
            showTooltip(this.tooltips.moduleName, 'cs-warning', err.message || 'Unknown server error', 5);
          }
          this.isDisabled = false;
        });
    },
    init() {
      // debugger;
    },

    async readFileAsText(file) {
      const res = await new Promise(resolve => {
        const fileReader = new FileReader();
        fileReader.onload = () => resolve(fileReader.result);
        fileReader.readAsText(file);
      });
      return res;
    },

    async uploadFile(file) {
      if (!file) {
        this.uploadedModuleValid = false;
        this.uploadedModule = {
          components: {},
          connectors: {},
          rootModule: {},
          subModules: {},
        };
        return;
      }
      hideTooltip(this.tooltips.moduleImport);
      // console.log('e', e);
      // const file = e.target.file[0];
      const fileData = await this.readFileAsText(file);
      let data;

      try {
        data = JSON.parse(fileData);

        console.log('uploaded file data', data);

        const { components, connectors, rootModule, subModules } = data;
        if (components && connectors && rootModule && subModules) {
          this.uploadedModuleValid = true;
          this.uploadedModule = data;
        } else throw new Error('Invalid Module Uploaded');
      } catch (e) {
        this.uploadedModuleValid = false;
        showTooltip(this.tooltips.moduleImport, 'cs-warning', 'Invalid Module Uploaded', 5);
      }
    },
    showImportModuleModal() {
      this.$refs.importModuleModal.showModal();
    },

    resetImportModule(data) {
      this.$refs.fileImport.reset();
      this.uploadedModuleValid = false;
      this.uploadedModule = {
        components: {},
        connectors: {},
        rootModule: {},
        subModules: {},
      };
      if (data.ok) {
        showTooltip(this.tooltips.moduleImport, 'cs-success', ' Module imported successfully', 5);
      } else {
        showTooltip(this.tooltips.moduleImport, 'cs-warning', 'Something went wrong, please contanct CS-Support', 5);
      }
    },
  },
};
</script>

<style lang="scss">
.module-name-warning .tooltip-inner {
  min-width: 18em;
}
</style>
<style lang="scss" scoped>
.module {
  &-title,
  &-text {
    margin: 20px;
    font-size: 1.2rem;
    color: rgb(26, 197, 3);
  }
  &-text i {
    font-size: 2rem;
    color: black;
    margin: 0 20px 0 70px;
  }
  &-form {
    margin: 0 0 0 0;
  }
}

.module-form .permission {
  width: 200px;
}

.module-form .description {
  width: 600px;
}

.form-inline select,
.form-inline input {
  margin: 0 0 8px 0;
}

.module-form .submit-button {
  //   margin: 50px 0 0 0;
  width: 120px;
  font-size: 1.3rem;
  padding: 2px 0;
}
.note {
  font-size: 12px;
  color: #666666;
}

.import-btn {
  font-size: 1.15rem;
  margin-left: 14px;
}
</style>
