<template>
  <div>
    <b-modal
      class="modal fade"
      :title="`Import Module: (${uploadedModuleData.rootModule.name})`"
      size="lg"
      id="importModal"
      ref="importModal"
      centered
      ok-variant="outline-success"
      ok-title="Import Module"
      @shown="onModalShow"
      @hide="onModalHide"
      hide-footer
    >
      <div
        v-if="Object.keys(uploadedModuleData.subModules).length || Object.keys(uploadedModuleData.components).length"
        class="delete-modal px-4 delete-modal-font"
      >
        <div class="mb-2">
          The module <strong>{{ uploadedModuleData.rootModule.name }} </strong> has following dependencies:
        </div>
        <div v-if="Object.keys(uploadedModuleData.subModules).length">
          <strong>Sub Modules:</strong> {{ Object.keys(uploadedModuleData.subModules) }}
        </div>
        <div v-if="Object.keys(uploadedModuleData.components).length" class="mt-2">
          <strong>Components:</strong> {{ Object.keys(uploadedModuleData.components) }}
        </div>
      </div>

      <div
        v-if="
          Object.keys(uploadedModuleData.subModules).length ||
            Object.keys(uploadedModuleData.components).length ||
            uploadedModuleData.connectors.length
        "
      >
        <div class="mt-2">
          Please choose the dependencies you want to import alongwith this module
        </div>
        <div class="mt-2">
          <b-form-checkbox
            v-if="Object.keys(uploadedModuleData.subModules).length"
            v-model="checkbox.importSubModules"
            id="importSubModules"
            name="importSubModules"
            :disabled="disableCheckbox"
          >
            Import All sub-modules
          </b-form-checkbox>
          <b-form-checkbox
            v-if="Object.keys(uploadedModuleData.components).length"
            v-model="checkbox.importComponents"
            id="importComponents"
            name="importComponents"
            :disabled="disableCheckbox"
          >
            Import All components
          </b-form-checkbox>
          <b-form-checkbox
            v-if="Object.keys(uploadedModuleData.connectors).length"
            v-model="checkbox.importConnectors"
            id="importConnectors"
            name="importConnectors"
            :disabled="disableCheckbox"
          >
            Import All connectors
          </b-form-checkbox>
        </div>
      </div>

      <div v-else>
        <div class="mb-2">
          The module <strong>{{ uploadedModuleData.rootModule.name }} </strong> has no sub-dependency:
        </div>
      </div>

      <div class="mt-3" v-if="showResolveConflictsButton">
        <b-alert show variant="danger">There are some conflicts, please resolve them first before proceeding</b-alert>
        <b-button style="position:relative; top:35px" @click="showConflictsResolutionModal" variant="outline-danger"> Resolve Conflicts</b-button>
      </div>
      <div class="float-right">
        <b-button :disabled="showResolveConflictsButton" @click="importModule" variant="outline-success">
          Import Module
        </b-button>
      </div>

      <ConflictsResolutionModal @conflictsResolved="resolveConflicts" :conflicts="errors" ref="conflictsResolutionModal" />
    </b-modal>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
// import _ from 'lodash';
import ConflictsResolutionModal from './conflictsResolutionModal.vue';

export default {
  name: 'ImportModuleModal',
  components: {
    ConflictsResolutionModal,
  },
  props: {
    uploadedModuleData: {
      type: Object,
      required: true,
      default() {
        return {
          components: {},
          connectors: {},
          rootModule: {},
          subModules: {},
        };
      },
    },
  },
  data() {
    return {
      conflictsResolved: false,
      moduleToSave: null,
      fileData: {},
      subModulesToBeExported: [],
      componentsToBeExported: [],

      moduleDependencies: {
        subModules: {},
        components: {},
        connectors: {},
      },
      checkbox: {
        disabled: false,
        importSubModules: false,
        importComponents: false,
        importConnectors: false,
      },
      errors: {
        rootModule: [],
        subModules: [],
        components: [],
        connectors: [],
      },
      action: {
        createNew: {
          modules: [],
          components: [],
          connectors: {},
        },
        updateExisting: {
          modules: [],
          components: [],
          connectors: {},
        },
        doNotUpdate: {
          modules: [],
          components: [],
          connectors: [],
        },
      },
    };
  },
  computed: {
    ...mapGetters(['modules', 'components', 'connectorEndpoints']),
    showResolveConflictsButton() {
      if (this.errors.rootModule.length || this.errors.subModules.length || this.errors.components.length || this.errors.connectors.length) {
        return true;
      }

      return false;
    },
    disableCheckbox() {
      if (this.checkbox.disabled) {
        return true;
      }

      return false;
    },
  },
  methods: {
    resolveConflicts(doNotUpdate) {
      this.conflictsResolved = true;
      this.action.doNotUpdate = doNotUpdate;
      this.errors = {
        rootModule: [],
        subModules: [],
        components: [],
        connectors: [],
      };
      console.log('all conflicts resolved, taking action now');
      console.log('this.action', this.action);
    },
    showConflictsResolutionModal() {
      this.$refs.conflictsResolutionModal.showModal();
    },
    // TODO change the name
    showModal() {
      this.$refs.importModal.show();
    },

    // checks
    checkRootModule(rootModule) {
      const { modules } = this.modules;
      let moduleExists = false;
      for (let i = 0; i < modules.length; i++) {
        if (modules[i].name === rootModule.name) {
          this.errors.rootModule.push({
            name: modules[i].name,
            reason: 'exists',
            message: `module with name=${modules[i].name} already exists`,
          });
          this.action.updateExisting.modules.push(rootModule);
          moduleExists = true;
          return;
        }
      }
      if (!moduleExists) this.action.createNew.modules.push(rootModule);
    },
    checksubModules(subModules) {
      const { modules } = this.modules;
      for (const module in subModules) {
        let moduleExists = false;
        for (let i = 0; i < modules.length; i++) {
          if (modules[i].name === subModules[module].name) {
            this.errors.subModules.push({
              name: modules[i].name,
              reason: 'exists',
              message: `module with name=${modules[i].name} already exists`,
            });
            this.action.updateExisting.modules.push(subModules[module]);
            moduleExists = true;
            break;
          }
        }
        if (!moduleExists) this.action.createNew.modules.push(subModules[module]);
      }
    },
    checkComponents(components) {
      const comp = this.components;
      for (const component in components) {
        let componentExists = false;
        for (let i = 0; i < comp.length; i++) {
          if (comp[i].name === components[component].name) {
            this.errors.components.push({
              name: comp[i].name,
              reason: 'exists',
              message: `component with name=${comp[i].name} already exists`,
            });
            this.action.updateExisting.components.push(components[component]);
            componentExists = true;
            break;
          }
        }
        if (!componentExists) this.action.createNew.components.push(components[component]);
      }
    },
    createHashMap(connectors) {
      const hash = {};

      connectors.forEach(e => {
        if (!hash[e.connector_type]) {
          hash[e.connector_type] = e.endpoints;
        } else {
          hash[e.connector_type] = [...hash[e.connector_type], e.endpoints];
        }
      });
      return hash;
    },
    checkConnectors(connectors) {
      const existingConnectors = this.connectorEndpoints;

      const incomingConnectorsHash = this.createHashMap(connectors); //  just to simplify things
      const existingConnectorsHash = this.createHashMap(existingConnectors); //  just to simplify things

      // TODO (remove these logs after testing)
      console.log('incomingConnectorsHash', incomingConnectorsHash);
      console.log('existingConnectorsHash', existingConnectorsHash);

      for (const connectorType in incomingConnectorsHash) {
        console.log('connectorType', connectorType);
        if (existingConnectorsHash[connectorType]) {
          incomingConnectorsHash[connectorType].forEach(newEndpoint => {
            let endpointExists = false;

            existingConnectorsHash[connectorType].forEach(oldEndpoint => {
              if (oldEndpoint.name === newEndpoint.name) {
                endpointExists = true;
                this.errors.connectors.push({
                  connector: connectorType,
                  name: newEndpoint.name,
                  reason: 'exists',
                  message: `endpoint=${newEndpoint.name} of ${connectorType} already exists`,
                });

                const updateExistingConnector = this.action.updateExisting.connectors;
                if (!updateExistingConnector[connectorType]) {
                  updateExistingConnector[connectorType] = [newEndpoint];
                } else {
                  updateExistingConnector[connectorType] = [...updateExistingConnector[connectorType], newEndpoint];
                }
              }
            });

            // if endpoint not found,
            if (!endpointExists) {
              const createNewConnector = this.action.createNew.connectors;
              if (!createNewConnector[connectorType]) {
                createNewConnector[connectorType] = [newEndpoint];
              } else {
                createNewConnector[connectorType] = [...createNewConnector[connectorType], newEndpoint];
              }
            }
          });
        }
      }
      console.log('this.action', this.action);
      console.log('this.errors', this.errors);
    },

    // create/update dependencies
    createModule(module) {
      return new Promise((resolve, reject) => {
        this.$store
          .dispatch('modules/create', module)
          .then(res => {
            console.log(`created module! ${module.name}`);
            return resolve(res);
          })
          .catch(error => {
            console.log(`Unable to createModule ${module}! Error!:`, error);
            this.$refs.importModal.hide();
            return reject(error);
          });
      });
    },
    updateModule(data) {
      return new Promise((resolve, reject) => {
        this.$store
          .dispatch('modules/updatemenucode', data)
          .then(res => {
            console.log(`Updated Module! ${data.moduleName}`);
            return resolve(res);
          })
          .catch(error => {
            console.log(`Unable to update Module ${data.moduleName}! Error!:`, error);
            this.$refs.importModal.hide();
            return reject(error);
          });
      });
    },
    createComponent(component) {
      return new Promise((resolve, reject) => {
        this.$store
          .dispatch('component/create', component)
          .then(res => {
            console.log(`created Component! ${component.name}`);
            return resolve(res);
          })
          .catch(error => {
            console.log(`Unable to createComponent ${component}! Error!:`, error);
            this.$refs.importModal.hide();
            return reject(error);
          });
      });
    },
    updateComponent(data) {
      return new Promise((resolve, reject) => {
        this.$store
          .dispatch('component/setcomponent', data)
          .then(res => {
            console.log(`Updated Component! ${data.componentName}`);
            return resolve(res);
          })
          .catch(error => {
            console.log(`Unable to update component ${data.componentName}! Error!:`, error);
            this.$refs.importModal.hide();
            return reject(error);
          });
      });
    },
    createConnectorEndpoint(endpoint) {
      return new Promise((resolve, reject) => {
        this.$store
          .dispatch('connectorEndpoint/create', endpoint)
          .then(res => {
            console.log(`created connectorEndpoint! ${res.name} of type=${res.type}`);
            return resolve(res);
          })
          .catch(error => {
            console.log(`Unable to create connectorEndpoint ${endpoint.name}! of type=${endpoint.type} Error!:`, error);
            this.$refs.importModal.hide();
            return reject(error);
          });
      });
    },
    updateConnectorEndpoint(endpoint) {
      return new Promise((resolve, reject) => {
        this.$store
          .dispatch('connectorEndpoint/update', endpoint)
          .then(res => {
            console.log(`updated connectorEndpoint! ${res.name} of type=${res.type}`);
            return resolve(res);
          })
          .catch(error => {
            console.log(`Unable to updated connectorEndpoint ${endpoint.name}! of type=${endpoint.type} Error!:`, error);
            this.$refs.importModal.hide();
            return reject(error);
          });
      });
    },

    // TODO (cleanup/refactor this function, very messy :))
    async createUpdateModuleAndItsDependencies() {
      try {
        for (const action in this.action) {
          if (action === 'createNew') {
            if (this.action[action].modules.length) {
              const promises = [];
              this.action[action].modules.forEach(module => {
                const promise = this.createModule(module);
                promises.push(promise);
              });
              // console.log(promises);
              await Promise.all(promises).then(() => console.log('*** created all modules ***'));
            }
            if (this.action[action].components.length) {
              const promises = [];
              this.action[action].components.forEach(comp => {
                const promise = this.createComponent(comp);
                promises.push(promise);
              });
              // console.log(promises);
              await Promise.all(promises).then(() => console.log('*** created all components ***'));
            }
            if (Object.keys(this.action[action].connectors).length) {
              const { connectors } = this.action[action];
              const promises = [];

              for (const type in connectors) {
                for (const endpoint of connectors[type]) {
                  const endpointData = { ...endpoint, type };
                  const promise = this.createConnectorEndpoint(endpointData);
                  promises.push(promise);
                }
              }
              await Promise.all(promises).then(() => console.log('*** created all connectors ***'));
            }
          }
          if (action === 'updateExisting') {
            if (this.action[action].modules.length) {
              try {
                const promises = [];
                this.action[action].modules.forEach(module => {
                  if (this.action.doNotUpdate.modules.includes(module.name)) {
                    console.log(`skipping module ${module.name}`);
                    return;
                  }
                  const data = {
                    data: module.code,
                    moduleName: module.name,
                  };

                  const promise = this.updateModule(data);
                  promises.push(promise);
                });
                // console.log(promises);
                await Promise.all(promises).then(() => console.log('*** updated all modules ***'));
              } catch (error) {
                console.log('error updateExisting module', error);
              }
            }
            if (this.action[action].components.length) {
              const promises = [];
              this.action[action].components.forEach(comp => {
                if (this.action.doNotUpdate.components.includes(comp.name)) {
                  console.log(`skipping component ${comp.name}`);
                  return;
                }
                const data = {
                  componentName: comp.name,
                  code: comp.code,
                };
                const promise = this.updateComponent(data);
                promises.push(promise);
              });
              // console.log(promises);
              await Promise.all(promises).then(() => console.log('*** updated all components ***'));
            }
            if (Object.keys(this.action[action].connectors).length) {
              const { connectors } = this.action[action];
              const promises = [];

              for (const type in connectors) {
                connectors[type].forEach(endpoint => {
                  const doNotUpdate = this.action.doNotUpdate.connectors;
                  let skipIteration = false;
                  for (const c of doNotUpdate) {
                    if (c.connector === type && c.endpoint === endpoint.name) {
                      skipIteration = true;
                      break;
                    }
                  }
                  if (skipIteration) {
                    console.log(`skipping endpoint=${endpoint.name} of connector=${type}`);
                    return;
                  }
                  const endpointData = { ...endpoint, type };
                  const promise = this.updateConnectorEndpoint(endpointData);
                  promises.push(promise);
                });
              }
              await Promise.all(promises).then(() => console.log('*** updated all connectors ***'));
            }
          }
        }
        this.$refs.importModal.hide();
        this.$emit('reset', { ok: true, rootModule: this.uploadedModuleData.rootModule });
      } catch (error) {
        console.log('error', error);
        this.$refs.importModal.hide();
        this.$emit('reset', { ok: false, rootModule: this.uploadedModuleData.rootModule });
      }
    },

    hasConflicts() {
      const { rootModule, subModules, components, connectors } = this.errors;
      if (rootModule.length || subModules.length || components.length || connectors.length) {
        return true;
      }
      return false;
    },
    async importModule(e) {
      if (this.conflictsResolved) {
        await this.createUpdateModuleAndItsDependencies();
      } else {
        e.preventDefault();
        this.errors = {
          rootModule: [],
          subModules: [],
          components: [],
          connectors: [],
        };
        this.checkbox.disabled = true;

        const { subModules, components, rootModule, connectors } = this.uploadedModuleData;
        console.log('this.uploadedModuleData', this.uploadedModuleData);

        this.checkRootModule(rootModule);

        if (this.checkbox.importSubModules) {
          this.checksubModules(subModules);
        }
        if (this.checkbox.importComponents) {
          this.checkComponents(components);
        }
        if (this.checkbox.importConnectors) {
          this.checkConnectors(connectors);
        }
        if (!this.hasConflicts()) {
          await this.createUpdateModuleAndItsDependencies();
        }
      }
    },

    resetModal() {
      this.checkbox.importSubModules = false;
      this.checkbox.importComponents = false;
      this.checkbox.importConnectors = false;
      this.checkbox.disabled = false;
      this.conflictsResolved = false;

      this.errors = {
        rootModule: [],
        subModules: [],
        components: [],
        connectors: [],
      };
      this.action = {
        createNew: {
          modules: [],
          components: [],
          connectors: [],
        },
        updateExisting: {
          modules: [],
          components: [],
          connectors: [],
        },
        doNotUpdate: {
          modules: [],
          components: [],
          connectors: [],
        },
      };
    },
    onModalShow() {
      this.resetModal();
    },
    onModalHide() {
      this.resetModal();
    },
  },
};
</script>
<style lang="scss" scoped>
.delete-modal {
  background-color: #fffaf3;
  color: #573a08;
  border-radius: 5px;
  padding: 1em 1.5em;
  border: 1px solid #573a08;
}
.delete-modal-font {
  font-size: 0.9em;
}
</style>
