<template>
  <!-- eslint-disable-next-line vue/no-v-html -->
  <th v-if="isHeader" class="vuetable-th-component-button" v-html="title"></th>
  <td v-else class="vuetable-td-component-button d-flex justify-content-center">
    <div class="btn-group" role="group" v-tooltip="{ content: buttonTooltipText }">
      &nbsp;
      <button type="button" :disabled="buttonDisabled" :class="buttonClass" class="btn cs-btn px-3 py-0" @click="actionClick($event)">
        <!-- eslint-disable-next-line vue/no-v-html -->
        <span v-html="buttonText"></span>
      </button>
    </div>
  </td>
</template>

<script>
/* eslint-disable import/no-unresolved */
// import { hideTooltip, showTooltip } from '@/utils/tooltip-alerts';
import VuetableFieldMixin from 'vuetable-2/src/components/VuetableFieldMixin';

export default {
  name: 'VuetableFieldButton',

  mixins: [VuetableFieldMixin],
  data() {
    return {
      state: '',
      buttonTooltipText: '',
      buttonDisabled: true,
      buttonText: 'Launch',
      buttonClass: '',
      canLaunchToProduction: false,
    };
  },

  mounted() {
    this.canLaunchToProduction = this.$parent.$parent.hasLaunchToProdPermission;

    let rowData;
    let svcName;
    let prodState;

    if (this.$options.propsData.rowData) {
      rowData = this.$options.propsData.rowData;
      svcName = rowData.appname;
      prodState = rowData.isProd ? 'prod' : 'preprod';
      this.populateTableRow(rowData);

      this.$root.$on(`refresh-launchpad-child-${svcName}-${prodState}`, service => {
        // ---- Is only a valid event if it matches the criteria --
        //      i.e. an event triggered for THIS SPECIFIC ROW of the table -- and the state has changed
        // ---- Overwrite the 'state' from a valid 'event' (it is NEWER than the stored state)
        this.populateTableRow(service, true /* from Event */);
      });
    }
  },
  methods: {
    populateTableRow(rowData) {
      // FIXME: healthy will have the ROLLBACK action assigned in future. it is temporarily overridden here
      // let buttonText = { healthy: "Rollback", impaired: "Rollback", empty: "Launch" };

      const { isProd } = rowData;

      if (typeof rowData.status !== 'undefined') this.state = rowData.status.toLowerCase();

      // set defaults for button...  disabled=true / buttonText / tooltipText='' / class (for border) = ''
      this.buttonState({ disabled: false, state: 'info', text: isProd ? 'Rollback' : 'Launch' });

      if (!isProd) {
        // Launch to prod if Healthy
        if (this.state.match(/healthy/i)) {
          // production exists, and version is NEWER --- Then we CAN LAUNCH (button NOT disabled)
          if (!rowData.prodVersion || parseInt(rowData.prodVersion, 10) < parseInt(rowData.version, 10)) {
            if (this.canLaunchToProduction) this.buttonState({ disabled: false, state: 'success' });
            else this.buttonState({ tooltip: "You don't have sufficient permissions to launch a module" });
          } else this.buttonState({ text: 'Launched', btnClass: 'border-0' });
        } else this.buttonState({ tooltip: 'Can only launch a healthy module' });
      } else {
        // healthy or not -- any "production" environment offers a rollback
        // FIXME ----- DISABLED BY DEFAULT, once rollback feature is implemented, we can enable this
        this.buttonState({ /* disabled: true, */ text: 'Rollback', tooltip: 'Not Implemented' });
      }
    },
    buttonState(options) {
      const defaultOptions = { disabled: true, btnClass: '', text: 'Launch', state: 'inactive', tooltip: '' };

      Object.keys(options).forEach(key => {
        // eslint-disable-next-line no-param-reassign
        if (!Object.keys(defaultOptions).includes(key)) delete options[key];
      });

      Object.keys(defaultOptions).forEach(key => {
        // eslint-disable-next-line no-param-reassign
        if (typeof options[key] === 'undefined') options[key] = defaultOptions[key];
      });

      const { disabled, btnClass, text, state, tooltip } = options;

      this.buttonDisabled = disabled;
      this.buttonText = text;
      this.buttonTooltipText = tooltip;
      this.buttonClass = `cs-btn-outline-${state} ${btnClass}`;
    },
    actionClick() {
      // ---- FIXME .... use EVENT to trigger this, currently this is considered an "Anti-Pattern" (where we call this.$parent.$parent... )
      this.$parent.$parent.toggleLaunchModal(
        this.$options.propsData.rowData,
        this.state,
        this /* sending SELF for controlling the button state and text in the event of errors */,
      );
    },
  },
};
</script>
