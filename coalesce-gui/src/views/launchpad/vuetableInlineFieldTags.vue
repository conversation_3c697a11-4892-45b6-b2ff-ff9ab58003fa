<!-- eslint-disable vue/no-v-html -->
<!-- eslint-disable vue/no-template-shadow -->
<template>
  <th v-if="isHeader" class v-html="title"></th>
  <td v-else class>
    <div v-if="(!isProd && canEditCodesOnStaging) || (isProd && canEditCodesOnProduction)">
      <b-form-tags
        style="max-width: 18em"
        size="sm"
        no-outer-focus
        duplicate-tag-text="duplicate"
        placeholder
        remove-on-delete
        class="mt-0"
        v-model="tags"
        :id="id"
        @input="changeTag()"
        @tag-state="validate()"
        :tag-validator="tagValidator"
        :ref="id"
        :disabled="isDisabled"
        tag-class="badge-light font-weight-bold border border-success"
      >
        <template v-slot="{ tags, inputAttrs, inputHandlers, addTag, removeTag }">
          <ul
            v-tooltip.top-end="{
              offset: 5,
              content: invalidCodeText,
              show: invalidCodeText != '',
            }"
            id="my-custom-tags-list"
            aria-live="polite"
            aria-atomic="false"
            aria-relevant="additions removals"
            class="list-unstyled mt-n1 mb-0 d-flex flex-wrap align-items-center"
          >
            <li
              :key="tag"
              :id="`my-custom-tags-tag_${tag.replace(/\s/g, '_')}_`"
              tag="li"
              body-class="py-1 pr-2 text-nowrap"
              v-for="tag in tags"
              class="badge badge-secondary badge-light b-form-tag d-inline-flex align-items-baseline"
              :class="'mw-100 mt-1 mr-1 font-weight-bold border border-success'"
            >
              <span class="b-form-tag-content flex-grow-1 text-truncate">{{ tag }}</span>
              <button
                type="button"
                @click="removeTag(tag)"
                class="close b-form-tag-remove ml-1"
                :aria-controls="`my-custom-tags-tag_${tag.replace(/\s/g, '_')}_`"
              >
                ×
              </button>
            </li>
            <li role="group" aria-live="off" class="d-inline-flex flex-grow-1 mt-1">
              <input
                :ref="'tagInput-' + id"
                v-bind="inputAttrs"
                v-on="!inputDisabled ? inputHandlers : false"
                @input="inputButtonVisible = inputAttrs != '' ? true : false"
                @focus="$root.$emit('prevent-launchpad-table-refresh')"
                @blur="$root.$emit('allow-launchpad-table-refresh')"
                class="b-form-tags-input w-100 flex-grow-1 p-0 m-0 bg-transparent border-0"
                style="outline: 0px; min-width: 5rem"
              />
              <button
                @click="addTag()"
                :class="{ 'd-none': !inputButtonVisible, disabled: !inputButtonVisible }"
                type="button"
                class="btn b-form-tags-button py-0 btn-outline-secondary disabled invisible"
                style="font-size: 90%"
              >
                Add
              </button>
            </li>
          </ul>
        </template>
      </b-form-tags>
    </div>
    <div v-else>{{ tags.join(', ') }}</div>
  </td>
</template>

<script>
// eslint-disable-next-line import/no-unresolved
import VuetableFieldMixin from 'vuetable-2/src/components/VuetableFieldMixin';
import { mapGetters } from 'vuex';

export default {
  name: 'VuetableInlineFieldTags',

  mixins: [VuetableFieldMixin],
  data() {
    return {
      inputDisabled: false,
      inputButtonVisible: true,
      isDisabled: false,
      id: null,
      tag: '',
      tags: [],
      state: '',
      invalidCodeText: '',
      allCodes: [],
      ussdCodes: {
        name: '',
        codes: { remove: [], add: [] },
        isProd: false,
        moduleId: '',
      },
      canEditCodesOnStaging: false,
      canEditCodesOnProduction: false,
    };
  },
  computed: {
    ...mapGetters(['user']),
    isProd() {
      return this.$parent.$parent.currentNav === 'prod';
    },
  },
  // watch: {
  //   tags(to, from) {
  //     this.$parent.$parent.hideTooltipforUssdCodes();
  //     this.$nextTick(() => {
  //       if (from.length !== 0 && to.length === 0 && this.$options.propsData.rowData.isProd) {
  //         this.tags = from;
  //         // this.$parent.$parent.showTooltipforUssdCodes();
  //       }
  //     });
  //   },
  // },
  mounted() {
    this.canEditCodesOnStaging = this.$parent.$parent.hasPreCodeEditPermission;
    this.canEditCodesOnProduction = this.$parent.$parent.hasProdCodeEditPermission;

    let rowData;
    if (this.$options.propsData.rowData) {
      rowData = this.$options.propsData.rowData;

      this.updateRow(rowData);

      this.allCodes = this.$parent.$parent.shortcodes;

      const svcName = rowData.appname;
      const prodState = rowData.isProd ? 'prod' : 'preprod';

      this.$root.$on(`refresh-launchpad-codes-${svcName}-${prodState}`, updatedRowData => {
        this.updateRow(updatedRowData);
      });
    }
  },
  methods: {
    updateRow(rowData) {
      const env = rowData.isProd ? 'prod' : 'staging';
      this.id = String(`${rowData.appname}-mid-${rowData.moduleId}-${env}`);
      this.tags = rowData.codes;
      this.ussdCodes.codes.remove = rowData.codes;
      this.ussdCodes.name = rowData.appname;
      this.ussdCodes.isProd = rowData.isProd;
      this.ussdCodes.moduleId = rowData.moduleId;
    },
    tagValidator(tag) {
      this.$parent.$parent.hideTooltipforUssdCodes();
      this.invalidCodeText = '';
      if (tag.length > 0) {
        if (this.$parent.$parent.shortcodes.includes(tag)) this.invalidCodeText = 'Shortcode in use!';
        else if (!tag.match(/^[0-9]{2,}(\*[0-9]{1,})*$/)) this.invalidCodeText = 'Invalid shortcode';
      }

      return this.invalidCodeText === '';
    },
    validate() {
      const tag = this.$refs[this.id].newTag;
      console.debug('running validate();', tag);
      // console.log("let tag = this.$refs[this.id]", this.$refs[this.id]);
      const isValid = this.tagValidator(tag);
      return isValid;
    },
    changeTag() {
      this.inputDisabled = true;
      this.$root.$emit('prevent-launchpad-table-refresh');
      console.debug('running changeTag();');
      const isValid = this.validate();
      // if (this.$options.propsData.rowData.isProd === true && this.tags.length <= 0) return;
      // console.log('continuing changeTag();');

      if (isValid) {
        this.$nextTick(() => {
          this.ussdCodes.codes.add = this.tags;

          // Get the all current tags
          this.ussdCodes.codes.allTags = this.tags;

          console.log("Codes right before 'update' request", JSON.stringify(this.ussdCodes.codes));

          this.$store
            .dispatch('launchpad/updateUssdCodes', this.ussdCodes)
            .then(() => {
              this.$parent.$parent.updateShortCodes(this.ussdCodes.codes);
            })
            .catch(err => {
              this.$toast.error(
                `Failed to update the shortcodes. You have to refresh the page to see the changes. The server error is: ${err.message || err}. `,
              );
              console.log(err.message || err); // Replaced alert with console.log for better debugging
              // You could use a custom notification system here instead of alert()
            })
            .then(() => {
              this.inputDisabled = false;
            });
        });
      } else {
        this.inputDisabled = false;
        console.log('duplicate');
      }
    },
  },
};
</script>

<style>
.vuetable-field-tags .b-form-tags-input {
  max-width: 1em !important;
}

.vuetable-field-tags .b-form-tags-button {
  display: none;
}
</style>
