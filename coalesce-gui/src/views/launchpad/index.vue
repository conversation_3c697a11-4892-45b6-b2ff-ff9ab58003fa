<template>
  <div class="launchpad-container">
    <div class="mb-5 pb-3 d-flex flex-row justify-content-between align-items-center">
      <h1 class="cs-text-primary pl-2 pt-2"><i class="fas fa-paper-plane"></i>&nbsp;&nbsp;Launchpad</h1>
      <div class="pr-4 d-flex justify-content-end align-items-center">
        <div style="min-width:7em">Refresh&nbsp;Every:</div>
        <select v-model="refreshIntervalSeconds" class="form-control ml-3 px-1">
          <option v-for="(refreshInterval, idx) in refreshIntervals" :key="idx" :value="refreshInterval.value" class="text-right">
            {{ refreshInterval.text }}
          </option>
        </select>
      </div>
    </div>
    <nav
      v-tooltip.top-end="{
        classes: tooltips.shortcodes.type,
        content: tooltips.shortcodes.content,
        trigger: 'manual',
        offset: -40,
        show: tooltips.shortcodes.visible,
      }"
    >
      <div
        class="nav nav-tabs"
        id="nav-tab"
        role="tablist"
        v-tooltip.top-end="{
          classes: tooltips.vuetables.type + ' mr-2',
          content: tooltips.vuetables.content,
          trigger: 'manual',
          offset: 7,
          show: tooltips.vuetables.visible,
        }"
      >
        <a
          class="nav-item nav-link"
          :class="{ active: currentNav == 'pre' }"
          @click="currentNav = 'pre'"
          id="nav-preprod-tab"
          data-toggle="tab"
          href="#nav-preprod"
          role="tab"
          aria-controls="nav-preprod"
          aria-selected="true"
          >Pre-Production</a
        >
        <a
          class="nav-item nav-link"
          :class="{ active: currentNav == 'prod' }"
          @click="currentNav = 'prod'"
          id="nav-prod-tab"
          data-toggle="tab"
          href="#nav-prod"
          role="tab"
          aria-controls="nav-prod"
          aria-selected="true"
          >Production</a
        >
      </div>
    </nav>
    <div id="nav-tabContent" class="tab-content pt-3">
      <!--h4 class="ml-1 py-2 px-4 vuetable-title preprod d-inline">Pre&nbsp;Production</h4-->
      <div class="tab-pane fade show active" id="nav-preprod" role="tabpanel" aria-labelledby="nav-preprod-tab">
        <vuetable
          ref="vt_preprod"
          class="mt-3"
          :api-mode="false"
          :data="localData.preprod"
          :fields="fields.preprod"
          :css="css.table.preprod"
          :show-sort-icons="true"
          :per-page="10"
          pagination-path
        ></vuetable>
      </div>
      <!--div class="mt-4">&nbsp;</div>
      <h4 class="ml-1 mt-5 py-2 px-4 vuetable-title prod d-inline">Production</h4-->
      <div class="tab-pane fade show" id="nav-prod" role="tabpanel" aria-labelledby="nav-prod-tab">
        <vuetable
          ref="vt_prod"
          class="mt-3"
          :api-mode="false"
          :data="localData.prod"
          :fields="fields.prod"
          :css="css.table.prod"
          :show-sort-icons="true"
          :per-page="10"
          pagination-path
        ></vuetable>
      </div>
    </div>
    <b-modal class="modal fade" size="lg" id="launchModal" ref="launchmodal" centered hide-footer>
      <!-- eslint-disable-next-line vue/no-unused-vars -->
      <template v-slot:modal-header="{ hide }">
        <div class="d-flex flex-column w-100 text-center">
          <div class="d-flex flex-row justify-content-around">
            <h2>
              {{ actionTextOn[launchModal.action] }}
              <span class="cs-text-coalesce">{{ launchModal.actingService.name }}</span>
              <br />
              <span v-if="launchModal.action != 'empty'">on </span>
              <span v-if="launchModal.action == 'empty'">to </span>
              <strong>{{ launchModal.actingService.destination }}</strong>
              <br />
            </h2>
          </div>
        </div>
      </template>
      <b-form class="d-flex flex-column" @submit.prevent="launchSubmit">
        <div class="d-flex flex-row justify-content-between align-items-center w-100 pb-2 px-4">
          <div>
            <h4 class="text-right mt-2">
              <small>Launch version: {{ launchModal.actingService.version }}</small>
            </h4>
          </div>
          <div class="py-2">
            <button
              type="button"
              class="m-2 btn cs-btn cs-btn-outline-danger"
              @click="$bvModal.hide('launchModal')"
              v-tooltip.left="{
                classes: tooltips.launchSubmit.type,
                content: tooltips.launchSubmit.content,
                trigger: 'manual',
                offset: 10,
                show: tooltips.launchSubmit.visible,
              }"
            >
              Cancel&nbsp;&nbsp;
              <i class="fas fa-times"></i>
            </button>
            <!--
            <span
              v-tooltip.top="
                !this.launchModal.selectBox.selected.length && {
                  content: 'Please enter at least one short code',
                  offset: 20,
                }
              "
            >
            -->
            <button type="submit" class="m-2 cs-btn btn" :class="classes.launchModal.submitButton">
              {{ actionTextOn[launchModal.action] }}
              <font-awesome-icon
                class="ml-1"
                :icon="launchModal.submitBusy ? 'circle-notch' : 'rocket'"
                :spin="launchModal.submitBusy"
              ></font-awesome-icon>
            </button>
            <!-- </span> -->
          </div>
        </div>
      </b-form>
    </b-modal>
  </div>
</template>

<script>
/* eslint-disable no-param-reassign */
/* eslint-disable import/no-unresolved */
import PermissionManager from '@/utils/PermissionManager';
import { hideTooltip, showTooltip } from '@/utils/tooltip-alerts';
import { mapGetters } from 'vuex';
import moment from 'moment';

import { Vuetable } from 'vuetable-2';
import CssConfig from './VuetableBootstrapConfig'; // https://codesandbox.io/s/90qx4488w
// import VuetablePagination from './VuetablePaginationBootstrap.vue';
// import VuetablePaginationInfo from "vuetable-2";
import VuetableFieldButton from './VuetableFieldButton.vue';
import vuetableInlineFieldTags from './vuetableInlineFieldTags.vue';

// https://codesandbox.io/s/7zkrn4lvz0

export default {
  name: 'Launchpad',
  components: {
    Vuetable,
    // VuetablePagination,
    // VuetablePaginationInfo
  },
  data() {
    return {
      hasPreCodeEditPermission: false,
      hasProdCodeEditPermission: false,
      hasLaunchToProdPermission: false,
      currentNav: 'pre', // pre | prod
      shortcodes: [],
      allCodes: {},
      tz_offset: 0,
      preventTableRefresh: false,
      refreshIntervalSeconds: 60, // default --- probably good to save this to USER preferences somewhere
      refreshIntervals: [
        { text: '5s', value: 5 },
        { text: '15s', value: 15 },
        { text: '30s', value: 30 },
        { text: '1m', value: 60 },
        { text: '5m', value: 60 * 5 },
        { text: '15m', value: 60 * 15 },
      ],
      localData: {
        preprod: { data: [] },
        prod: { data: [] },
      },
      actionTextOn: {
        empty: 'Launch',
        impaired: 'Rollback',
        // ---- FIXME: Under normal circumstances, a HEALTHY service would have the option to ROLLBACK - it is temporarily overridden here
        // healthy: "Rollback"
        healthy: 'Launch',
      },
      launchModal: {
        submitBusy: false,
        selectBox: {
          tagboxDirty: false,
          allowedCodes: [],
          disallowedCodes: [],
          timeoutDelay: null,
          invalidCodeText: '',
          selected: [],
          selectOptions: [],
        },
        action: '',
        title: '',
        actingService: {
          name: '',
          source: 'Staging',
          destination: 'Production',
          version: 'unknown',
          codes: { remove: [], add: {} },
        },
      },
      classes: {
        launchButton: 'cs-btn-submit',
        launchModal: {
          submitButton: 'cs-btn-submit',
        },
      },
      codesTooltipDelayTimeout: null,
      codesTooltipDelay: 350, // milliseconds
      tooltips: {
        vuetables: {
          visible: false,
          timeout: null,
          type: '',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
        codes: {
          visible: false,
          timeout: null,
          type: '',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
        shortcodes: {
          visible: false,
          timeout: null,
          type: '',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
        launchSubmit: {
          visible: false,
          timeout: null,
          type: '',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
      },
      fields: {
        preprod: [
          {
            name: 'appname',
            title: 'App Name',
            sortField: 'appname',
            titleClass: 'cs-table-title pre-title',
            dataClass: 'cs-table-row row-text-pre',
          },
          {
            name: vuetableInlineFieldTags,
            title: 'USSD Code(s)',
            sortField: 'codes',
            titleClass: 'cs-table-title pre-title',
            dataClass: 'cs-table-row row-text-pre vuetable-field-tags',
            formatter: value => {
              const result = value.length > 0 ? value.join(', ') : 'No Code(s)';
              return result;
            },
          },
          {
            name: 'date',
            title: 'Date',
            sortField: 'date',
            titleClass: 'cs-table-title pre-title',
          },
          {
            name: 'version',
            title: 'Version',
            sortField: 'version',
            titleClass: 'cs-table-title pre-title',
          },
          {
            name: 'status',
            title: 'Status',
            sortField: 'status',
            titleClass: 'cs-table-title pre-title',
            formatter: this.stateColor,
          },
          {
            name: VuetableFieldButton,
            title: '&nbsp;',
            titleClass: 'center aligned cs-table-title normal-title',
            dataClass: 'left aligned vuetable-field-button',
            switch: {
              // label: 'Male?',
              label: data => data.name,
              field: data => data.gender === 'M',
            },
          },
        ],
        prod: [
          {
            name: 'appname',
            title: 'App Name',
            sortField: 'appname',
            titleClass: 'cs-table-title prod-title',
            dataClass: 'cs-table-row row-text-prod',
          },
          {
            name: vuetableInlineFieldTags,
            title: 'USSD Code(s)',
            sortField: 'codes',
            titleClass: 'cs-table-title prod-title',
            dataClass: 'cs-table-row row-text-prod vuetable-field-tags',
            formatter: value => {
              const result = value.length > 0 ? value.join(', ') : 'No Code(s)';
              return result;
            },
          },
          {
            name: 'date',
            title: 'Date',
            sortField: 'date',
            titleClass: 'cs-table-title prod-title',
          },
          {
            name: 'version',
            title: 'Version',
            sortField: 'prodversion',
            titleClass: 'cs-table-title prod-title',
          },
          {
            name: 'status',
            title: 'Status',
            sortField: 'status',
            titleClass: 'cs-table-title prod-title',
            formatter: this.stateColor,
          },
          /*
          {
            name: 'approver',
            title: 'Approver',
            sortField: 'approver',
            titleClass: "cs-table-title normal-title",
          },
          */
          {
            name: VuetableFieldButton,
            title: '&nbsp;',
            titleClass: 'center aligned cs-table-title normal-title',
            dataClass: 'left aligned',
            switch: {
              // label: 'Male?',
              label: data => data.name,
              field: data => data.gender === 'M',
            },
          },
        ],
      },
      sortOrder: [{ field: 'appname', direction: 'asc' }],
      css: CssConfig,
    };
  },
  computed: {
    ...mapGetters(['user', 'modules', 'userPermissions']),
    pm() {
      if (!this.userPermissions) return undefined;

      const pm = new PermissionManager(this.userPermissions);
      return pm;
    },
    state() {
      return this.launchModal.selectBox.tagboxDirty ? this.launchModal.selectBox.selected.length >= 1 : null;
    },
    tagboxDirty() {
      return this.launchModal.selectBox.tagboxDirty; // for watcher...
    },
  },
  watch: {
    tagboxDirty() {
      this.launchModal.selectBox.tagboxDirty = true;
    },
    refreshIntervalSeconds() {
      console.log('Table refresh changed (with immediate effect) to... ', this.refreshIntervalSeconds);

      if (this.timer) clearInterval(this.timer);

      this.timer = setInterval(() => {
        this.loadTableData();
      }, this.refreshIntervalSeconds * 1000);
    },
  },
  created() {
    // returns minutes to get from LOCAL TZ to UTC
    //  EXAMPLE:: If we are +2 GMT ----- difference (to get to UTC from where we are) will be -120 minutes
    //
    this.tz_offset = new Date().getTimezoneOffset();
    this.tz_offset *= -1; // change from "Local to UTC" - to be "UTC to Local"
    this.timer = setInterval(() => {
      this.loadTableData();
    }, this.refreshIntervalSeconds * 1000);
  },
  mounted() {
    this.$root.$on('prevent-launchpad-table-refresh', () => {
      console.log('Preventing table refresh ... ');
      this.preventTableRefresh = true;
    });
    this.$root.$on('allow-launchpad-table-refresh', () => {
      setTimeout(() => {
        this.preventTableRefresh = false;
      }, 2000);
    });
    this.$root.$on('launchpad-table-refresh', () => {
      this.preventTableRefresh = false;
      this.loadTableData();
    });
    this.loadTableData();
    try {
      // check if user can edit short codes on pre-production table
      this.hasPreCodeEditPermission = this.pm && this.pm.hasPermission({ can: 'codeedit', forGroup: 'preprodservice' });

      // check if user can edit short codes on production table
      this.hasProdCodeEditPermission = this.pm && this.pm.hasPermission({ can: 'codeedit', forGroup: 'prodservice' });

      // check if user can launch modules into production
      this.hasLaunchToProdPermission = this.pm && this.pm.hasPermission({ can: 'launch', a: 'prodservice' });
    } catch (error) {
      console.log('error', error);
    }
  },
  beforeDestroy() {
    if (this.timer) clearInterval(this.timer);
  },
  methods: {
    tagValidator(tag) {
      this.launchModal.selectBox.invalidCodeText = '';

      if (tag.length > 0) {
        if (Object.values(this.launchModal.selectBox.disallowedCodes).includes(tag)) this.launchModal.selectBox.invalidCodeText = 'Shortcode in use!';
        else if (!tag.match(/^[0-9]{2,}(\*[0-9]{1,})*$/)) this.launchModal.selectBox.invalidCodeText = 'Invalid shortcode';
      }

      return this.launchModal.selectBox.invalidCodeText === '';
    },
    loadTableData() {
      if (this.preventTableRefresh) {
        console.log('Refresh prevention enabled ... not refreshing');
        return;
      }

      hideTooltip(this.tooltips.vuetables);

      this.$store
        .dispatch('launchpad/services', { sort: '', page: 1, per_page: 25 })
        .then(response => {
          console.log('Launchpad Data', response);

          Object.keys(response.data).forEach(key => {
            for (let i = 0; i < response.data[key].data.length; i++) {
              const svc = response.data[key].data[i];

              const prodState = svc.isProd ? 'prod' : 'preprod';
              const svcName = svc.appname;

              if (this.localData[key].data.length > 0) {
                if (!this.localData[key].data[i]) return;

                const stateChanged = this.localData[key].data[i].status !== svc.status;

                // ---- is production out of date compared with pre-production?
                let prodOutdated = false;
                if (key === 'preprod') {
                  this.localData.preprod.data.forEach(ele => {
                    if (
                      this.localData[key].data[i].version &&
                      svc.version &&
                      parseInt(this.localData[key].data[i].version, 10) !== parseInt(svc.version, 10)
                    ) {
                      prodOutdated = true;
                    }

                    if (
                      this.localData[key].data[i].prodVersion &&
                      svc.prodVersion &&
                      parseInt(this.localData[key].data[i].prodVersion, 10) !== parseInt(svc.prodVersion, 10)
                    ) {
                      prodOutdated = true;
                    }

                    if (ele.appname === svc.appname && ele.version === svc.version) {
                      this.$root.$emit(`refresh-launchpad-child-${ele.appname}-preprod`, ele);
                    }
                  });
                }

                // ---- if either is true ... the BUTTON on the vuetable child must be updated to match the new state
                if (stateChanged || prodOutdated) {
                  // event listener is in VuetableFieldButton.vue file
                  this.$root.$emit(`refresh-launchpad-child-${svcName}-${prodState}`, svc);
                }

                this.$root.$emit(`refresh-launchpad-codes-${svcName}-${prodState}`, svc); // event listener is in VuetableInlineFieldTags.vue file
                // }
              }
            }
          });

          this.localData.preprod = response.data.preprod;
          this.localData.prod = response.data.prod;

          // ---- prepopulate all possible codes, used for validation in the 'tagbox' on the launch modal screen
          this.allCodes = [];
          this.shortcodes = [];
          this.localData.preprod.data.forEach(svc => {
            svc.codes.forEach(c => {
              this.allCodes.push(c);
              this.shortcodes.push(c);
            });
          });
          this.localData.prod.data.forEach(svc => {
            svc.codes.forEach(c => {
              this.allCodes.push(c);
              this.shortcodes.push(c);
            });
          });

          // Fix DB data from UTC to local time
          this.localData.preprod.data.map(svc => {
            if (svc.date) {
              // has valid date
              // eslint-disable-next-line no-param-reassign
              svc.date = moment(svc.date)
                .add(this.tz_offset, 'm')
                .format('YYYY-MM-DD HH:mm');
            }
            return svc;
          });
          this.localData.prod.data.map(svc => {
            if (svc.date) {
              // has valid date
              // eslint-disable-next-line no-param-reassign
              svc.date = moment(svc.date)
                .add(this.tz_offset, 'm')
                .format('YYYY-MM-DD HH:mm');
            }
            return svc;
          });

          this.$refs.vt_preprod.reload();
          this.$refs.vt_preprod.refresh();
          this.$refs.vt_prod.reload();
          this.$refs.vt_prod.refresh();
        })
        .catch(err => {
          showTooltip(this.tooltips.vuetables, 'cs-warning', 'Unable to retrieve launchpad table data');
          console.log('Could not retrieve launchpad LIST :/', err);
          // ---- ON ERROR, prevent reload ??
          //    If this is done, we should provide some sort of "manual" trigger for users to refresh or reload
          //        (or just leave them to refresh the page?)
          //    if (this.timer) clearInterval(this.timer)
        });
    },
    // ---- triggered by child -> VuetableFieldButton.vue
    async toggleLaunchModal(serviceInfo, action, tableRowContext) {
      // reload table data -- in case it has changed prior to using the "Launch" option
      await this.loadTableData();

      let validState = false;
      this.localData.preprod.data.forEach(svc => {
        if (svc.appname === serviceInfo.appname) validState = svc.status.match(/healthy/i);
      });

      // ---- invalid state means the table has changed it's state --- we must refresh before we can proceed with further launch requests
      if (!validState) {
        tableRowContext.buttonText = 'Fault (hover)';
        tableRowContext.buttonTooltipText = 'Service state has changed, auto-refreshing the launchpad';
        tableRowContext.buttonDisabled = true;
        tableRowContext.buttonStateClass = 'cs-btn-outline-inactive';
        setTimeout(() => {
          this.localData.preprod = [];
          this.localData.prod = [];
          this.loadTableData();
        }, 7000);
        return;
      }

      hideTooltip(this.tooltips.launchSubmit);

      // options for 'action' are : healthy, impaired, or "EMPTY" (which means the service is not running - i.e. NO STATE)
      console.log('Requesting LAUNCH for service:\n', serviceInfo);

      console.log('production data:\n', this.localData.prod);
      // ---- we are working with a PRE-prod service.... about to launch to prod
      //    We need to determine valid production codes, and _the rest_ of the codes are "invalid" (can't be chosen to go to production with)
      //    Let's get the production code which is allowed... and remove it from the disallowed list...
      //      That leaves us with 2 lists:
      //      Allowed: <existing codes assigned to this service's PRODUCTION instance>
      //      Disallowed: All other codes, preprod or prod
      this.launchModal.selectBox.allowedCodes = [];
      this.localData.prod.data.forEach(prodSvc => {
        if (prodSvc.appname === serviceInfo.appname) this.launchModal.selectBox.allowedCodes = prodSvc.codes;
      });
      // copy codes so we can edit without breaking the original
      this.launchModal.selectBox.disallowedCodes = JSON.parse(JSON.stringify(this.allCodes));
      this.launchModal.selectBox.disallowedCodes = this.launchModal.selectBox.disallowedCodes.filter(c => {
        return !this.launchModal.selectBox.allowedCodes.includes(c);
      });

      // ---- Here we assign the "OLD" codes (allowed because they were _ALREADY_ assigned) ....
      //    OLD codes will be 'removed' (if they are not the same as 'added' codes provided AFTER this request is complete)
      this.launchModal.actingService.codes.remove = this.launchModal.selectBox.allowedCodes;

      this.launchModal.selectBox.selected = this.launchModal.selectBox.allowedCodes;

      // ---- FIXME: Under normal circumstances, HEALTHY would have it's own action, and will NOT be overridden
      //        However we are remporarily overriding it for customer release
      //        I.E. This will be removed later!
      if (String(action).toLowerCase() === 'healthy') action = 'empty';
      //    //    //

      this.launchModal.action = action === '' ? 'empty' : action;
      this.launchModal.actingService.version = serviceInfo.version;
      this.launchModal.actingService.name = serviceInfo.appname;
      this.launchModal.actingService.moduleId = serviceInfo.moduleId;
      this.launchModal.actingService.destination = 'Production';

      if (!this.$refs.launchmodal.isShow) this.$refs.launchmodal.show();
      else this.$refs.launchmodal.hide();
    },
    launchSubmit() {
      // ---- DETERMINE ACTION BEFORE MOVING FORWARD!
      console.log(`${this.actionTextOn[this.launchModal.action]} Clicked!`);
      // ---- Override that no other action can trigger aside from 'Launch' //// and validation succeeds
      if (this.actionTextOn[this.launchModal.action] !== 'Launch') return;

      // ---- After we confirm that there are codes assigned, we want to submit them with the request
      this.launchModal.submitBusy = true;
      hideTooltip(this.tooltips.launchSubmit);
      this.$store
        .dispatch('launchpad/launch', this.launchModal.actingService)
        .then(response => {
          console.log('response', response);
          this.launchModal.submitBusy = false;
          this.classes.launchButton = 'cs-btn-inactive';
          showTooltip(this.tooltips.launchSubmit, 'cs-success', 'Launched to production!<br />', 5);
          this.loadTableData(); // reload table data
          setTimeout(() => {
            if (this.$refs.launchmodal.isShow) this.$refs.launchmodal.hide();
          }, 3 * 1000);
          console.log('Success?', response);
        })
        .catch(err => {
          this.launchModal.submitBusy = false;
          if (err.message === 'Error: Request failed with status code 403') {
            showTooltip(this.tooltips.launchSubmit, 'cs-warning', 'Sorry, you do not have permission to perform this action', 5);
          } else {
            showTooltip(
              this.tooltips.launchSubmit,
              'cs-error',
              `Failed to launch: ${typeof err.message === 'string' ? err.message : 'Server Error'}`,
            );
          }

          console.log('error', err.message || err);
        });
    },
    stateColor(value = '') {
      if (value == null || `${value}`.length <= 0) return '&nbsp;';

      const colorClass = value.match(/^healthy/i) ? 'cs-text-primary' : 'cs-text-danger';
      return `<span class="${colorClass}">${value}</span>`;
    },
    onAction(action, data, index) {
      console.log(`slot) action: ${action}`, data.name, index);
    },
    onPaginationData(paginationData) {
      this.$refs.pagination.setPaginationData(paginationData);
    },
    onChangePage(page) {
      this.$refs.vuetable.changePage(page);
    },
    editRow(rowData) {
      console.warn(`You clicked edit on${JSON.stringify(rowData)}`);
    },
    deleteRow(rowData) {
      console.warn(`You clicked delete on${JSON.stringify(rowData)}`);
    },
    onLoading() {
      console.log('loading... show your spinner here');
    },
    onLoaded() {
      console.log('loaded! .. hide your spinner here');
    },

    updateShortCodes(code) {
      code.remove.forEach(c => {
        this.shortcodes = this.shortcodes.filter(e => e !== c);
      });
      code.add.forEach(c => {
        this.shortcodes.push(c);
      });
    },
    showTooltipforUssdCodes() {
      setTimeout(() => {
        showTooltip(this.tooltips.shortcodes, 'cs-warning', 'you cannot delete the last code', 5);
      }, 300);
    },
    hideTooltipforUssdCodes() {
      hideTooltip(this.tooltips.shortcodes);
    },
  },
};
</script>

<style lang="scss" scoped>
@import '../../styles/cs-styles.scss';
.nav-tabs {
  border-width: 2px;
  .nav-link {
    border-width: 2px;
    color: $cs_color_default;
    margin-bottom: -2px;
    &.active {
      color: $coalesce_green;
      border-radius: 7px 7px 0 0;
    }
  }
}
.launchpad {
  &-container {
    margin: 30px;
  }
  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}

.VueTables__date-filter {
  border: 1px solid #ccc;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
}
</style>
<style lang="scss">
/* Importing for the purpose of variables --- this can probably be separated and handled better ... i.e. split variables off */
@import '../../styles/cs-styles.scss';

.table th {
  padding-top: 0.5em;
  padding-bottom: 0.5em;
}

.vuetable-title {
  background: #eee;
  &.preprod {
    color: $cs_color_info !important;
  }
  &.prod {
    color: $coalesce_green !important;
  }
}

/* Force "TH" because it otherwise styles the entire column :( ?? */
[class*='vuetable-td-'].cs-table-row {
  .row-text-pre,
  .row-text-prod {
    color: $cs_color_info;
  }
}
[class*='vuetable-th-'].cs-table-title {
  .sort-icon {
    float: right;
    padding-top: 0.2em;
    padding-bottom: 0.1em;
    margin-left: 0.2em;
  }
  background: #eee;
  padding-left: 1.2em;
  &.pre-title {
    color: $cs_color_info;
  }
  &.prod-title {
    color: $coalesce_green;
  }
  &.normal-title {
    color: #666;
  }
}
</style>
