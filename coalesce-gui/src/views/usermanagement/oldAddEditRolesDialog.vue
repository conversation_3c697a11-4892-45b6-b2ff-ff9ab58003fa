<template>
  <div>
    <b-modal ref="roleUpdateModal" scrollable centered hide-footer title="Update Role">
      <div role="group">
        <label for="input-live" class="bold">Name:</label>
        <b-form-input id="input-live" v-model="updatedInfo.name" disabled trim></b-form-input>

        <div class="mt-4">
          <b-form-group label="Select Permissions">
            <b-form-checkbox-group id="checkbox-group-2" v-model="selected" name="flavour-2" size="md" switches>
              <div v-for="(permission, index) in permissions" :key="index">
                <b-form-checkbox :value="permission.name">
                  {{ permission.name }}
                </b-form-checkbox>
              </div>
            </b-form-checkbox-group>
          </b-form-group>
          <div style="height: 6.4em;">
            <label for="input-description" class="mt-3 bold">Description:</label>
            <b-form-input
              id="input-description"
              v-model="updatedInfo.description"
              :state="updatedDescriptionState"
              placeholder="Enter Role Description"
              trim
            ></b-form-input>
            <b-form-invalid-feedback id="input-live-feedback">Enter at least 4 letters</b-form-invalid-feedback>
          </div>
          <div style="margin-left: 45%;">
            <span
              v-tooltip.top="
                isDeleteButtonDisabled && {
                  content: 'Cannot delete this Role, its being assigned to a user',
                  offset: 10,
                }
              "
            >
              <button type="button" class="btn cs-btn cs-btn-danger mt-2 btn-md mr-2" @click="deleteRole" :disabled="isDeleteButtonDisabled">
                Delete Role
              </button>
            </span>
            <span v-tooltip.top="!hasEditRoleFormChanged && { content: 'Nothing on the page has changed', offset: 10 }">
              <button
                type="button"
                class="btn cs-btn mt-2 btn-md"
                :class="[isUpdatedButtonDisabled || !hasEditRoleFormChanged ? 'cs-btn-inactive' : 'cs-btn-submit ']"
                @click="updateRole"
                :disabled="isUpdatedButtonDisabled"
              >
                Update Role
              </button>
            </span>
          </div>
        </div>
      </div>
    </b-modal>

    <b-modal id="roleModal" size="xl" title="Roles" hide-footer class="d-flex flex-column">
      <table class="table table-bordered">
        <thead>
          <tr>
            <th>Name</th>
            <th>Permissions</th>
            <th>Description</th>
          </tr>
        </thead>

        <tbody>
          <tr v-for="aRole in roles" :key="aRole.name" style="cursor: pointer;" @click="onRowClick(aRole)">
            <td class="align-middle">{{ aRole.name.replace(/^\w/, c => c.toUpperCase()) }}</td>

            <td>
              <ul class="p-0 d-flex flex-wrap">
                <li v-for="permission in aRole.permission" :key="permission" class="m-1 p-1 d-inline">
                  {{ `• ${permission.replace(/^\w/, c => c.toUpperCase())} ` }}
                </li>
              </ul>
            </td>
            <td class="align-middle">{{ aRole.description }}</td>
          </tr>
        </tbody>
      </table>

      <!-- <button type="button" class="btn btn-info float-right mt-2 btn-md" @click="addNewRow">
          <i class="fas fa-plus-circle"></i>
          Add New Role
        </button>-->
      <div class="d-flex justify-content-end">
        <button class="btn cs-btn cs-btn-submit" v-b-modal.roleCreateModal @click="clearSelectedRoles">
          Add New Role
        </button>
      </div>

      <b-modal ref="roleCreateModal" scrollable centered id="roleCreateModal" size="md" title="Create New Role" hide-footer>
        <div role="group">
          <label for="input-live" class="bold">Name:</label>
          <b-form-input
            class="w-100"
            id="input-live"
            v-model="name"
            :state="nameState"
            aria-describedby="input-live-help input-live-feedback"
            placeholder="Enter Role Name"
            trim
          ></b-form-input>

          <!-- This will only be shown if the preceding input has an invalid state -->
          <b-form-invalid-feedback id="input-live-feedback">Enter at least 4 letters</b-form-invalid-feedback>
          <div class="mt-4">
            <b-form-group label="Select Permissions">
              <b-form-checkbox-group id="checkbox-group-2" v-model="selected" name="flavour-2" size="md" switches>
                <div v-for="(permission, index) in permissions" :key="index">
                  <b-form-checkbox :value="permission.name">
                    {{ permission.name }}
                  </b-form-checkbox>
                </div>
              </b-form-checkbox-group>
            </b-form-group>
            <label for="input-description" class="mt-3 bold">Description:</label>
            <b-form-input
              id="input-description"
              v-model="description"
              :state="descriptionState"
              placeholder="Enter Role Description"
              trim
            ></b-form-input>

            <!-- This will only be shown if the preceding input has an invalid state -->
            <b-form-invalid-feedback id="input-live-feedback">Enter at least 4 letters</b-form-invalid-feedback>
            <div class="mt-2 float-right">
              <span
                v-tooltip.top="
                  (!name || !description || !selected) && {
                    content: 'Missing fields',
                    offset: 20,
                  }
                "
              >
                <button
                  type="button"
                  class="btn cs-btn btn-md"
                  :class="[isAddRoleButtonDisabled ? 'cs-btn-inactive' : 'cs-btn-submit ']"
                  @click="addNewRow"
                  :disabled="isAddRoleButtonDisabled"
                >
                  <i class="fas fa-plus-circle"></i>&emsp;Add New Role
                </button>
              </span>
            </div>
          </div>
        </div>
      </b-modal>
    </b-modal>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

import _ from 'lodash';

export default {
  name: 'OldAddEditRolesDialog',
  props: {
    // permission: Object,
    // roles: Array,
    permissions: {
      type: Array,
      default() {
        return [];
      },
    },
    data: {
      type: Object,
      default() {
        return {};
      },
    },
    localData: {
      type: Array,
      default() {
        return [];
      },
    },
    ummsVersion: {
      type: Object,
      default() {
        return {};
      },
    },
    /*
    index: {
      type: Number,
      default() {
        return 0;
      },
    },
    */
  },
  data() {
    return {
      roles: [],
      name: '',
      description: '',
      updatedInfo: {
        name: '',
        description: '',
        permission: [],
      },
      selected: [],
      role: {},
      isDeleteButtonDisabled: false,
      oldDescription: '',
      oldPermission: [],
    };
  },
  computed: {
    ...mapGetters(['user']),
    hasEditRoleFormChanged() {
      let changed = false;
      const samePersmissions = _.isEqual(this.oldPermission, this.selected);
      if (this.oldDescription !== this.updatedInfo.description || !samePersmissions) changed = true;

      return changed;
    },
    nameState() {
      return this.name.length > 3;
    },
    descriptionState() {
      return this.description.length > 3;
    },
    updatedDescriptionState() {
      return this.updatedInfo.description.length > 3;
    },
    isUpdatedButtonDisabled() {
      const descriptionCheck = this.updatedInfo.description.length > 3;
      const permissionCheck = this.selected.length > 0;
      if (!descriptionCheck || !permissionCheck) return true;

      return false;
    },
    isAddRoleButtonDisabled() {
      const nameCheck = this.name.length > 3;
      const descriptionCheck = this.description.length > 3;
      const permissionCheck = this.selected.length > 0;
      if (!nameCheck || !descriptionCheck || !permissionCheck) return true;

      return false;
    },
  },
  mounted() {
    this.$store
      .dispatch('user/listRoles', this.ummsVersion)
      .then(response => {
        response.roles.forEach(roleRow => {
          const { name, permission, description } = roleRow;
          this.roles.push({ name, permission, description });
        });
        console.log('stored roles ... ', this.roles);
      })
      .catch(err => {
        console.log(err);
      });
  },
  methods: {
    async addNewRow() {
      try {
        const role = {
          name: this.name,
          permission: this.selected,
          description: this.description,
          ummsVersion: this.ummsVersion.version,
        };
        console.log('role', role);
        await this.$store
          .dispatch('user/createRole', role)
          .then(() => {
            this.roles.push(role);
          })
          .catch(err => {
            console.error('Error creating new role', err);
          });
        await this.$emit('updateRolesInParent');
        //        let roles = await this.$store.dispatch("user/listRoles");
        // this.tableData = roles.role;
        this.$refs.roleCreateModal.hide();
        this.name = '';
        this.description = '';
        this.selected = [];
      } catch (e) {
        console.error('Error creating new role', e);
      }
    },
    async updateRole() {
      try {
        const role = {
          name: this.updatedInfo.name,
          permission: this.selected,
          description: this.updatedInfo.description,
          ummsVersion: this.ummsVersion.version,
        };
        await this.$store
          .dispatch('user/updateRole', role)
          .then(() => {
            this.roles = this.roles.map(mapRole => {
              if (mapRole.name === this.updatedInfo.name) {
                // eslint-disable-next-line no-param-reassign
                mapRole.permission = this.selected;
                // eslint-disable-next-line no-param-reassign
                mapRole.description = this.updatedInfo.description;
              }
              return mapRole;
            });
            this.$emit('updateRolesInParent');
          })
          .catch(err => {
            console.error('Problem updating role :/', err);
          });

        this.$refs.roleUpdateModal.hide();
      } catch (e) {
        console.error('Coult no update role :/', e);
      }
    },
    async deleteRole() {
      try {
        const role = {
          name: this.updatedInfo.name,
          permission: this.selected,
          description: this.updatedInfo.description,
        };
        await this.$store
          .dispatch('user/deleteRole', role)
          .then(() => {
            this.roles = this.roles.filter(ele => {
              return ele.name !== role.name;
            });
            this.$emit('updateRolesInParent');
          })
          .catch(err => {
            console.error('Problem deleting role :/', err);
          });
        this.$refs.roleUpdateModal.hide();
      } catch (e) {
        console.error('Coult no delete role :/', e);
      }
    },
    onRowClick(row) {
      this.updatedInfo.name = row.name;
      this.updatedInfo.permission = row.permission;
      this.updatedInfo.description = row.description;
      this.selected = row.permission;

      this.oldDescription = row.description;
      this.oldPermission = row.permission;

      //   console.log("this.updatedInfo", this.updatedInfo);
      const isRoleUsed = this.localData.some(ele => ele.role[0] === row.name);
      // console.log('isRoleUsed', isRoleUsed)
      this.isDeleteButtonDisabled = isRoleUsed;
      this.$refs.roleUpdateModal.show();
    },
    clearSelectedRoles() {
      this.description = '';
      this.selected = [];
      this.name = '';
    },
  },
};
</script>

<style lang="scss" scoped></style>
