<template>
  <div>
    <b-modal size="lg" ref="roleUpdateModal" scrollable centered hide-footer title="Update Role">
      <div role="group">
        <div class="container">
          <div class="row">
            <label for="input-live" class="bold col-2 align-self-center">Name:</label>
            <b-form-input class="col-10" id="input-live" v-model="updatedInfo.name" disabled trim></b-form-input>
          </div>

          <div class="row mt-3">
            <label for="input-description" class="bold col-2 align-self-center">Description:</label>
            <b-form-input
              class="col-10"
              id="input-description"
              v-model="updatedInfo.description"
              :state="updatedDescriptionState"
              placeholder="Enter Role Description"
              trim
            ></b-form-input>
            <b-form-invalid-feedback id="input-live-feedback">Enter at least 4 letters</b-form-invalid-feedback>
          </div>
        </div>

        <div class="mt-3 ml-3">
          <label for="input-live" class="bold">Select Permissions:</label>
          <div id="role-Permissions" class="container h-100">
            <div v-for="(permission, index) in permissions" :key="index">
              <permissionGroup
                :index="index"
                :data="permission"
                :selected="selected"
                :role="role"
                :permissions="permissions"
                @addPermission="addPermission"
                @removePermission="removePermission"
                @addAllGroupPermissions="addAllGroupPermissions"
                @removeAllGroupPermissions="removeAllGroupPermissions"
              />
            </div>
          </div>

          <div style="margin-left: 65%;">
            <span
              v-tooltip.top="
                isDeleteButtonDisabled && {
                  content: 'Cannot delete this Role, its being assigned to a user',
                  offset: 10,
                }
              "
            >
              <button type="button" class="btn cs-btn cs-btn-danger mt-2 btn-md mr-2" @click="deleteRole" :disabled="isDeleteButtonDisabled">
                Delete Role
              </button>
            </span>
            <span
              v-tooltip.top="
                !hasEditRoleFormChanged && {
                  content: 'Nothing on the page has changed',
                  offset: 10,
                }
              "
            >
              <button
                type="button"
                class="btn cs-btn mt-2 btn-md"
                :class="[isUpdatedButtonDisabled || !hasEditRoleFormChanged ? 'cs-btn-inactive' : 'cs-btn-submit ']"
                @click="updateRole"
                :disabled="isUpdatedButtonDisabled || !hasEditRoleFormChanged"
              >
                Update Role
              </button>
            </span>
          </div>
        </div>
      </div>
    </b-modal>
    <b-modal id="roleModal" size="xl" title="Roles" hide-footer class="d-flex flex-column">
      <table class="table table-sm table-bordered">
        <thead>
          <tr>
            <th style="width: 13%;">Name</th>
            <th style="width: 57%;">Permissions</th>
            <th style="width: 30%;">Description</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="aRole in roles"
            :key="aRole.name"
            :style="aRole.name != 'admin' ? 'cursor: pointer' : 'background: #f7f7f7'"
            @click="onRowClick(aRole)"
          >
            <td class="align-middle bold">{{ aRole.name.replace(/^\w/, c => c.toUpperCase()) }}</td>

            <td>
              <div v-for="(rolePermission, index) in aRole.permission" :key="index" class>
                <div class="container">
                  <div class="row">
                    <div style="font-size: 15px;" class="align-self-center px-0 col-2 bold">{{ rolePermission.group }}:</div>

                    <div class="d-flex flex-row flex-wrap col-10 mt-2">
                      <span>{{ rolePermission.permissions.join(',&emsp;') }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </td>
            <td class="align-middle">{{ aRole.description }}</td>
          </tr>
        </tbody>
      </table>

      <div class="d-flex justify-content-end">
        <button class="btn cs-btn cs-btn-submit" v-b-modal.roleCreateModal @click="clearSelectedRoles">Add New Role</button>
      </div>
      <b-modal ref="roleCreateModal" scrollable id="roleCreateModal" size="lg" title="Create New Role" hide-footer>
        <div class="container">
          <div class="row">
            <label for="role-name" class="bold col-2 align-self-center">Name:</label>
            <b-form-input class="col-10" id="role-name" v-model="name" :state="nameState" placeholder="Enter Role Name" trim></b-form-input>
          </div>
          <div class="row mt-3">
            <label for="input-description" class="bold col-2 align-self-center">Description:</label>
            <b-form-input
              class="col-10"
              id="input-description"
              v-model="description"
              :state="descriptionState"
              placeholder="Enter Role Description"
              trim
            ></b-form-input>
          </div>
        </div>
        <b-form-group class="mt-4 ml-3" label="Select Permission:">
          <div id="role-Permissions" class="container h-100">
            <div v-for="(permission, index) in permissions" :key="index">
              <permissionGroup
                :index="index"
                :data="permission"
                :selected="selected"
                :permissions="permissions"
                @addPermission="addPermission"
                @removePermission="removePermission"
                @addAllGroupPermissions="addAllGroupPermissions"
                @removeAllGroupPermissions="removeAllGroupPermissions"
              />
            </div>
          </div>
        </b-form-group>
        <div class="mt-2 float-right">
          <span
            v-tooltip.top="
              isAddRoleButtonDisabled && {
                content: 'Missing fields',
                offset: 20,
              }
            "
          >
            <button
              type="button"
              class="btn cs-btn btn-md"
              :class="[isAddRoleButtonDisabled ? 'cs-btn-inactive' : 'cs-btn-submit ']"
              @click="addNewRow"
              :disabled="isAddRoleButtonDisabled"
            >
              <i class="fas fa-plus-circle"></i>&emsp;Add New Role
            </button>
          </span>
        </div>
      </b-modal>
    </b-modal>
  </div>
</template>

<script>
import _ from 'lodash';
import permissionGroup from './permissions.vue';

export default {
  name: 'NewAddEditRolesDialog',
  components: {
    permissionGroup,
  },
  props: {
    // permission: Object,
    // roles: Array,
    permissions: {
      type: Array,
      default() {
        return [];
      },
    },
    data: {
      type: Object,
      default() {
        return {};
      },
    },
    localData: {
      type: Array,
      default() {
        return [];
      },
    },
    ummsVersion: {
      type: Object,
      default() {
        return {};
      },
    },
    /*
    index: {
      type: Number,
      default() {
        return 0;
      },
    },
    */
  },
  data() {
    return {
      roles: [],
      name: '',
      description: '',
      updatedInfo: {
        name: '',
        description: '',
        permission: [],
      },
      selected: [],
      role: {},
      isDeleteButtonDisabled: false,
      oldDescription: '',
      oldPermission: [],
      testoldPermission: [],
    };
  },

  computed: {
    nameState() {
      return this.name.length > 3;
    },
    descriptionState() {
      return this.description.length > 3;
    },
    isAddRoleButtonDisabled() {
      const nameCheck = this.name.length > 3;
      const descriptionCheck = this.description.length > 3;
      const permissionCheck = this.selected.length > 0;
      if (!nameCheck || !descriptionCheck || !permissionCheck) return true;

      return false;
    },
    hasEditRoleFormChanged() {
      const oldPermissions = [];

      this.oldPermission.forEach(p => {
        p.permissions.forEach(per => {
          oldPermissions.push(`${p.group}_${per}`);
        });
      });

      let changed = false;
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      const samePersmissions = _.isEqual(oldPermissions.sort(), this.selected.sort());
      if (this.oldDescription !== this.updatedInfo.description || !samePersmissions) changed = true;

      return changed;
    },

    updatedDescriptionState() {
      return this.updatedInfo.description.length > 3;
    },
    isUpdatedButtonDisabled() {
      const descriptionCheck = this.updatedInfo.description.length > 3;
      const permissionCheck = this.selected.length > 0;
      if (!descriptionCheck || !permissionCheck) return true;

      return false;
    },
    // isChecked: function() {
    //     return this.$parent.$parent.currentNav == "prod" ? true : false;
    // }
  },
  watch: {},
  mounted() {
    this.clearSelectedRoles();
    this.$store
      .dispatch('user/listRoles', this.ummsVersion)
      .then(response => {
        response.roles.forEach(roleRow => {
          const { name, permission, description } = roleRow;
          this.roles.push({ name, permission, description });
        });
        // console.log("stored roles ... ", this.roles);
      })
      .catch(err => {
        this.noDataTemplateRoles = err.message;
        console.log(err);
      });
  },

  methods: {
    removePermission(permission) {
      this.selected = this.selected.filter(p => p !== permission);
    },
    addPermission(permission) {
      this.selected.push(permission);
    },
    removeAllGroupPermissions(permissionsArr, group) {
      // this.selected = this.selected.filter(p => p != permission);
      // permissionsArr would look like this [ "user_list", "user_invite" ]
      //  each permission would have its own "group as a prefix" followed by "_" and then "permission"
      //  e.g "user_list" , here "user" is group and "list" is permission

      this.selected.forEach(p => {
        const res = p.split('_');

        if (group === res[0]) this.selected = this.selected.filter(per => per !== p);
      });
    },
    addAllGroupPermissions(permissionsArr) {
      // this.selected.push(permission);
      permissionsArr.forEach(p => {
        if (!this.selected.includes(p)) this.selected.push(p);
      });
    },
    clearSelectedRoles() {
      this.description = '';
      this.selected = [];
      this.name = '';
      this.oldPermission = [];
    },
    async addNewRow() {
      try {
        const role = {
          name: this.name,
          permission: this.modifyData(this.selected),
          description: this.description,
          ummsVersion: this.ummsVersion.version,
        };

        // console.log("role", role);
        await this.$store
          .dispatch('user/createRole', role)
          .then(() => {
            this.roles.push(role);
          })
          .catch(err => {
            console.error('Could creating new role', err);
          });
        this.$emit('updateRolesInParent');
        // let roles = await this.$store.dispatch("user/listRoles", ummsVersion);
        // this.tableData = roles.role;
        this.$refs.roleCreateModal.hide();
        this.name = '';
        this.description = '';
        this.selected = [];
      } catch (e) {
        console.error('Could not add new row (for new role)', e);
      }
    },
    async updateRole() {
      try {
        const role = {
          name: this.updatedInfo.name,
          permission: this.modifyData(this.selected),
          description: this.updatedInfo.description,
          ummsVersion: this.ummsVersion.version,
        };

        await this.$store
          .dispatch('user/updateRole', role)
          .then(() => {
            this.roles = this.roles.map(mapRole => {
              if (mapRole.name === this.updatedInfo.name) {
                // eslint-disable-next-line no-param-reassign
                mapRole.permission = this.modifyData(this.selected);
                // eslint-disable-next-line no-param-reassign
                mapRole.description = this.updatedInfo.description;
              }
              return mapRole;
            });
            this.$emit('updateRolesInParent');
          })
          .catch(err => {
            console.error('Problem updating role :/', err);
          });

        this.$refs.roleUpdateModal.hide();
      } catch (e) {
        console.error('Unable to update role', e);
      }
    },
    async deleteRole() {
      try {
        const role = {
          name: this.updatedInfo.name,
          permission: this.selected,
          description: this.updatedInfo.description,
        };
        await this.$store
          .dispatch('user/deleteRole', role)
          .then(() => {
            this.roles = this.roles.filter(ele => {
              return ele.name !== role.name;
            });
            this.$emit('updateRolesInParent');
          })
          .catch(err => {
            console.error('Problem deleting role :/', err);
          });
        this.$refs.roleUpdateModal.hide();
      } catch (e) {
        console.error('e', e);
      }
    },
    onRowClick(row) {
      if (row.name === 'admin') return;
      this.role = row;
      console.log('row', row);
      this.updatedInfo.name = row.name;
      this.updatedInfo.description = row.description;
      // this.updatedInfo.permission = row.permission;

      const arr = [];
      row.permission.forEach(p => {
        p.permissions.forEach(per => {
          arr.push(`${p.group}_${per}`);
        });
      });
      this.oldPermission = row.permission;
      this.selected = arr;

      this.oldDescription = row.description;

      //   console.log("this.updatedInfo", this.updatedInfo);
      const isRoleUsed = this.localData.some(ele => ele.role[0] === row.name);
      // console.log('isRoleUsed', isRoleUsed)
      this.isDeleteButtonDisabled = isRoleUsed;
      this.$refs.roleUpdateModal.show();
    },
    modifyData(strArr) {
      // ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
      //
      // strArr would like like this ["user_list", "user_update"]
      //  and we want to make it like this                            ||
      //          [                                                   ||
      //               {                                              ||
      //                  group: "user",                              ||
      //                  permissions:  ["list", "update"]            ||
      //               },                                             ||
      //               {                                              ||
      //                  group: "user",                              ||
      //                  permissions:  ["list", "update"]            ||
      //               },                                             ||
      //          ]
      // ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||

      const obj = {}; // to strore group and its related permissions as key value pairs like this {"user": ["invite", "create"]}

      strArr.forEach(p => {
        const arr = p.split('_');
        const group = arr[0];
        const permission = arr[1];

        // if group doesn't exsit in obj then it will create a new keyvalue pair
        //  where : key being "group" value being "permission" else it will modify the exisiting array
        if (!obj[group]) obj[group] = [permission];
        else obj[group].push(permission);
      });

      const permissionArr = [];

      for (const key in obj) {
        permissionArr.push({
          group: key,
          permissions: obj[key],
        });
      }
      return permissionArr;
    },
  },
};
</script>

<style scoped></style>
