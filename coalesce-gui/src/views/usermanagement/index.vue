<!-- eslint-disable vue/no-unused-vars -->
<template>
  <div>
    <div class="management-container m-3 d-flex flex-column">
      <div class="d-flex flex-row justify-content-between">
        <h1>User Management</h1>
        <!--div class="mt-2">
          <button class="btn cs-btn cs-btn-submit py-1 px-2" v-b-modal.roleModal>Add/Edit Roles</button>
        </div-->
      </div>
      <vuetable
        :no-data-template="noDataTemplate"
        ref="vuetable"
        :api-mode="false"
        :data="localData"
        :fields="fields"
        :css="css.table"
        :show-sort-icons="true"
        :per-page="10"
        pagination-path
      ></vuetable>
      <b-modal class="modal fade" size="md" id="userConfirmDeleteModal" ref="userConfirmDeleteModal" centered hide-footer>
        <template v-slot:modal-header="{ hide }">
          <div class="d-flex flex-column w-100 text-center">
            <h2>
              <label class="my-2 bold">Please Confirm.</label>
              <br />
              <span class="text-danger">Deleting</span>
              <i> {{ userEditModal.first_name }} {{ userEditModal.last_name }}</i>
              <br />
              <small>({{ userEditModal.email }})</small>
            </h2>
          </div>
        </template>
        <b-form class="d-flex flex-column" @submit.prevent="saveChanges">
          <div class="d-flex flex-column align-items-center justify-content-center w-100 pb-2">
            <div class="py-2">
              <button
                id="editModal_actionButton"
                type="button"
                class="m-2 btn cs-btn"
                :class="classes.userEditModal.actionButton"
                @click="modalConfirmDelete"
              >
                <!-- eslint-disable-next-line vue/no-v-html -->
                <span v-html="userEditModal_ActionText"></span>&nbsp;&nbsp;
                <i class="fas fa-times"></i>
              </button>
              <button class="btn cs-btn cs-btn-secondary" block @click="$bvModal.hide('userConfirmDeleteModal')">
                Cancel
              </button>
            </div>
          </div>
        </b-form>
      </b-modal>

      <b-modal class="modal fade" size="md" id="userEditModal" ref="userEditModal" centered hide-footer>
        <template v-slot:modal-header="{ hide }">
          <div class="d-flex flex-column w-100 text-center">
            <h2>
              Editing User -
              <strong>{{ userEditModal.first_name }} {{ userEditModal.last_name }}</strong>
            </h2>
          </div>
        </template>
        <b-form class="d-flex flex-column" @submit.prevent="saveChanges">
          <div class="d-flex flex-column align-items-center justify-content-center w-100 pb-2">
            <div id="userEditForm" class="d-flex flex-row justify-content-center w-100">
              <b-form-group label="Assigned Role">
                <div v-for="(role, idx) in roles" :key="idx">
                  <b-form-radio v-model="userEditModal.account_type" name="some-radios" :value="role.name">
                    {{ role.name.replace(/^\w/, c => c.toUpperCase()) }}
                  </b-form-radio>
                </div>
              </b-form-group>
            </div>
            <div class="py-2">
              <button
                id="editModal_actionButton"
                type="button"
                class="m-2 btn cs-btn"
                :class="classes.userEditModal.actionButton"
                v-on="
                  userEditModal.state == 'Inactive'
                    ? { click: $event => $bvModal.show('userConfirmDeleteModal') }
                    : { click: $event => changeUserAccess('Active') }
                "
              >
                <!-- eslint-disable-next-line vue/no-v-html -->
                <span v-html="userEditModal_ActionText"></span>&nbsp;&nbsp;
                <i class="fas fa-times"></i>
              </button>
              <button
                v-if="userEditModal.state != 'Inactive'"
                type="submit"
                class="m-2 cs-btn btn"
                :class="classes.userEditModal.submitButton"
                :disabled="classes.userEditModal.submitButton == 'cs-btn-inactive'"
                v-tooltip.right="{
                  classes: tooltips.userEditSubmit.type,
                  content: tooltips.userEditSubmit.content,
                  trigger: 'manual',
                  offset: 10,
                  show: tooltips.userEditSubmit.visible,
                }"
              >
                Save&nbsp;
                <i class="far fa-save"></i>
              </button>
            </div>
          </div>
        </b-form>
      </b-modal>
      <newAddEditRolesDialog
        v-if="userManagement.version == 'new'"
        :umms-version="userManagement"
        :local-data="localData"
        :permissions="permissions"
        @updateRolesInParent="getAllRoles"
      />
      <oldAddEditRolesDialog
        v-if="userManagement.version == 'old'"
        :umms-version="userManagement"
        :local-data="localData"
        :permissions="permissions"
        @updateRolesInParent="getAllRoles"
      />
    </div>
  </div>
</template>

<script>
/* eslint-disable vue/no-side-effects-in-computed-properties */
/* eslint-disable import/no-unresolved */
import PermissionManager from '@/utils/PermissionManager';
import { hideTooltip, showTooltip } from '@/utils/tooltip-alerts';
import { mapGetters } from 'vuex';
import { Vuetable } from 'vuetable-2';

import CssConfig from './VuetableBootstrapConfig'; // https://codesandbox.io/s/90qx4488w
import VuetableFieldButton from './VuetableFieldButton.vue';
import newAddEditRolesDialog from './newAddEditRolesDialog.vue';
import oldAddEditRolesDialog from './oldAddEditRolesDialog.vue';

export default {
  name: 'UserManagement',
  components: {
    Vuetable,
    newAddEditRolesDialog,
    oldAddEditRolesDialog,
  },
  data() {
    return {
      userManagement: { version: '' },
      allSelected: false,
      indeterminate: false,
      noDataTemplate: 'No Data Available',
      noDataTemplateRoles: 'No Data Available',
      roles: [],

      permissions: [],

      classes: {
        userEditModal: {
          submitButton: 'cs-btn-submit',
          actionButton: '',
        },
      },
      userEditModal: {
        state: '',
        first_name: '<first-name-here>',
        last_name: '<last-name-here>',
        account_type: '',
        email: '',
      },
      tooltips: {
        userEditSubmit: {
          classes: '',
          visible: false,
          timeout: null,
          type: '',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
      },

      tableData: null,
      fields: [
        {
          name: 'name',
          title: 'Full Name',
          sortField: 'name',
          titleClass: 'cs-table-title pre-title',
          dataClass: 'cs-table-row row-text-pre',
        },
        {
          name: 'email',
          title: 'Email',
          sortField: 'email',
          titleClass: 'cs-table-title pre-title',
        },
        {
          name: 'state',
          title: 'Status',
          sortField: 'status',
          titleClass: 'cs-table-title pre-title',
          formatter: value => {
            let cssClass;
            if (value.match(/inactive/i)) cssClass = 'text-danger';
            else if (value.match(/invited/i)) cssClass = 'text-primary';
            else if (value.match(/active/i)) cssClass = 'cs-text-primary';
            else cssClass = 'cs-text-warning';

            return `<span class="${cssClass}">${value}</span>`;
          },
        },
        {
          name: 'role',
          title: 'Roles',
          titleClass: 'center aligned cs-table-title normal-title',
          dataClass: 'left aligned',
          // callback: "showPermissions"
          formatter: value => {
            return value[0];
          },
        },
        {
          name: VuetableFieldButton,
          title: '&nbsp;',
          titleClass: 'center aligned cs-table-title normal-title',
          dataClass: 'left aligned',
          // callback: "showPermissions"
          formatter: value => {
            return value;
          },
        },
      ],
      sortOrder: [{ field: 'username', direction: 'asc' }],
      css: CssConfig,
      localData: [],
    };
  },
  computed: {
    ...mapGetters(['userPermissions']),
    pm() {
      if (!this.userPermissions) return undefined;

      const pm = new PermissionManager(this.userPermissions);
      return pm;
    },
    userEditModal_ActionText() {
      switch (this.userEditModal.state) {
        case 'Locked':
          this.classes.userEditModal.actionButton = 'cs-btn-warning';
          return 'Unlock User';
        case 'Invited':
          this.classes.userEditModal.actionButton = 'cs-btn-danger';
          return 'Delete User';
        case 'Inactive':
          this.classes.userEditModal.actionButton = 'cs-btn-info';
          return 'Activate User';
        case 'Active':
          this.classes.userEditModal.actionButton = 'cs-btn-outline-warning';
          return 'Deactivate';
        default:
          this.classes.userEditModal.actionButton = 'cs-btn-warning';
          return 'Unknown-State';
      }
    },
  },
  mounted() {
    // ---- get umms version
    this.$store
      .dispatch('user/getUmmsVersion')
      .then(async response => {
        if (response && response.version && response.version.major === 1) this.userManagement.version = 'new';
        else if (response && response.version && response.message === 'failed') this.userManagement.version = 'old';

        await this.getAllRoles();
        await this.getAllPermissions();
        // ---- collect users to populate table
        this.$store
          .dispatch('user/listUsers')
          .then(listUsersResponse => {
            // ---- Make the permission available in the table data
            // ---- that way we can assign the "BUTTON" state in the vue component for the button by checking this permission state :)
            const hasPermission = this.pm && this.pm.hasPermission({ can: 'update', a: 'user' });

            listUsersResponse.users.map(user => {
              // eslint-disable-next-line no-param-reassign
              user.hasPermission = hasPermission;
              return user;
            });

            this.localData = listUsersResponse.users;
            this.localData.map(v => {
              // eslint-disable-next-line no-param-reassign
              v.name = `${v.firstname} ${v.lastname}`;
              return v;
            });
            console.debug('listUsers() localData -> ', this.localData);
          })
          .catch(err => {
            this.noDataTemplate = err.message;
            console.error('listUsers() failed in catch -> ', err);
          });
      })
      .catch(err => {
        console.error('getUmmsVersion err in GUI mounted() -> ', err);
        this.userManagement.version = 'old';
      });

    // ---- collect roles to populate modal for user editing
  },
  methods: {
    async getAllRoles() {
      try {
        const response = await this.$store.dispatch('user/listRoles', this.userManagement);
        this.roles = response.roles;
      } catch (e) {
        console.error('async getAllRoles() failed -> ', e);
      }
    },
    async getAllPermissions() {
      try {
        const response = await this.$store.dispatch('user/listPermissions', this.userManagement);
        if (this.userManagement.version === 'new') this.permissions = response.permissionGroup;
        else this.permissions = response.permissions;
      } catch (e) {
        console.error('async getAllPermissions() failed -> ', e);
      }
    },
    listUsers() {
      this.$store
        .dispatch('user/listUsers')
        .then(response => {
          this.localData = response.users;
          this.localData.map(v => {
            // eslint-disable-next-line no-param-reassign
            v.name = `${v.firstname} ${v.lastname}`;
            return v;
          });
        })
        .catch(err => {
          this.noDataTemplate = err.message;
          console.log(err);
        });
    },
    async changeState(state) {
      const user = {
        email: this.userEditModal.email,
        state,
      };

      // ---- If we are "re-activating" a user (locked or inactive isn't really relevant)....
      //      then we must reset their "attempts" to 0...
      this.localData.forEach(rowUser => {
        if (rowUser.email === this.userEditModal.email && state === 'Active') {
          user.info = rowUser.info;
          user.info.attempts = 0;
        }
      });

      const updatePromise = this.$store
        .dispatch('user/updateUser', user)
        .then(response => {
          for (let i = 0; i < this.localData.length; i++) {
            if (this.localData[i].email === user.email) {
              // ---- set the new state first
              this.localData[i].state = state;

              // ---- update the 'editable' nature of the FieldButton MIXIN
              this.$refs.vuetable.$children.map(child => {
                if (child.rowData && child.rowData.email) {
                  if (child.rowData.email === user.email) {
                    Object.keys(child.buttonStates).forEach(key => {
                      // eslint-disable-next-line no-param-reassign
                      child.buttonStates[key] = false;
                    });
                    switch (state) {
                      case 'Active':
                        // eslint-disable-next-line no-param-reassign
                        child.buttonStates.canEdit = true;
                        break;
                      default:
                        // eslint-disable-next-line no-param-reassign
                        child.buttonStates.canActivate = true;
                        break;
                    }
                  }
                }
                return child;
              });
            }
          }
          console.log('response', response);
          return true;
        })
        .catch(err => {
          console.error('Unable to change user state :/', err);
          return false;
        });

      this.$bvModal.hide('userEditModal');

      return updatePromise;
    },
    async saveChanges() {
      hideTooltip(this.tooltips.userEditSubmit);
      const user = {
        email: this.userEditModal.email,
        role: [this.userEditModal.account_type],
      };
      this.$store
        .dispatch('user/updateUser', user)
        .then(() => {
          for (let i = 0; i < this.localData.length; i++) {
            if (this.localData[i].email === user.email) this.localData[i].role = [this.userEditModal.account_type];
          }
          this.$bvModal.hide('userEditModal');
        })
        .catch(err => {
          if (err.message.match(/403/)) {
            showTooltip(
              this.tooltips.userEditSubmit,
              'cs-error',
              'You do not have permissions to perform <span class="nowrap">this action</span>',
              5,
            );
          }
          console.log(err);
        });
    },
    async changeUserAccess() {
      if (this.userEditModal.state === 'Active') {
        await this.changeState('Inactive');
      } else if (this.userEditModal.state === 'Invited') {
        this.$bvModal.show('userConfirmDeleteModal');
      } else {
        await this.changeState('Active');
      }
    },
    modalConfirmDelete() {
      this.deleteUser()
        .then(() => {
          this.$bvModal.hide('userConfirmDeleteModal');
        })
        .catch(err => {
          console.error('unable to delete :/ please contact the administrator', err);
        });
    },
    async deleteUser() {
      const user = {
        email: this.userEditModal.email,
      };
      this.$store
        .dispatch('user/deleteUser', user)
        .then(response => {
          const newLocalData = [];
          for (let i = 0; i < this.localData.length; i++) {
            if (this.localData[i].email !== user.email) newLocalData.push(this.localData[i]);
          }
          this.localData = newLocalData;
          console.log('deleteUser', response);
        })
        .catch(err => {
          console.error('Could not delete user :/', err);
        });

      this.$bvModal.hide('userEditModal');
    },
    // ---- triggered by child -> VuetableFieldButton.vue
    async activateUser(userEmail) {
      this.userEditModal.email = userEmail;
      const stateChanged = await this.changeState('Active');
      return stateChanged;
    },
    toggleLaunchModal(userInfo) {
      hideTooltip(this.tooltips.userEditSubmit);

      console.log('userInfo for modal:\n', userInfo);

      const [userRole] = userInfo.role;

      this.userEditModal.account_type = userRole;
      this.userEditModal.first_name = userInfo.firstname;
      this.userEditModal.last_name = userInfo.lastname;
      this.userEditModal.state = userInfo.state;
      this.userEditModal.email = userInfo.email;

      if (!this.$refs.userEditModal.isShow) this.$refs.userEditModal.show();
      else this.$refs.userEditModal.hide();
    },

    showPermissions(data) {
      return data[0];
    },
  },
};
</script>

<style lang="scss" scoped>
.VueTables__date-filter {
  border: 1px solid #ccc;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
}
</style>
<style lang="scss">
/* Importing for the purpose of variables --- this can probably be separated and handled better ... i.e. split variables off */
@import '../../styles/cs-styles.scss';

.vuetable {
  margin-top: 5em;
}

.table th {
  padding-top: 0.5em;
  padding-bottom: 0.5em;
}
/* Force "TH" because it otherwise styles the entire column :( ?? */
[class*='vuetable-td-'].cs-table-row {
  .row-text-pre {
    color: $cs_color_info;
  }
}
[class*='vuetable-th-'].cs-table-title {
  .sort-icon {
    float: right;
    padding-right: 0.4em;
    padding-top: 0.2em;
    padding-bottom: 0.1em;
    margin-left: 0.2em;
  }
  background: #eee;
  color: #666;
}
</style>
