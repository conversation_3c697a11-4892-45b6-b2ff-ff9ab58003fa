<template>
  <!-- eslint-disable-next-line vue/no-v-html -->
  <th v-if="isHeader" class="vuetable-th-component-button" v-html="title"></th>
  <td v-else class="vuetable-td-component-button">
    <div class="d-flex flex-row user-manage-buttons justify-content-center">
      <span v-if="buttonStates.canUnlock" @click="activateUser($event)" class="badge badge-warning px-2 cursor-pointer">
        <i class="fas fa-unlock-alt"></i>&nbsp;Unlock
      </span>
      <span v-if="buttonStates.canActivate" @click="activateUser($event)" class="badge badge-info px-2 cursor-pointer">
        <i class="far fa-user"></i>&nbsp;Activate
      </span>
      <span v-if="buttonStates.canEdit" @click="editUser($event)" class="badge badge-light px-2 cursor-pointer">
        <i class="fas fa-edit"></i>&nbsp;Edit
      </span>
      <span
        v-if="buttonStates.noPermission"
        class="badge badge-light px-2 cursor-default"
        v-tooltip="{ delay: { show: 200, hide: 100 }, content: 'No permission to edit' }"
      >
        <i class="fas fa-ban"></i>&nbsp;
      </span>
      <span
        v-if="!Object.values(buttonStates).includes(true)"
        class="badge badge-light px-2 cursor-normal"
        v-tooltip="{ delay: { show: 200, hide: 100 }, content: 'Cannot edit self' }"
      >
        <i class="fas fa-user-lock" style="font-size:1.5em"></i>
      </span>
    </div>
  </td>
</template>

<script>
// eslint-disable-next-line import/no-unresolved
import VuetableFieldMixin from 'vuetable-2/src/components/VuetableFieldMixin';

export default {
  name: 'VuetableFieldButton',
  mixins: [VuetableFieldMixin],
  data() {
    return {
      buttonStates: {
        canUnlock: false,
        canActivate: false,
        canEdit: false,
        noPermission: false,
      },
      prodState: '',
      buttonDisabled: true,
      buttonText: 'Launch',
      buttonClass: 'cs-btn-outline-inactive',
    };
  },
  mounted() {
    if (!this.$options.propsData.rowData) return;

    const { rowData } = this.$options.propsData;

    this.setButtonState(rowData.email, rowData.hasPermission ? rowData.state : 'noPermission');

    console.debug('VuetableFieldButtom mounted() rowData -> ', rowData);
  },
  methods: {
    setButtonState(rowEmail, rowState) {
      if (rowEmail !== this.$store.state.user.user.email) {
        switch (rowState) {
          case 'noPermission':
            this.buttonStates.noPermission = true;
            break;
          case 'Inactive':
            this.buttonStates.canActivate = true;
            break;
          case 'Locked':
            this.buttonStates.canUnlock = true;
            break;
          default:
            this.buttonStates.canEdit = true;
        }
      }
    },
    activateUser() {
      // debugger
      console.debug('clicked "unlock" OR "activate" ...');
      const successPromise = this.$parent.$parent.activateUser(this.$options.propsData.rowData.email);
      successPromise.then(() => {
        // ---- force all states to false... and set THIS state to true
        Object.keys(this.buttonStates).forEach(key => {
          this.buttonStates[key] = false;
        });
        this.buttonStates.canEdit = true;
      });
    },
    editUser() {
      // debugger
      console.debug('clicked "edit" ...');
      this.$parent.$parent.toggleLaunchModal(this.$options.propsData.rowData);
    },
  },
};
</script>

<style lang="scss" scoped>
.cursor-default {
  cursor: default;
}
</style>
