<template>
  <div>
    <b-form-checkbox style="display: inline;" v-model="allSelected" :indeterminate="indeterminate" @change="toggleAll($event, data)">
    </b-form-checkbox>
    <span v-b-toggle="`collapse-${index}`" class="m-1" style="cursor: pointer;">
      <span style="font-size: 13px; color: #686868;"> ({{ selectedPermissions.length }}/{{ data.permissions.length }}) </span>
      <span class="ml-2"> {{ data.group }} </span>

      <b-icon v-if="!shown" icon="chevron-down"></b-icon>
      <b-icon v-if="shown" icon="chevron-up"></b-icon>
    </span>

    <b-collapse :id="`collapse-${index}`" @show="showCollapse" @hide="hideCollapse">
      <div style="background-color: #f7f9fa; border: 1px solid #e0dbdb;" class="row mb-2">
        <div class="col-12">
          <div>
            <div class="mr-4 mb-3 pt-3">
              <b-form-checkbox-group id="checkbox-group-2" v-model="selectedPermissions" size="md" class="ml-4">
                <div v-for="(permission, idx) in data.permissions" :key="idx">
                  <checkboxRow @onCheckboxStateChange="onCheckboxStateChange" :data="data" :permission="permission" />
                </div>
              </b-form-checkbox-group>
            </div>
          </div>
        </div>
      </div>
    </b-collapse>
  </div>
</template>

<script>
import checkboxRow from './checkbox.vue';

export default {
  name: 'PermissionGroup',
  components: {
    checkboxRow,
  },
  props: {
    permissionsData: {
      type: Array,
      default() {
        return [];
      },
    },
    selected: {
      type: Array,
      default() {
        return [];
      },
    },
    data: {
      type: Object,
      default() {
        return {};
      },
    },
    index: {
      type: Number,
      default() {
        return 0;
      },
    },
    role: {
      type: Object,
      default() {
        return { permission: [] }; // empty permissions for when 'creating' new roles
      },
    },
  },
  data() {
    return {
      allSelected: false,
      indeterminate: false,
      selectedPermissions: [],
      shown: false,
    };
  },
  watch: {
    selectedPermissions(newVal) {
      // Handle changes in individual flavour checkboxes
      if (newVal.length === 0) {
        this.indeterminate = false;
        this.allSelected = false;
      } else if (newVal.length === this.data.permissions.length) {
        this.indeterminate = false;
        this.allSelected = true;
      } else {
        this.indeterminate = true;
        this.allSelected = false;
      }
    },
  },
  mounted() {
    if (this.role) {
      const arr = [];
      this.role.permission.forEach(p => {
        if (p.group === this.data.group) {
          // console.log("p", p);
          p.permissions.forEach(per => {
            arr.push(`${p.group}_${per}`);
          });
        }

        this.selectedPermissions = arr;
      });
    }
  },

  methods: {
    showCollapse() {
      this.shown = true;
    },
    hideCollapse() {
      this.shown = false;
    },
    onCheckboxStateChange(checked, permission) {
      if (checked) this.$emit('addPermission', permission);
      else this.$emit('removePermission', permission);
    },
    toggleAll(checked, data) {
      console.log('checked', checked);
      // console.log("checked", data);

      const arr = [];
      data.permissions.forEach(p => {
        const value = `${data.group}_${p.name}`;
        if (!this.selectedPermissions.includes(value)) arr.push(value);
      });

      this.selectedPermissions = checked ? [...this.selectedPermissions, ...arr] : [];
      console.log('selectedPermissions', this.selectedPermissions);

      if (!this.selectedPermissions.length) this.$emit('removeAllGroupPermissions', this.selectedPermissions, this.data.group);
      else this.$emit('addAllGroupPermissions', this.selectedPermissions, this.data.group);
    },
  },
};
</script>

<style scoped></style>
