<template>
  <div class="row">
    <div class="col-4">
      <b-form-checkbox @change="onCheckboxStateChange($event, `${data.group}_${permission.name}`)" :value="`${data.group}_${permission.name}`">
        {{ permission.name }}
      </b-form-checkbox>
    </div>
    <div class="col-8">{{ permission.description }}</div>
  </div>
</template>

<script>
export default {
  name: 'CheckboxRow',
  props: {
    permission: {
      type: Object,
      default() {
        return {};
      },
    },
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  methods: {
    onCheckboxStateChange(checked, permission) {
      this.$emit('onCheckboxStateChange', checked, permission);
    },
  },
};
</script>

<style scoped></style>
