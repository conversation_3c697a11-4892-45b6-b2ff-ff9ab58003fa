<template>
  <div class="container-fluid">
    <transition name="fade-transform" v-if="!isMounted">
      <div class="m-5 pt-4" style="position:absolute;">
        <h3 class="d-flex align-items-center mt-5">
          <font-awesome-icon icon="circle-notch" spin class="mr-3" style="font-size:115%"></font-awesome-icon>
          <small>Loading endpoint...</small>
        </h3>
      </div>
    </transition>
    <div v-show="isMounted" :class="{ 'pt-4': editMode, 'pt-3': !editMode }">
      <div class="endpoint-title row">
        <div class="col-sm-12 d-flex flex-column">
          <h4 v-if="!editMode" class="d-flex">
            <i class="fas fa-plug mr-2" /><span class="cs-text-primary">New {{ connector }} Endpoint</span>
          </h4>
          <h4 v-else class="d-flex">
            <i class="fas fa-plug mr-2" />&emsp;Edit Endpoint Connection Pool -
            <span
              class="cs-text-primary d-flex"
              v-tooltip.right="{
                classes: tooltips.newlyCreated.type,
                content: tooltips.newlyCreated.content,
                trigger: 'manual',
                offset: 20,
                show: tooltips.newlyCreated.visible,
              }"
            >
              <span class="mx-2">{{ endpoint.name }}</span>
              <span v-show="true" class="badge badge-dark text-warning">{{ connector }}</span>
            </span>
          </h4>
        </div>
      </div>
      <!-----------------------------    Alert Modal    -------------------------------->

      <b-modal
        @shown="onAlertModalShown()"
        @hide="onAlertModalHide()"
        class="modal fade"
        size="lg"
        id="alertModal"
        ref="alertModal"
        centered
        title="Important Notice"
        hide-footer
        header-bg-variant="light"
        header-text-variant="dark"
        body-bg-variant="light"
        body-text-variant="dark"
      >
        <div class="p-0" style="margin-left: -15px; margin-right: -15px; margin-top:-15px;">
          <div class="d-flex flex-column">
            <div class="d-flex flex-column">
              <!--div class="my-2 bold" v-if="!endpointProdHasCompleted">Primary Notice</div-->
              <div class="alert alert-warning border border-0 rounded-0">
                <div v-if="editMode">
                  <div class="mb-3">
                    You have made changes
                    <span class="nowrap"
                      >to the '<strong>{{ endpoint.name }}</strong
                      >' endpoint.</span
                    >
                  </div>
                  <div class="mb-3">
                    All associated Pre-Production and <strong>Production</strong> services will be updated
                    <span class="nowrap">with <i>immediate</i> effect.</span>
                  </div>
                  <div class="text-danger">
                    Please only proceed if you are absolutely sure!
                  </div>
                </div>
                <div v-if="!endpointProdHasCompleted" :class="editMode ? 'pt-5' : ''">
                  <h6 class="pb-2" v-if="editMode"><i>Additionally...</i></h6>
                  <div class="mb-3">
                    This operation will {{ editMode ? 'update' : 'create' }} the '<strong>{{ endpoint.name }}</strong
                    >' endpoint with <strong class="text-danger">missing</strong> Production <span class="nowrap">environment information</span>.
                  </div>
                  <div>
                    To fill in the Production environment information, <br />
                    choose <strong>Cancel</strong> and go to the <strong>Production</strong> tab and configure those settings.
                  </div>
                </div>
              </div>
            </div>

            <div class="d-flex mt-3 justify-content-end px-3">
              <div class="pb-3 ">
                <button
                  type="button"
                  class="btn cs-btn cs-btn-secondary mx-2 py-1"
                  @click="$refs.alertModal.hide()"
                  v-tooltip.left="{
                    classes: tooltips.saveEndpointModalButton.type,
                    content: tooltips.saveEndpointModalButton.content,
                    trigger: 'manual',
                    offset: 20,
                    show: tooltips.saveEndpointModalButton.visible,
                  }"
                >
                  <i class="fas fa-times"></i>
                  &nbsp;Cancel
                </button>

                <button
                  type="button"
                  class="btn cs-btn py-1"
                  @click="editMode ? onUpdate() : onCreate()"
                  :disabled="!isFormValid"
                  :class="[isFormValid ? 'cs-btn-warning' : 'cs-btn-inactive']"
                >
                  Save Endpoint&emsp;<i class="ml-2" :class="saving ? 'fas fa-circle-notch fa-spin' : 'far fa-save'"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </b-modal>
      <!-----------------------------    Name field    -------------------------------->

      <div class="row mb-2" v-if="!editMode">
        <div class="col-sm-2 align-self-center pb-3 text-right" for="name">
          Name
          <required-asterisk />
        </div>
        <div class="col-sm-8">
          <input
            id="name"
            v-model.trim="endpoint.name"
            type="text"
            :disabled="editMode"
            class="form-control description mb-0"
            :placeholder="`${connector} Endpoint Name`"
            @blur="$v.endpoint.name.$touch()"
            @input="validName()"
          />
          <div class="d-flex align-items-center">
            <div>
              <small class="text-secondary ml-1 mb-2">
                <strong>Note:</strong> Name must be alphanumeric, cannot begin with a number, can contain underscores (<i>has automatic validation</i
                >)</small
              >
            </div>
          </div>
          <div v-if="$v.endpoint.name.$error && !$v.endpoint.name.required" class="error-messages">
            Name is required
          </div>
        </div>
        <!-- {{ $v.endpoint.name }} -->
      </div>

      <!-----------------------------    Description field    -------------------------------->

      <div class="row mb-4">
        <div class="col-sm-2 align-self-center pb-3 text-right" for="description">
          Description
          <required-asterisk />
        </div>
        <div class="col-sm-8 input-fields-height">
          <input
            id="description"
            v-model.trim="endpoint.description"
            type="text"
            class="form-control description mb-0"
            :placeholder="`${connector} Endpoint Description`"
            @blur="$v.endpoint.description.$touch()"
          />
          <div v-if="$v.endpoint.description.$error && !$v.endpoint.description.required" class="error-messages">
            Description is required
          </div>
        </div>
      </div>
      <!-----------------------------    Tabs   -------------------------------->

      <div class="endpoint-form row">
        <div class="col-sm-12">
          <b-modal size="md" variant="warning" id="ucip-endpoint-delete" hide-footer hide-header centered>
            <div class="text-center py-3">
              <div class="mb-3">
                Delete <strong>Connection {{ endpointDeletionObject.index + 1 }}</strong> from
                <strong>{{ endpointDeletionObject.environment === 'preprod' ? 'Pre-Production' : 'Production' }}</strong>
              </div>
              <div>
                <button class="btn btn-outline-secondary mr-3" @click="$bvModal.hide('ucip-endpoint-delete')">
                  Cancel
                </button>
                <button class="btn cs-btn cs-btn-danger" @click="deleteAssociatedEndpointConnection()">
                  Delete!
                </button>
              </div>
            </div>
          </b-modal>
          <b-tabs v-model="tabIndex" content-class="mt-3" fill>
            <!-- <div class="col-sm-10 input-fields-height"> -->
            <b-tab v-for="(state, idx) in ['preprod', 'prod']" :key="idx" title-item-class="endpoints-tab-shrink">
              <template v-slot:title>
                <div>{{ state === 'prod' ? 'Production' : 'Pre-Production' }}</div>
              </template>
              <span v-if="connector === 'waiting'">
                &nbsp;
              </span>

              <!-----------------------------    HuX   -------------------------------->

              <div v-else-if="connector == 'hux'">
                <div v-for="(ep, epIndex) in endpoint[state]" :key="epIndex">
                  <hux-endpoint-form
                    :edit-mode="editMode"
                    :is-prod="state === 'prod'"
                    :endpoint-index="epIndex"
                    @formChanged="onFormChange"
                    :ref="`endpoint${state === 'prod' ? 'Prod' : 'Preprod'}`"
                    :endpoint-data="ep"
                  ></hux-endpoint-form>
                </div>
              </div>

              <!-----------------------------    UCIP   ------------------------------->

              <div v-else-if="connector == 'ucip'" role="tablist">
                <b-nav tabs content-class="mt-3 ucip-tabs" class="px-4" style="font-size:95%;">
                  <b-nav-item v-for="(ep, epIndex) in endpoint[state]" :key="epIndex" :active="shownConnection[state] === epIndex">
                    <div @click.prevent="shownConnection[state] = epIndex">
                      Connection {{ epIndex + 1 }}
                      <font-awesome-icon
                        v-if="epIndex !== 0"
                        v-tooltip="{
                          content: !canUpdateAnEndpoint ? 'Insufficient permissions to delete' : hasChanged ? 'Please save first' : '',
                          offset: 5,
                        }"
                        @click.prevent="
                          endpointDeletionObject = { index: epIndex, environment: state };
                          !canUpdateAnEndpoint || hasChanged ? '' : $bvModal.show('ucip-endpoint-delete');
                        "
                        style="outline:none;"
                        icon="trash-alt"
                        class="ml-2"
                        :class="!canUpdateAnEndpoint || hasChanged ? 'text-secondary' : 'text-danger'"
                      ></font-awesome-icon>
                    </div>
                  </b-nav-item>
                  <b-nav-item v-if="endpoint[state].length <= 9">
                    <div
                      @click="
                        endpoint[state].push({ timeout: 1000 });
                        shownConnection[state] = endpoint[state].length - 1;
                      "
                    >
                      New <font-awesome-icon icon="plus" class="ml-1 mt-1"></font-awesome-icon>
                    </div>
                  </b-nav-item>
                </b-nav>
                <div v-for="(ep, epIndex) in endpoint[state]" :key="epIndex">
                  <transition name="fade">
                    <div v-show="shownConnection[state] === epIndex">
                      <ucip-endpoint-form
                        :edit-mode="editMode"
                        :uid="epIndex"
                        :is-prod="state === 'prod'"
                        :endpoint-index="epIndex"
                        @formChanged="onFormChange"
                        :ref="`endpoint${state === 'prod' ? 'Prod' : 'Preprod'}`"
                        :endpoint-data="ep"
                      ></ucip-endpoint-form>
                    </div>
                  </transition>
                </div>
              </div>

              <!-----------------------------    CAI3G   ------------------------------->

              <div v-else-if="connector == 'cai3g'">
                <div v-for="(ep, epIndex) in endpoint[state]" :key="epIndex">
                  <cai3g-endpoint-form
                    :edit-mode="editMode"
                    :is-prod="state === 'prod'"
                    :endpoint-index="epIndex"
                    @formChanged="onFormChange"
                    :ref="`endpoint${state === 'prod' ? 'Prod' : 'Preprod'}`"
                    :endpoint-data="ep"
                  ></cai3g-endpoint-form>
                </div>
              </div>
              <!-----------------------------    MobileMoney   ------------------------------->

              <div v-else-if="connector == 'mobileMoney'">
                <div v-for="(ep, epIndex) in endpoint[state]" :key="epIndex">
                  <mobileMoneyEndpointForm
                    :edit-mode="editMode"
                    :is-prod="state === 'prod'"
                    :endpoint-index="epIndex"
                    @formChanged="onFormChange"
                    @formUpdated="onFormUpdate"
                    :ref="`endpoint${state === 'prod' ? 'Prod' : 'Preprod'}`"
                    :endpoint-data="ep"
                  ></mobileMoneyEndpointForm>
                </div>
              </div>

              <!-----------------------------    Crediverse   ------------------------------->

              <div v-else-if="connector == 'crediverse'">
                <div v-for="(ep, epIndex) in endpoint[state]" :key="epIndex">
                  <crediverseEndpointForm
                    :edit-mode="editMode"
                    :is-prod="state === 'prod'"
                    :endpoint-index="epIndex"
                    @formChanged="onFormChange"
                    :ref="`endpoint${state === 'prod' ? 'Prod' : 'Preprod'}`"
                    :endpoint-data="ep"
                  ></crediverseEndpointForm>
                </div>
              </div>

              <!-----------------------------    SMPP   ------------------------------->

              <div v-else-if="connector == 'smpp'">
                <div v-for="(ep, epIndex) in endpoint[state]" :key="epIndex">
                  <smppEndpointForm
                    :edit-mode="editMode"
                    :is-prod="state === 'prod'"
                    :endpoint-index="epIndex"
                    @formChanged="onFormChange"
                    :ref="`endpoint${state === 'prod' ? 'Prod' : 'Preprod'}`"
                    :endpoint-data="ep"
                  ></smppEndpointForm>
                </div>
              </div>

              <!-----------------------------   COMZAACS   ------------------------------->

              <div v-else-if="connector == 'comzaAcs'">
                <div v-for="(ep, epIndex) in endpoint[state]" :key="epIndex">
                  <comzaAcsEndpointForm
                    :edit-mode="editMode"
                    :is-prod="state === 'prod'"
                    :endpoint-index="epIndex"
                    @formChanged="onFormChange"
                    @formUpdated="onFormUpdate"
                    :ref="`endpoint${state === 'prod' ? 'Prod' : 'Preprod'}`"
                    :endpoint-data="ep"
                  ></comzaAcsEndpointForm>
                </div>
              </div>

              <!-----------------------------       ----------------------------------->

              <div v-else class="d-flex pt-4 pr-5 pl-2 ml-4">
                <div class="alert alert-danger" v-if="editMode">
                  <strong>Sorry</strong>, this endpoint could not be found. <br />Please contact your network administrator
                </div>
                <div class="alert alert-danger" v-else>
                  <!-- Technically this will never load ... unless we try to CREATE a endpoint _not_ listed in the endpointSelector types `:) -->
                  <strong>Sorry</strong>, we were unable to load the endpoint type to be created. <br />Please contact your network administrator
                </div>
              </div>
            </b-tab>
            <!-----------------------------   clone-icon  ----------------------->

            <!--template v-slot:tabs-end>
              <li role="clone" style="flex-grow:2;" class="d-flex flex-row nav-item align-items-center justify-content-end">
                <div class="align-content-center justify-content-center mr-5" :class="tabIndex === 1 ? 'd-flex' : 'd-none'">
                  <span @click="clone()" @mouseover="cloneIconHover = true" @mouseout="cloneIconHover = false" class="cursor-pointer clone"
                    ><span class="mr-2">Clone Pre-prod Settings</span
                    ><i class="fa-clone cursor-pointer" :class="{ fas: cloneIconHover, far: !cloneIconHover }"></i
                  ></span>
                </div>
              </li>
            </template-->
            <!-- </div> -->
          </b-tabs>
          <hr class="my-4" style="border-top:1px solid #b8b8b8;" />
          <div class="mb-5 ml-5">
            <span
              v-tooltip.right="{
                content: !canUpdateAnEndpoint
                  ? 'Insufficient permission to update endpoints'
                  : !isFormValid
                  ? 'Please fill in valid and required fields (for all connections)'
                  : !hasChanged
                  ? 'Form unchanged, please update to submit'
                  : '',
                offset: 10,
              }"
            >
              <button
                @click="!endpointProdHasCompleted || editMode ? $refs.alertModal.show() : onCreate()"
                class="btn cs-btn"
                :disabled="!canUpdateAnEndpoint || !hasChanged || !isFormValid"
                :class="[
                  hasChanged && isFormValid && canUpdateAnEndpoint ? (showWarnButton ? 'cs-btn-warning' : 'cs-btn-submit') : 'cs-btn-inactive',
                ]"
                v-tooltip.right="{
                  classes: tooltips.saveEndpointButton.type,
                  content: tooltips.saveEndpointButton.content,
                  trigger: 'manual',
                  offset: 20,
                  show: tooltips.saveEndpointButton.visible,
                }"
              >
                Save Endpoint&emsp;<i class="ml-2" :class="saving ? 'fas fa-circle-notch fa-spin' : 'far fa-save'"></i>
              </button>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved */
import requiredAsterisk from '@/utils/components/required.vue';
import huxEndpointForm from '@/components/ConnectorEndpoints/HuX';
import ucipEndpointForm from '@/components/ConnectorEndpoints/UCIP';
import cai3gEndpointForm from '@/components/ConnectorEndpoints/CAI3G';
import mobileMoneyEndpointForm from '@/components/ConnectorEndpoints/mobileMoney';
import crediverseEndpointForm from '@/components/ConnectorEndpoints/crediverse';
import smppEndpointForm from '@/components/ConnectorEndpoints/smpp';
import comzaAcsEndpointForm from '@/components/ConnectorEndpoints/comzaAcs';

import PermissionManager from '@/utils/PermissionManager';
import { hideTooltip, showTooltip } from '@/utils/tooltip-alerts';
import { assert } from '@/utils/assertions';
import { mapGetters } from 'vuex';

import { required } from 'vuelidate/lib/validators';

export default {
  name: 'Endpoint',
  components: {
    huxEndpointForm,
    ucipEndpointForm,
    cai3gEndpointForm,
    mobileMoneyEndpointForm,
    crediverseEndpointForm,
    smppEndpointForm,
    comzaAcsEndpointForm,
    requiredAsterisk,
  },
  data() {
    return {
      shownConnection: { preprod: 0, prod: 0 },
      endpointDeletionObject: {},
      invalidConnector: false,
      saving: false,
      cloneIconHover: false,
      tabIndex: 0,
      endpointId: null,
      editMode: false,
      isMounted: false,
      feedback: {
        ok: true,
        message: '',
      },
      endpointPreprodHasInvalid: false,
      endpointProdHasInvalid: false,
      endpointProdHasCompleted: false,
      endpointInvalid: {
        preprod: [],
        prod: [],
      },
      hasChanged: !this.editMode,
      connector: 'waiting',
      originalEndpoint: {},
      endpoint: {
        name: '',
        description: '',
        preprod: [
          {
            timeout: 1000,
          },
        ],
        prod: [
          {
            timeout: 1000,
          },
        ],
        type: '',
      },
      tooltips: {
        newlyCreated: {
          visible: false,
          timeout: null,
          type: 'cs-success',
          invisibleInSeconds: 6, // default is 0, will not clear the tooltip
          content: 'New Endpoint Successfully Created!',
        },
        saveEndpointModalButton: {
          visible: false,
          timeout: null,
          type: 'cs-error',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
        saveEndpointButton: {
          visible: false,
          timeout: null,
          type: 'cs-error',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
      },
    };
  },
  validations: {
    endpoint: {
      name: {
        required,
      },
      description: {
        required,
      },
    },
  },
  computed: {
    ...mapGetters(['connectorEndpoints', 'userPermissions']),
    pm() {
      if (!this.userPermissions) return undefined;

      return new PermissionManager(this.userPermissions);
    },
    canUpdateAnEndpoint() {
      return this.pm && this.pm.hasPermission({ can: 'update', an: 'endpoint' });
    },
    endpointName() {
      let name = '';
      this.connectorEndpoints.findIndex(connectorEndpoint => {
        const endpointIndex = connectorEndpoint.endpoints.findIndex(endpoint => {
          name = endpoint.name;
          return endpoint.id === this.endpointId;
        });
        return endpointIndex !== -1;
      });
      return name;
    },
    isFormValid() {
      if (this.$v.$invalid || this.endpointPreprodHasInvalid || this.endpointProdHasInvalid || this.invalidConnector) {
        return false;
      }
      return true;
    },
    showWarnButton() {
      if (!this.isFormValid || this.endpointProdHasCompleted) {
        return false;
      }
      return true;
    },
  },
  watch: {
    endpoint: {
      handler() {
        if (this.editMode) {
          this.checkForChanges();
        }
        hideTooltip(this.tooltips.saveEndpointModalButton);
        hideTooltip(this.tooltips.saveEndpointButton);
      },
      deep: true,
    },
  },
  destroyed() {},
  mounted() {},
  created() {
    hideTooltip(this.tooltips.saveEndpointButton);
    hideTooltip(this.tooltips.saveEndpointModalButton);
    this.editMode = this.$route.name === 'edit';

    if (!this.editMode) {
      this.endpointId = null;
      this.isMounted = true;
      this.endpoint.type = this.$route.params.connectorType;
      this.connector = this.$route.params.connectorType;
    } else this.loadEndpoint();
  },
  methods: {
    async deleteAssociatedEndpointConnection() {
      const { environment, index } = this.endpointDeletionObject;
      const newEndpoint = [...this.endpoint[environment]];
      newEndpoint.splice(index, 1);
      this.endpoint[environment] = newEndpoint;
      await this.onUpdate();
      this.$bvModal.hide('ucip-endpoint-delete');

      // FIXME
      // Workaround issue with vue updating.
      //  Vue doesn't detect the endpoint change, so retains data from the DELETED connection instead of updating.
      //  Because of this we need to refresh the page
      this.isMounted = false;
      this.$router.go(0);
    },
    trueIfNestedHasChanged(old, newValue) {
      if (this.hasChanged) return true;

      if (old === null && newValue === null) {
        // eslint-disable-next-line consistent-return
        return undefined;
      } else if (assert(old).isArray() && assert(newValue).isArray()) {
        if (old.length !== newValue.length) {
          this.hasChanged = true;
        } else {
          old.findIndex((oldArrayValue, idx) => {
            return this.trueIfNestedHasChanged(oldArrayValue, newValue[idx]);
          });
        }
      } else if (assert(old).isObject() && assert(newValue).isObject()) {
        // eslint-disable-next-line camelcase
        const uniqueAndNonEmpty_newValueChildren = (() => {
          const newChildren = [];
          Object.keys(newValue).forEach(key => {
            if (key !== 'description' && typeof old[key] === 'undefined' && !!newValue[key]) newChildren.push(newValue[key]);
          });
          return newChildren;
        })();
        this.hasChanged = uniqueAndNonEmpty_newValueChildren.length > 0;

        Object.keys(old).findIndex(key => {
          return this.trueIfNestedHasChanged(old[key], newValue[key]);
        });
        // eslint-disable-next-line eqeqeq
      } else if (old != newValue && typeof newValue !== 'undefined') {
        this.hasChanged = true;
      }

      return this.hasChanged;
    },
    checkForChanges() {
      this.hasChanged = false;

      Object.keys(this.endpoint).findIndex(environment => {
        // environment is preprod OR prod
        let oldValue = this.originalEndpoint[environment];
        const value = this.endpoint[environment];

        if (assert(oldValue).isArray()) {
          oldValue = oldValue.map(v => {
            // eslint-disable-next-line no-param-reassign
            delete v.description;
            return v;
          });
        }

        this.trueIfNestedHasChanged(oldValue, value);
        return this.hasChanged;
      });

      return this.hasChanged;
    },
    onFormUpdate(invalidForm) {
      this.invalidConnector = invalidForm;
    },
    validName() {
      if (!this.editMode) {
        this.endpoint.name = this.endpoint.name.toJSName();
      }
    },
    onAlertModalShown() {
      hideTooltip(this.tooltips.saveEndpointButton);
      hideTooltip(this.tooltips.saveEndpointModalButton);
    },
    onAlertModalHide() {
      hideTooltip(this.tooltips.saveEndpointModalButton);
      this.$refs.alertModal.hide();
    },
    onFormChange(endpointObject) {
      hideTooltip(this.tooltips.saveEndpointButton);
      const { endpoint, isProd, validation, index } = endpointObject;
      // console.log('invalid::', validation.$invalid);
      if (isProd) {
        this.endpointInvalid.prod[index] = validation.$invalid;
        this.endpoint.prod[index] = endpoint;
      } else {
        this.endpointInvalid.preprod[index] = validation.$invalid;
        this.endpoint.preprod[index] = endpoint;
      }

      this.checkForChanges();

      const hasCompleted = this.endpoint.prod.findIndex(prodEndpoint => {
        const connectorType = this.connector;
        const hasRequiredCommonProdFields = prodEndpoint.hostname && prodEndpoint.port && prodEndpoint.timeout;
        let hasRequiredProdEndpoint = false;
        switch (true) {
          case connectorType === 'mobileMoney':
            hasRequiredProdEndpoint =
              hasRequiredCommonProdFields &&
              prodEndpoint.protocol &&
              prodEndpoint.authDetails.username &&
              prodEndpoint.authDetails.password &&
              prodEndpoint.authDetails.key;
            break;
          case connectorType === 'smpp':
            hasRequiredProdEndpoint =
              hasRequiredCommonProdFields && //
              prodEndpoint.auth.system_id &&
              prodEndpoint.auth.password;
            break;
          case connectorType === 'comzaAcs':
            hasRequiredProdEndpoint =
              hasRequiredCommonProdFields &&
              prodEndpoint.protocol &&
              prodEndpoint.authDetails.username &&
              prodEndpoint.authDetails.password &&
              prodEndpoint.countryCode;
            break;
          case connectorType === 'ucip':
            hasRequiredProdEndpoint =
              hasRequiredCommonProdFields &&
              prodEndpoint.url &&
              prodEndpoint.hostIdentity &&
              prodEndpoint.hostIdentity.originNodeType &&
              prodEndpoint.hostIdentity.originHostName;
            break;
          default:
            hasRequiredProdEndpoint = hasRequiredCommonProdFields;
        }

        return !!hasRequiredProdEndpoint;
      });

      this.endpointProdHasCompleted = hasCompleted !== -1;

      // Intentional Swap of evaluation for Prod VS Preprod --- one is inclusive, other is exclusive
      this.endpointProdHasInvalid = this.endpointInvalid.prod.findIndex(item => item === false) === -1;
      this.endpointPreprodHasInvalid = this.endpointInvalid.preprod.findIndex(item => item === true) !== -1;
    },
    clone() {
      // Pass by value to child (don't pass by reference here)
      // const endpointData = JSON.stringify(this.endpoint.preprod);
      const endpointData = { ...this.endpoint.preprod };
      this.$refs.endpointProd[0].onClone(endpointData);
    },
    loadEndpoint() {
      this.endpointId = this.$route.params.endpointId;

      this.$store
        .dispatch('connectorEndpoint/get', this.endpointId)
        .then(endpoint => {
          const ep = endpoint;
          ep.displayName = ep.name; // add 'displayName'

          this.originalEndpoint = JSON.parse(JSON.stringify(endpoint, null));
          this.endpoint = JSON.parse(JSON.stringify(endpoint, null));

          this.connector = endpoint.type;

          this.isMounted = true;
          if (this.$route.query.state === 'new') {
            setTimeout(() => {
              showTooltip(this.tooltips.newlyCreated, 'cs-success', 'Endpoint Successfully Created!', 6);
            }, 200);
          }
        })
        .catch(err => {
          // setting connector to empty shows 'error'
          this.connector = '';
          this.isMounted = true;
          console.error('GET connector endpoint failed\n', err);
        });
    },
    onUpdate() {
      hideTooltip(this.tooltips.saveEndpointModalButton);
      hideTooltip(this.tooltips.saveEndpointButton);
      console.log('UPDATE action, data:');

      const endpoint = { ...this.endpoint };
      delete endpoint.displayName;
      this.saving = true;

      return this.$store
        .dispatch('connectorEndpoint/update', endpoint)
        .then(updatedEndpoint => {
          console.log('connectorEndpoint/update', updatedEndpoint);
          this.originalEndpoint = JSON.parse(JSON.stringify(updatedEndpoint, null));
          this.hasChanged = false;
          this.$refs.alertModal.hide();
          showTooltip(this.tooltips.saveEndpointButton, 'cs-success', 'Endpoint Successfully Saved!', 5);
        })
        .catch(err => {
          showTooltip(this.tooltips.saveEndpointModalButton, 'cs-error', err.message || err);
          console.error('ERROR Updating endpoint: ', err);
          this.feedback.ok = false;
          this.feedback.message = err.message || err;
        })
        .then(() => {
          this.saving = false;
        });
    },
    onCreate() {
      hideTooltip(this.tooltips.saveEndpointModalButton);
      hideTooltip(this.tooltips.saveEndpointButton);
      // console.log('CREATE action, data:', endpoint);
      // debugger;

      // if (endpoint.type === 'SOAP') return;

      const endpoint = { ...this.endpoint };
      const { type } = endpoint;

      // ---- create successful ? - can edit now
      this.saving = true;
      this.$store
        .dispatch('connectorEndpoint/create', endpoint)
        .then(result => {
          console.log('connectorEndpoint/create view-', result);
          this.feedback = { ok: true, message: '' };
          const endpointName = result.name;
          this.editMode = true;
          this.$refs.alertModal.hide();
          /*
          FIXME .... add 'endpointType' into URL
           */
          // Done creating .... move to "edit" page
          window.location = `#/connector/endpoint/${type}/edit/${endpointName}?state=new`;
        })
        .catch(err => {
          showTooltip(this.tooltips.saveEndpointModalButton, 'cs-error', err.message || err);
          console.error('ERROR Creating endpoint: ', err);
          this.feedback.ok = false;
          this.feedback.message = err.message || err;
        })
        .then(() => {
          this.saving = false;
        });
    },
  },
};
</script>

<style lang="scss">
.endpoints-tab-shrink {
  max-width: 15rem;
}
</style>
<style lang="scss" scoped>
@import '@/styles/cs-styles.scss';
.outline-danger {
  border-color: $cs_color_danger !important;
  border-width: 2px !important;
}
.clone:hover .fa-clone {
  color: $coalesce_green;
}
.input-fields-height {
  height: 3.6em;
}
.description {
  width: 400px;
}
.error-messages {
  width: 100%;
  margin-left: 0.25rem;
  font-size: 80%;
  color: #dc3545;
  // margin-top: -1.7em;
  // margin-left: 15em;
}
.endpoint {
  &-title {
    margin: 30px;
    font-size: 1.2rem;
  }
  &-form {
    margin: 0 0 0 0;
    & .permission {
      width: 200px;
    }
    & .description {
      width: 600px;
    }
    & .hostname {
      width: 300px;
    }
    & .port {
      width: 150px;
    }
  }
}
/* always present */
.fade-transition {
  transition: all 0.4s ease;
  overflow: hidden;
}
/* .expand-enter defines the starting state for entering */
/* .expand-leave defines the ending state for leaving */
.fade-enter,
.fade-leave {
  opacity: 0;
}
</style>
