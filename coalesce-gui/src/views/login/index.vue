<template>
  <div
    :class="{ 'd-none': !pageLoaded, 'd-flex': pageLoaded }"
    class="login-container w-100 h-100 flex-column justify-content-start justify-content-sm-center align-items-center"
  >
    <div class="d-flex row justify-content-center justify-content-xl-start w-100">
      <!-- SM -->
      <div class="d-flex justify-content-center justify-content-xl-end col-xl-6 align-items-center align-items-xl-end">
        <div class="login-logo">
          <img class="mb-2" style="width: 140px" src="@/assets/images/Coalesce-Logo-with-text.png" />
        </div>
        <div class="d-flex flex-row mr-xl-5">
          <p class="pl-3 mb-1 text-light login-logo-slogan mr-xl-5" style="font-size: 120%">It’s all <span class="nowrap">coming together!</span></p>
          <div class="mr-xl-2"></div>
        </div>
      </div>
    </div>
    <div class="d-flex justify-content-center no-gutters">
      <div class="d-flex justify-content-center col-12">
        <div class="login-picture">
          <img class="d-none d-xl-flex" style="width: 100%" src="@/assets/images/young-african-male-on-cloud.svg" />
        </div>

        <form @submit="onSubmit" :model="loginForm" class="d-flex flex-column login-form justify-content-around">
          <div class="title-container">
            <h3 class="title">Sign In</h3>
            <p class="title-text">
              Welcome back, please sign in to
              <span class="nowrap">your account.</span>
            </p>
          </div>

          <div class="form-items">
            <div class="col-auto px-0">
              <label for="username">Email</label>
              <div
                class="input-group uname-input-group mb-2"
                :class="{ 'has-focus': hasFocus || autofilled }"
                v-tooltip.top-end="{
                  classes: tooltips.username.type,
                  content: tooltips.username.content,
                  trigger: 'manual',
                  offset: 0,
                  show: tooltips.username.visible,
                }"
              >
                <div class="input-group-prepend">
                  <div class="input-group-text border border-right-0" :class="classes.usernameInput">
                    <span class="svg-container">
                      <font-awesome-icon icon="user"></font-awesome-icon>
                    </span>
                  </div>
                </div>
                <input
                  @focus="hasFocus = true"
                  @blur="hasFocus = false"
                  @animationstart="autofillFix"
                  @input="borderControl"
                  v-model="loginForm.username"
                  class="for-username form-control border border-left-0"
                  type="text"
                  name="username"
                  id="username"
                  placeholder="<EMAIL>"
                  :class="classes.usernameInput"
                  ref="username"
                />
              </div>
            </div>

            <div class="col-auto px-0 pword-tooltip-block">
              <label for="password">Password</label>
              <div
                class="input-group pword-input-group mb-2"
                v-tooltip.top-end="{
                  classes: tooltips.password.type,
                  content: tooltips.password.content,
                  trigger: 'manual',
                  offset: 0,
                  show: tooltips.password.visible,
                }"
              >
                <div class="input-group-prepend">
                  <div class="input-group-text border border-right-0" :class="classes.passwordInput">
                    <span class="svg-container">
                      <font-awesome-icon icon="lock"></font-awesome-icon>
                    </span>
                  </div>
                </div>
                <input
                  @input="borderControl"
                  v-model="loginForm.password"
                  class="form-control border border-right-0 border-left-0"
                  :class="classes.passwordInput"
                  :type="passwordType"
                  name="password"
                  id="password"
                  placeholder="********"
                  ref="password"
                />
                <div class="input-group-append">
                  <div class="input-group-text border border-left-0" :class="classes.passwordInput">
                    <span class="show-pwd cursor-pointer" @click="showPwd">
                      <font-awesome-icon :icon="passwordType === 'password' ? ['far', 'eye-slash'] : ['far', 'eye']"></font-awesome-icon>
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div class="d-flex justify-content-between options-wrapper pb-4 mb-2">
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="rememberMeCheck" v-model="loginForm.remember" />
                <label class="form-check-label" for="rememberMeCheck">Remember me?</label>
              </div>
              <div>
                <a class="cs-text-primary" style="font-size: 0.95em" href="#" @click.prevent="forgotPassword">Forgot Password</a>
              </div>
            </div>

            <div class="d-flex justify-content-center w-100">
              <button
                v-tooltip.bottom="{
                  classes: tooltips.submit.type + ' login-err-alert',
                  content: tooltips.submit.content,
                  trigger: 'manual',
                  container: '.err-container',
                  offset: 4,
                  show: tooltips.submit.visible,
                }"
                type="submit"
                ref="submit"
                class="btn cs-btn d-flex align-items-center"
                :disabled="!valid.username || !valid.password"
                :class="classes.submitButton"
              >
                Sign In
                <font-awesome-icon v-if="loading" class="ml-2" icon="circle-notch" spin></font-awesome-icon>
              </button>
            </div>
            <div class="d-flex justify-content-center w-100 err-container"></div>
          </div>
          <!-- <p class="terms-and-policy-text">
            By proceeding, you agree with our
            <a href="#" class="nowrap">Terms of Service</a> and
            <a href="#" class="nowrap">Privacy Policy</a>
          </p> -->
        </form>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved */
import { mapGetters } from 'vuex';
import { hideTooltip, showTooltip } from '@/utils/tooltip-alerts';

export default {
  name: 'Login',
  data() {
    return {
      loading: false,
      hasFocus: false,
      autofilled: false,
      submittable: false,
      pageLoaded: false,
      tooltips: {
        username: {
          visible: false,
          timeout: null,
          type: '',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
        password: {
          visible: false,
          timeout: null,
          type: '',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
        submit: {
          visible: false,
          timeout: null,
          type: '',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
      },
      classes: {
        usernameInput: 'border-dark',
        passwordInput: 'border-dark',
        submitButton: 'cs-btn-inactive',
        timeout: null,
      },
      show: {
        msg_failure: false,
      },
      loginForm: {
        username: '',
        password: '',
        remember: false,
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur' }],
        password: [{ required: true, trigger: 'blur' }],
      },
      valid: {
        username: true,
        password: true,
      },
      failure: '',
      pwMinLength: 8,
      passwordType: 'password',
      redirect: undefined,
      isSuccess: null,
    };
  },
  computed: {
    ...mapGetters(['user']),
  },
  watch: {
    $route: {
      handler(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  mounted() {
    this.pageLoaded = true;

    if (typeof this.$route.query['session-expired'] !== 'undefined') {
      showTooltip(this.tooltips.submit, 'cs-warning', 'Your session expired, please login again.');
    }

    // ---- Vue workaround (which DOESN'T WORK) for waiting for page elements to load before triggering the 'valid/invalid' fields display
    //      HOWEVER: It's important to note... the ONLY place this doesn't work is for browser auto-filled fields :/
    this.$nextTick(() => {
      // ---- Additional few millisecond timeout, still did not help with browser auto-filled fields
      setTimeout(() => {
        this.setUpdatable();
      }, 300);
    });
  },
  methods: {
    autofillFix(e) {
      if (e.animationName === 'onAutoFillStart') this.autofilled = true;
      else if (e.animationName === 'onAutoFillCancel') this.autofilled = false;

      this.$nextTick(() => {
        this.setUpdatable();
      });
    },
    setUpdatable() {
      if (this.classes.timeout) clearTimeout(this.classes.timeout);
      const validUname = this.validateUsername();
      const validPword = this.validatePassword();
      if (validUname && validPword) this.submittable = true;
      else this.submittable = false;

      this.classes.submitButton = this.submittable ? 'cs-btn-submit' : 'cs-btn-inactive';

      return { validUname, validPword };
    },
    borderControl(eventOrFieldName) {
      if (typeof eventOrFieldName !== 'string') this.$emit('button', eventOrFieldName.target.value);

      hideTooltip(this.tooltips.submit);
      const { validUname, validPword } = this.setUpdatable();
      const fieldName = typeof eventOrFieldName === 'string' ? eventOrFieldName : eventOrFieldName.target.name;
      let valid = false;
      this.classes[`${fieldName}Input`] = 'border-dark';
      this.valid[fieldName] = true;

      hideTooltip(this.tooltips[fieldName]);

      if (fieldName === 'username') valid = validUname;
      else valid = validPword;

      if (!valid && this.loginForm.username !== '') {
        this.classes.timeout = setTimeout(() => {
          showTooltip(
            this.tooltips[fieldName],
            'cs-warning',
            fieldName === 'username' ? 'Invalid email format' : `Minimum Length: ${this.pwMinLength}`,
          );
          this.classes[`${fieldName}Input`] = 'border-danger';
          this.valid[fieldName] = false;
        }, 600);
      } else {
        hideTooltip(this.tooltips[fieldName]);
        this.valid[fieldName] = true;
      }
    },
    forgotPasswordTooltip(options = { success: true, message: '' }) {
      const { success } = options;
      let { message } = options;
      if (String(message).length === 0) {
        message = 'If a valid account exists with this email address, then login detals will be <span class="nowrap">emailed there.</span>';
      }

      // ---- Mimic delay, we want to imply there was success in almost all cases - EVEN IF THERE ISN'T
      //      If they are a legitimate user and did not receive their email ... they must contact SysAdmin
      //      Errors should not be dislpayed in case of sensitive information
      setTimeout(() => {
        showTooltip(this.tooltips.submit, success ? 'cs-success' : 'cs-error', message, success ? 5 : null);
      }, 300);
    },
    forgotPassword() {
      if (!this.validateUsername()) {
        showTooltip(this.tooltips.username, 'cs-warning', 'Please enter your email to request your password reset', 5);
        return;
      }

      this.$store
        .dispatch('user/forgotten', { email: this.loginForm.username })
        .then(result => {
          console.debug('success', result);
          this.forgotPasswordTooltip();
        })
        .catch(err => {
          if (err.message.match(/general settings/i)) {
            this.forgotPasswordTooltip({ success: false, message: 'Error sending password reset request.' });
          } else {
            this.forgotPasswordTooltip();
          }
        });
    },
    validateUsername() {
      // is email ?
      const re = new RegExp('[^@]+@[^@]+\\.[^@]+');
      return !!this.loginForm.username.match(re);
    },
    validatePassword() {
      const re = new RegExp(`^.{${this.pwMinLength},}$`);
      const regexMatch = this.loginForm.password.match(re);
      return regexMatch;
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = '';
      } else {
        this.passwordType = 'password';
      }
    },
    onSubmit(evt) {
      // ---- although not a "necessary" step to "hide" the tooltip.... this helps with UX
      //      - it causes a "flash" of the error when you attempt to login again
      //      - thus preventing the impression that your second login attempt was not processed
      //      - by seeing the flash, the user knows they are being presented with the error again ... i.e. something happened.
      hideTooltip(this.tooltips.submit);
      // debugger;
      evt.preventDefault();

      // ---- in case the request was made to reset... let's make sure it doesn't send that type of request ;)
      this.loginForm.resetRequest = false;

      if (this.submittable) {
        this.loading = true;

        this.$store
          .dispatch('user/login', this.loginForm)
          .then(userData => {
            this.$store.dispatch('user/getInfo').then(() => {
              this.$root.$emit('enableOnActive'); // enables "lockoutManager" when the user becomes 'active' (i.e. they LOGIN)
              if (typeof userData.state === 'string' && ['Invited', 'forgotPassToken'].includes(userData.state)) {
                // ---- Invited user uses an OTP... add this to `user.info` in the state
                //      Once retrieved for use on the next page (/login/choose-password) ...
                //      it is used as 'old_password' so that the user does not have to re-enter it
                const { info } = this.$store.state.user;
                info.otp = this.loginForm.password;
                info.currentState = userData.state; // storing this for checking when on the choose-password page
                this.$store.commit('user/SET_INFO', info);
                this.$router.push({ path: '/login/choose-password' });
              } else this.$router.push({ path: this.redirect || '/' });

              this.loading = false;
            });
          })
          .catch(err => {
            showTooltip(this.tooltips.submit, 'cs-error', err.message || err);

            this.$refs.username.focus();
            this.$refs.username.select();
            this.loading = false;
          });
      }
    },
  },
};
</script>

<style lang="scss">
$light_gray: #eee;
$dark_gray: #889aa4;
$cursor: #fff;
$login_text: #444;
$login_green: #39b54a;
$info_text: #33b5e5;

.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
  height: 0;
}

html,
body {
  min-width: 350px;
  min-height: 650px;
}

.login-err-alert {
  top: auto !important;
  left: auto !important;
  transform: none !important;
  .tooltip-inner {
    max-width: 20em;
  }
}

.input-group-text {
  background: transparent;
}

.form-control:focus {
  -webkit-box-shadow: 0 0 0 0 transparent;
  box-shadow: 0 0 0 0 transparent;
}

/* autofill fix - see https://rojas.io/how-to-detect-chrome-autofill-with-vue-js/ */
:-webkit-autofill {
  animation-name: onAutoFillStart;
}
:not(:-webkit-autofill) {
  animation-name: onAutoFillCancel;
}
@keyframes onAutoFillStart {
  from {
  }
  to {
  }
}
@keyframes onAutoFillCancel {
  from {
  }
  to {
  }
}
</style>

<style lang="scss" scoped>
/* Define an animation behavior */
@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}
/* This is the class name given by the Font Awesome component when icon contains 'spinner' */
.fa-spinner,
.fa-spin {
  /* Apply 'spinner' keyframes looping once every second (1s)  */
  animation: spinner 1s linear infinite;
}
$dark_gray: #889aa4;
$light_gray: #eee;
$login_text: #444;
$login_green: #39b54a;
$info_text: #33b5e5;

.login-container {
  background: rgb(84, 88, 92);
  background: radial-gradient(circle, rgba(84, 88, 92, 1) 0%, rgba(120, 127, 132, 1) 88%, rgba(120, 127, 132, 1) 100%);
  /*  background-image: url("~@/assets/images/login-bg-polygon.jpg");
  background-repeat: no-repeat;
  background-size: 100% 100%;*/
  background: radial-gradient(circle, rgba(84, 88, 92, 1) 0%, rgba(120, 127, 132, 1) 88%, rgba(120, 127, 132, 1) 100%);
  min-height: 100%;
  width: 100%;
  overflow: hidden;
  padding-bottom: 2em;

  .login-picture {
    @media only screen and (min-width: 1200px) {
      min-width: 500px;
    }
  }
  .login-picture img {
    margin: 0;
    width: 100%;
    max-width: 500px;
    max-height: 500px;
    padding: 0;
  }

  .login-form {
    height: 500px;
    min-width: 450px;
    max-width: 600px;
    background-color: white;
    padding: 2em;
    padding-bottom: 0.5em;
    width: 100%;
    margin: auto 0;

    .title-container {
      position: relative;
      margin-bottom: 40px;

      p {
        margin: 0;
        padding: 0;
      }

      .title {
        font-size: 26px;
        color: $login_text;
        margin: 0 0 10px 0;
        text-align: left;
        font-weight: normal;
        color: $login_green;
      }
      .title-text {
        letter-spacing: 0.1em;
      }
      @media only screen and (max-width: 1200px) {
        .title,
        .title-text {
          text-align: center;
        }
      }
    }

    .form-items {
      width: 80%;
      padding-bottom: 4.5em;

      .form-item-label {
        font-size: 90%;
      }
      button[type='submit'] {
        display: flex;
        padding: 0.5em 3em;
      }

      @media only screen and (max-width: 1200px) {
        width: 100%;
      }
    }
    .terms-and-policy-text {
      line-height: 1.4em;
      text-align: left;
      font-size: 80%;
      margin-bottom: 0;
    }
    .terms-and-policy-text a {
      color: $info_text;
    }

    @media only screen and (max-width: 1200px) {
      width: 100%;
      margin: auto 0;
      .terms-and-policy-text {
        text-align: center;
      }
    }
    @media only screen and (max-width: 768px) {
      .terms-and-policy-text {
        margin-bottom: 1rem;
      }
    }
  }
}
</style>
