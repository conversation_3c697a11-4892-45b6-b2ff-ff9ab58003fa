<template>
  <div class="d-flex justify-content-center align-items-center w-100 h-100 choose-pass-container pt-4 px-0 px-sm-5">
    <div class="d-flex flex-column justify-content-center col-12 col-md-8 col-lg-6 col-xl-5 p-2 p-sm-4">
      <h2>
        Choose a
        <span class="nowrap">new password</span>
      </h2>
      <div class="picture-title py-2">
        <img v-show="user.picture" class="user-picture ml-2 mr-3" style="height:5.5em; width:5.5em" :src="user.picture" />
        <h6 v-show="!user.picture" class="navbar-btn-click bg-dark mb-0 text-light rounded-circle ml-2 mr-3" style="padding:1.1rem;">
          <!-- eslint-disable-next-line vue/no-v-html -->
          <span class="navbar-btn-click" v-html="user.firstname.slice(0, 1) + user.lastname.slice(0, 1)"></span>
        </h6>

        <div class="user-title d-flex flex-column justify-content-center align-items-start">
          <h4 class="m-0">{{ user.firstname }} {{ user.lastname }}</h4>
          <h4 class="m-0">
            <small>{{ user.email }}</small>
          </h4>
        </div>
      </div>
      <div class="password-help mt-2 mb-4">
        <p class="help-text">
          <strong>
            Strong passwords are hard to guess, includes numbers, letters and
            <span class="nowrap">punctuations marks.</span>
            <a href="#" class="pw-help-link">Learn More</a>
          </strong>
        </p>
      </div>
      <div>
        <form ref="choosePasswordForm" @submit="onSubmit" class="choose-password-form" auto-complete="on" label-position="left">
          <div class="col-auto px-0">
            <label class="bold mb-4">Type new password</label>
            <div
              class="input-group mb-2"
              v-tooltip.right="{
                classes: 'cs-warning pr-4',
                content:
                  'New Password should be greater than 7 characters and it must contain a number, a lowercase ,an uppercase, and a special character',
                trigger: 'manual',
                offset: 50,
                show: $v.newPassword.$invalid && show.inputError,
              }"
            >
              <div class="input-group-prepend">
                <div class="input-group-text" :class="classes.newPassword">
                  <span class="svg-container">
                    <font-awesome-icon icon="user-lock"></font-awesome-icon>
                  </span>
                </div>
              </div>
              <input
                ref="newPassword"
                @input="delayTouch($v.newPassword)"
                v-model="$v.newPassword.$model"
                :type="passwordType"
                class="form-control border-right-0 border-left-0"
                :class="classes.newPassword"
                name="newPassword"
                id="newPassword"
                placeholder="Type your new password here"
              />
              <div class="input-group-append">
                <div class="input-group-text" :class="classes.newPassword">
                  <span class="show-pwd cursor-pointer" @click="showPwd">
                    <font-awesome-icon :icon="passwordType === 'password' ? ['far', 'eye-slash'] : ['far', 'eye']"></font-awesome-icon>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!--p class="confirm-text pt-3">
            <strong>
              We will send you an email to confirm <span class="nowrap">your password reset</span>
            </strong>
          </p-->

          <div class="d-flex justify-content-center justify-content-xl-start pt-3">
            <div class="col-12 col-xl-6 d-flex justify-content-center justify-content-xl-end">
              <input
                :class="classes.submitButton"
                :disabled="$v.newPassword.$invalid || classes.submitButton == 'cs-btn-inactive'"
                type="submit"
                class="px-5 py-2 btn cs-btn"
                value="Submit"
                v-tooltip.right="{
                  classes: tooltips.submit.type,
                  content: tooltips.submit.content,
                  trigger: 'manual',
                  offset: 20,
                  show: tooltips.submit.visible,
                }"
              />
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved */
import { mapGetters } from 'vuex';
import { hideTooltip, showTooltip } from '@/utils/tooltip-alerts';
import { required, minLength } from 'vuelidate/lib/validators';

const touchMap = new WeakMap();

export default {
  name: 'ChoosePassword',
  data() {
    return {
      newPasswordTimeout: null,
      tooltips: {
        submit: {
          visible: false,
          timeout: null,
          type: 'cs-error',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
      },
      classes: {
        newPassword: 'border-dark',
        submitButton: 'cs-btn-inactive',
      },
      show: {
        inputError: false,
      },
      newPassword: '',
      passwordType: 'password',
    };
  },
  validations: {
    newPassword: {
      required,
      minLength: minLength(8),
      goodPassword: password => {
        // a custom validator!
        return password.length >= 7 && /[a-z]/g.test(password) && /[A-Z]/g.test(password) && /[0-9]/g.test(password) && /[^a-zA-Z\d]/g.test(password);
      },
    },
  },
  computed: {
    ...mapGetters(['user']),
  },
  mounted() {
    this.$refs.newPassword.focus();
  },
  methods: {
    delayTouch($v) {
      hideTooltip(this.tooltips.submit);
      clearTimeout(this.newPasswordTimeout);
      this.show.inputError = false;
      touchMap.set($v, $v.touch);
      $v.$reset();

      this.classes.submitButton = this.$v.newPassword.$invalid ? 'cs-btn-inactive' : 'cs-btn-submit';

      if (this.$v.newPassword.$model.length <= 0) this.classes.newPassword = 'border-dark';

      this.newPasswordTimeout = setTimeout(() => {
        if (this.$v.newPassword.$invalid) this.classes.newPassword = this.$v.newPassword.$model.length <= 0 ? 'border-dark' : 'border-danger';
        else this.classes.newPassword = 'border-success';

        this.show.inputError = !!this.$v.newPassword.$invalid;
      }, 700);
    },
    showPwd() {
      hideTooltip(this.tooltips.submit);

      this.passwordType = this.passwordType === 'password' ? '' : 'password';

      this.$nextTick(() => {
        this.$refs.newPassword.focus();
      });
    },
    async onSubmit(evt) {
      // debugger;
      evt.preventDefault();

      if (!this.$store.state.user.info) {
        showTooltip(this.tooltips.submit, 'cs-error', 'Server Error: Could not retrieve stored OTP, please contact support', 10);
        return;
      }

      try {
        const check = {
          email: this.$store.state.user.email,
          current_password: this.newPassword,
          password: this.newPassword,
        };
        await this.$store.dispatch('user/updateUser', check);
        // No throw? .... that means oldpassword == new password (successful change), so do not change password in the db
        showTooltip(this.tooltips.submit, 'cs-warning', 'Your new Password must differ from your old password', 10);
        return;
      } catch (e) {
        console.error('failed to update user', e);
      }

      const toSubmit = {
        // ---- only submit Email, because ID (if submitted) will be used as precedence over email
        email: this.$store.state.user.email,
        current_password: this.$store.state.user.info.otp,
        password: this.newPassword,
      };
      // ---- If the password we used to login was a token reset password (determined ahead of time)
      //      Then we want to flag it, because we must decide whether they are "Invited" or not to show them the "change picture" option.
      const hasFirstTime = this.$store.state.user.info.currentState === 'forgotPassToken' ? {} : { 'first-time': 1 };

      //  Stored the password and current state temporarily for page handover.
      //  Removing them now...
      delete this.$store.state.user.info.currentState;
      delete this.$store.state.user.info.otp;
      const newInfo = this.$store.state.user.info;
      this.$store.commit('user/SET_INFO', newInfo);

      // ---- disables the button during submit action
      this.classes.submitButton = 'cs-btn-inactive';

      // ---- If this button works, then the password request is VALID (i.e. this.$v.newPassword.$invalid == false)
      this.$store
        .dispatch('user/updateUser', toSubmit)
        .then(() => {
          this.$store
            .dispatch('user/getInfo')
            .then(() => {
              // successfully updated password
              showTooltip(this.tooltips.submit, 'cs-success', 'Password updated successfully! Redirecting...', 4);
              setTimeout(() => {
                this.$router.push({ path: '/', query: hasFirstTime });
              }, 3000);
            })
            .catch(err => {
              console.error('An unknown error occurred during getInfo() request', err);
            });
        })
        .catch(err => {
          showTooltip(this.tooltips.submit, 'cs-error', err.message || 'Request Error');
        });
    },
  },
};
</script>

<style lang="scss">
$off_white: #fafafa;
$light_gray: #eee;
$dark_gray: #889aa4;
$login_text: #444;
$login_green: #39b54a;
$info_text: #33b5e5;

html,
body,
#app {
  height: 100%;
  width: 100%;
}

html,
body {
  min-width: 350px;
  min-height: 650px;
  background-color: $off_white;
}

.input-group-prepend .input-group-text,
.input-group-append .input-group-text {
  border-radius: 15px;
}

.form-control:focus {
  -webkit-box-shadow: 0 0 0 0 transparent;
  box-shadow: 0 0 0 0 transparent;
}

.input-group-text {
  background: transparent;
}

.choose-pass-container {
  .choose-password-form {
    .new-password {
      .el-input-group__prepend {
        border-radius: 10px 0 0 10px;
      }
      .el-input-group__append {
        border-radius: 0 10px 10px 0;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
$white: #fff;
$dark_gray: #889aa4;
$light_gray: #eee;
$login_text: #444;
$login_green: #39b54a;
$info_text: #33b5e5;

.choose-pass-container {
  min-width: 350px;
  padding-bottom: 10em;
  .picture-title {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .user-picture {
      border-radius: 50%;
      /* Padding creates (intentional) distortion on the picture image... margin makes the radius a pure circle */
      /*padding: 0.5em 1.5em 0.5em 0;*/
      /*margin: 0.5em 2em 0.5em 0;*/
    }
    .user-title {
      h4 {
        font-weight: bold;
        letter-spacing: 0.05em;
      }
      small {
        font-weight: normal;
      }
    }
  }
  .password-help {
    letter-spacing: 0.05em;
    .help-text {
      font-size: 0.85em;
      margin: 0;
      font-weight: normal;
    }
    .pw-help-link {
      color: $login_green;
    }
  }
  .input-group {
    max-width: 25em;
    margin-bottom: 0.8em;
  }
  .confirm-text {
    margin-bottom: 2em;
  }
  .el-button--submit.active {
    background-color: $login_green;
    color: $light_gray;
  }
  .el-button--submit {
    background-color: $light_gray;
    color: $dark_gray;
    display: flex;
    padding-left: 2em;
    padding-right: 2em;
    border-radius: 10px;
  }
}
</style>
