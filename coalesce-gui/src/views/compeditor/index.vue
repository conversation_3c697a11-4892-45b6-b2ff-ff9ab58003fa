<template>
  <div class="dashboard-container ml-2 mt-4 mr-3 mb-5">
    <div class="d-flex justify-content-between align-items-center">
      <h1 class="mb-4">{{ component.name }}</h1>
      <!-- <div class="mb-3">
                <b-button v-b-toggle.collapse-3 class="m-1">Choose Libraries</b-button>
                <b-collapse visible id="collapse-3">
                    <b-card class="p-0 m-0 mr-3">
                        <b-form-checkbox-group id="library" v-model="selected" size="md" switches>
                            <span v-for="(library, index) in libraries" :key="index">
                                <b-form-checkbox
                                    @change="onCheckboxToggle(library, $event)"
                                    :value="library.value"
                                    inline
                                >
                                    {{ library.name }}
                                </b-form-checkbox>
                            </span>
                        </b-form-checkbox-group>
                    </b-card>
                </b-collapse>
      </div>-->
      <!-- eslint-disable-next-line prettier/prettier -->
      <button :disabled="true" style="min-width:8em;" class="alert mr-3 px-4 py-2" :class="save.class">
        <!-- eslint-disable-next-line vue/no-v-html -->
        <span v-html="saveText" class="mr-1"></span>
        <font-awesome-icon
          class="ml-2"
          :icon="saveState !== 'done' && saveState !== 'failed' ? 'circle-notch' : 'save'"
          :spin="saveState === 'saving'"
        ></font-awesome-icon>
      </button>
    </div>
    <div id="monaco-editor"></div>
  </div>
</template>

<script>
// eslint-disable-next-line import/no-unresolved
import * as monaco from 'monaco-editor';

export default {
  name: 'Dashboard',
  data() {
    return {
      editor: null,
      compLibraries: [],
      libraries: [
        { name: 'SOAP', value: 1 },
        { name: 'UCIP', value: 2 },
      ],
      selected: [],
      saveState: 'done',
      component: {
        code: '',
        saved: true, // default is assume we are 'saved' when loading the page
      },
      save: {
        class: 'alert-secondary',
        text: 'Saved&emsp;<i class="fas fa-save"></i>',
        timeout: '',
        seconds: 2,
      },
      oldCode: '',
    };
  },
  computed: {
    saveText() {
      let text;

      switch (this.saveState) {
        case 'failed':
          text = 'Save&nbsp;Failed';
          break;
        case 'saving':
          text = 'Busy...';
          break;
        case 'waiting':
          text = 'Waiting...';
          break;
        default:
          text = 'Saved';
          break;
      }

      return text;
    },
  },
  watch: {
    saveState() {
      switch (this.saveState) {
        case 'done':
          this.save.class = 'alert-success';
          break;
        case 'saving':
        case 'waiting':
          this.save.class = 'alert-warning';
          break;
        case 'failed':
          this.save.class = 'alert-failed';
          break;
        default:
          this.save.class = 'alert-secondary';
          break;
      }
    },
  },
  mounted() {
    this.$store.dispatch('component/getcomponent', this.$route.params).then(response => {
      const [component] = response.comp;
      this.component = component;
      this.compLibraries = response.libraries;

      // save a copy of the code
      this.oldCode = this.component.code;

      this.component.saved = true;

      // eslint-disable-next-line max-len
      // ---- monaco OPTIONS refreence : https://microsoft.github.io/monaco-editor/api/interfaces/monaco.editor.ieditorconstructionoptions.html#cursorsmoothcaretanimation
      const monacoSettings = {
        // wordWrapColumn: 100,
        value: this.component.code,
        language: 'javascript',
        theme: 'vs-dark',
        cursorSmoothCaretAnimation: true,
        automaticLayout: true,
        mouseWheelZoom: true,
        // wordWrap: "wordWrapColumn",
        minimap: {
          enabled: false,
        },
      };

      let myMonaco;

      if (window.$loadMonacoInNode) {
        myMonaco = monaco;
      } else {
        myMonaco = window.$monacoEditor;
      }

      // Stuff to add to monaco to allow 'intellisense' --- WIP ... TODO
      /*
      myMonaco.languages.registerSignatureHelpProvider('javascript', {
        signatureHelpTriggerCharacters: ['(', ','],
        provideSignatureHelp: (model, position, token) => {
          console.log('Signature Help', model, position, token);
          return {
            activeParameter: 0,
            activeSignature: 0,
            signatures: [
              {
                label: 'string substr(string $string, int $start [, int $length])',
                parameters: [
                  {
                    label: 'string $string',
                    documentation: 'The input string. Must be one character or longer.',
                  },
                  {
                    label: 'int $start',
                    documentation:
                      "If $start is non-negative, the returned string will start at the $start'th position in string, " +
                      "counting from zero. For instance, in the string 'abcdef', the character at position 0 is 'a', " +
                      "the character at position 2 is 'c', and so forth.\r\nIf $start is negative, the returned string " +
                      "will start at the $start'th character from the end of string. If $string is less than $start " +
                      "characters long, FALSE will be returned.",
                  },
                  {
                    label: 'int $length',
                    documentation:
                      'If $length is given and is positive, the string returned will contain at most $length characters ' +
                      'beginning from $start (depending on the length of $string) If $length is given and is negative, ' +
                      'then that many characters will be omitted from the end of $string (after the start position has ' +
                      'been calculated when a start is negative). If $start denotes the position of this truncation or ' +
                      'beyond, FALSE will be returned. If $length is given and is 0, FALSE or NULL, an empty string will ' +
                      'be returned. If $length is omitted, the substring starting from $start until the end of the string will be returned.',
                  },
                ],
              },
            ],
          };
        },
      });

      myMonaco.languages.registerCompletionItemProvider('javascript', {
        provideCompletionItems: (model, position) => {
          // debugger
          const suggestions = [
            {
              label: 'substr',
              kind: monaco.languages.CompletionItemKind.Function,
              documentation: 'Finds a substring of a string.',
              detail: 'string',
            },
            {
              label: 'simpleText',
              kind: monaco.languages.CompletionItemKind.Text,
              insertText: 'simpleText',
            },
            {
              label: 'testing',
              kind: monaco.languages.CompletionItemKind.Keyword,
              // eslint-disable-next-line no-template-curly-in-string
              insertText: 'testing(${1:condition})',
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            },
            {
              label: 'ifelse',
              kind: monaco.languages.CompletionItemKind.Snippet,
              // eslint-disable-next-line no-template-curly-in-string
              insertText: ['if (${1:condition}) {', '\t$0', '} else {', '\t', '}'].join('\n'),
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'If-Else Statement',
            },
          ];
          return { suggestions };
        },
      });
      */

      this.editor = myMonaco.editor.create(document.getElementById('monaco-editor'), monacoSettings);

      // ---- Save Event listener
      this.editor.onKeyUp(() => {
        this.save.seconds = 2;
        const currentValue = this.editor.getValue();

        // save-if-different trigger
        if (currentValue !== this.oldCode) this.updateCode(this.editor.getValue());
      });

      // this will trigger save event when user press ctrl+s
      // eslint-disable-next-line no-bitwise
      this.editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KEY_S, () => {
        this.save.seconds = 0;

        const currentValue = this.editor.getValue();
        if (currentValue !== this.oldCode) this.updateCode(this.editor.getValue());
      });
    });
  },
  methods: {
    onCheckboxToggle(library, value) {
      console.log('onValueChange', library, value);
      const data = {
        componentId: this.$route.params.componentId,
        libraryId: library.value,
      };
      if (value) {
        this.$store
          .dispatch('component/addLibrary', data)
          .then(res => {
            console.log('Library added successfully', res);
          })
          .catch(err => {
            console.log('Unable to save Library! Error!', err);
          });
      } else {
        this.$store
          .dispatch('component/removeLibrary', data)
          .then(res => {
            console.log('Library removed successfully', res);
          })
          .catch(err => {
            console.log('Unable to remove Library! Error!', err);
          });
      }
    },
    updateCode(newValue) {
      // debugger;

      this.saveState = 'waiting';

      clearTimeout(this.save.timeout);

      const { componentId } = this.$route.params;

      this.save.timeout = setTimeout(async () => {
        this.saveState = 'saving';
        this.$store
          .dispatch('component/setcomponent', {
            componentId,
            code: newValue,
          })
          .then(() => {
            console.log('Saved!');
            this.oldCode = newValue;
            this.saveState = 'done';
          })
          .catch(err => {
            this.saveState = 'failed';
            console.log('Unable to save! Error!', err);
            // IMPORTANT - this is disabled, but if enabled, it will prevent "saving" when there is an ERROR
            //
            //  Using this assignment, we are setting the old and new values to be the same ...
            //  this prevents the 'save-if-different' trigger (see comment below in `mounted()`)
            //
            //  The SOLE reason to want to prevent this trigger, is to prevent save attempts if an error is present
            //  However, we will need to enable a MANUAL SAVE if we want this ---- which doesn't exist at this stage
            //
            //    this.oldCode = newValue;
            //
          });
      }, this.save.seconds * 1000);
    },
  },
};
</script>

<style lang="scss" scoped>
.dashboard {
  &-container {
    margin: 10px 0 0 0;
    resize: vertical;
    // overflow: auto;
  }
  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}

.editor-width {
  width: 900px;
}

#monaco-editor {
  height: 100vh;
  background-color: transparent;
  resize: vertical;
  // overflow: auto;
}
</style>
