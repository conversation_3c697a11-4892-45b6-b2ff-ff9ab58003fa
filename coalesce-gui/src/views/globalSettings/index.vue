<template>
  <div class="px-4">
    <h1 class="m-3 my-4">Settings</h1>
    <b-tabs active-nav-item-class="active-tab-text" content-class="mt-3" fill v-model="tabIndex">
      <b-tab title="General Settings" title-link-class="h5 mb-0" active>
        <p class="p-4">
          <general-settings
            v-if="globalSettings.generalSettings.length !== 0 && hasGeneralSettingsListPermission"
            :settings="globalSettings.generalSettings"
            :focussed="tabIndex === indexes.generalSettingsIdx"
          ></general-settings>
          <b-alert class="m-4" show variant="warning" v-else>Insufficient permissions to list General Settings</b-alert>
        </p>
      </b-tab>
      <!--b-tab title="Language Settings" title-link-class="h5 mb-0">
        <p class="p-4">
          <language-settings :focussed="tabIndex === indexes.languageSettingsIdx"></language-settings>
        </p>
      </b-tab-->
      <b-tab title="Global Messages" title-link-class="h5 mb-0">
        <p class="p-4">
          <global-messages v-if="hasGlobalMessagesListPermission" :focussed="tabIndex === indexes.globalMessagesIdx"></global-messages>
          <b-alert class="m-4" show variant="warning" v-else>Insufficient permissions to list Global Messages</b-alert>
        </p>
      </b-tab>
    </b-tabs>
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved */
import { mapGetters } from 'vuex';
import PermissionManager from '@/utils/PermissionManager';
import generalSettings from './generalSettings.vue';
// import languageSettings from './languageSettings.vue';
import globalMessages from './globalMessages.vue';
// import firstTimeSettingsModal from './firstTimeSettingsModal.vue';

export default {
  components: {
    generalSettings,
    // languageSettings,
    globalMessages,
    // firstTimeSettingsModal,
  },
  data() {
    return {
      tabIndex: 0,
      indexes: {
        // Indexes depend on the ORDER of the b-tab elements above. Starting from 0
        generalSettingsIdx: 0,
        // languageSettingsIdx: 1,
        globalMessagesIdx: 1,
      },
    };
  },
  computed: {
    ...mapGetters(['globalSettings', 'userPermissions']),
    pm() {
      if (!this.userPermissions) return undefined;

      const pm = new PermissionManager(this.userPermissions);
      return pm;
    },
    hasGeneralSettingsListPermission() {
      return this.pm && this.pm.hasPermission({ can: 'list-general-settings', forGroup: 'globalsettings' });
    },
    hasGlobalMessagesListPermission() {
      return this.pm && this.pm.hasPermission({ can: 'list-messages', forGroup: 'globalsettings' });
    },
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style lang="scss">
@import '../../styles/cs-styles.scss';

.nav-tabs .nav-link {
  color: #787f84;
  &:hover {
    color: #585f64;
  }
}

.active-tab-text {
  color: $coalesce_green !important;
}
</style>
