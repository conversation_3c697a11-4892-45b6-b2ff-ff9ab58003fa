<template>
  <div>
    <b-form @submit="onSubmit">
      <b-container fluid>
        <div v-for="(message, err, index) in global_messages" :key="index">
          <!--
                  <h5 class="mt-4">
                    {{ index + 1 }}: <span>{{ err }}:</span>
                  </h5>
                  -->
          <b-card-text class="mt-4 mb-0">
            <b
              >{{ index + 1 }}: <span>{{ err.replace(/(^|_)./g, s => s.replace('_', ' ').toUpperCase()) }}:</span>
            </b>
          </b-card-text>
          <b-card-text class="description">
            <b class="mr-2">Description: </b>
            <span>{{ message.description }}</span>
          </b-card-text>

          <b-row class="my-1" v-for="(text, lang) in message.content" :key="lang">
            <b-col sm="1">
              <label :for="`invalid-option-${lang}`"
                ><h6 class="cs-heading">{{ lang == 'eng' ? 'English' : lang == 'fra' ? 'French' : lang == 'ewe' ? 'Ewe' : lang }}:</h6>
              </label>
            </b-col>
            <b-col sm="11">
              <b-form-input
                :disabled="!hasGlobalSettingsEditPermission"
                v-model="message.content[lang]"
                :id="`${err}-${lang}`"
                type="text"
                aria-describedby="input-live-help input-live-feedback"
                @input="isValid(`${err}-${lang}`)"
              ></b-form-input>
              <b-form-invalid-feedback style="display:none" :id="`${err}-${lang}-feedback`">
                Enter at least 4 letters
              </b-form-invalid-feedback>
            </b-col>
          </b-row>
        </div>
        <div class="mt-4 ml-3 float-right">
          <span
            v-tooltip.top="
              !hasGlobalSettingsEditPermission
                ? {
                    content: 'Insufficient Permissions',
                    offset: 30,
                  }
                : !hasFormChanged
                ? {
                    content: 'Nothing on the page has changed',
                    offset: 30,
                  }
                : ''
            "
          >
            <button
              :disabled="isDisabled || !hasFormChanged || !hasGlobalSettingsEditPermission"
              class="submit-button btn cs-btn  px-4 mb-3"
              :class="[isDisabled || !hasFormChanged || !hasGlobalSettingsEditPermission ? 'cs-btn-inactive' : 'cs-btn-submit ']"
              type="submit"
              v-tooltip.left="{
                classes: 'mb-3 ' + tooltips.updateMessages.type,
                content: tooltips.updateMessages.content,
                trigger: 'manual',
                offset: 10,
                show: tooltips.updateMessages.visible,
              }"
            >
              Update Settings
            </button>
          </span>
        </div>
      </b-container>
    </b-form>
  </div>
</template>
<script>
/* eslint-disable import/no-unresolved */
import { hideTooltip, showTooltip } from '@/utils/tooltip-alerts';
import PermissionManager from '@/utils/PermissionManager';
import { mapGetters } from 'vuex';
import _ from 'lodash';

export default {
  data() {
    return {
      languages: ['English', 'French', 'Ewe'],
      global_messages: {},
      oldMessages: {},
      isDisabled: false,
      tooltips: {
        updateMessages: {
          visible: false,
          timeout: null,
          type: '',
          invisibleInSeconds: 0, // default is 0, will not clear the tooltip
          content: '',
        },
      },
      hasGlobalSettingsEditPermission: false,
    };
  },
  computed: {
    ...mapGetters(['userPermissions']),
    pm() {
      if (!this.userPermissions) return undefined;

      const pm = new PermissionManager(this.userPermissions);
      return pm;
    },
    hasFormChanged() {
      const equal = _.isEqual(this.global_messages, this.oldMessages);
      return !equal;
    },
  },
  async mounted() {
    // check if user can edit short codes on pre-production table

    this.hasGlobalSettingsEditPermission = this.pm && this.pm.hasPermission({ can: 'edit-messages', forGroup: 'globalsettings' });

    this.$store
      .dispatch('globalSettings/getGlobalMessages')
      .then(response => {
        // console.log("response", response.data.menu_code.global_messages);
        const globalMessages = JSON.stringify(response.data.menu_code.global_messages);

        this.global_messages = JSON.parse(globalMessages);
        this.oldMessages = JSON.parse(globalMessages);

        for (const ele in globalMessages) {
          const messages = globalMessages[ele].content;
          for (const i in messages) {
            const name = `${ele}_${i}`;
            this.oldMessages[name] = messages[i];
          }
        }
      })
      .catch(err => console.log('err in globalSettings/getGlobalMessages', err));
  },
  methods: {
    onSubmit(e) {
      this.isDisabled = true;
      // console.log("global_messages", this.global_messages);
      hideTooltip(this.tooltips.updateMessages);
      e.preventDefault();

      const postData = {
        menu_code: {
          root_menu: 'main_menu',
          menus: {
            main_menu: {
              content: [],
            },
          },
          messages: {},
          global_messages: this.global_messages,
          ussd_matches: [],
        },
        components: {},
        connectors: {},
      };
      // console.log("postData", postData);
      this.$store
        .dispatch('globalSettings/updateGlobalMessages', postData)
        .then(() => {
          this.oldMessages = JSON.parse(JSON.stringify(this.global_messages));
          showTooltip(this.tooltips.updateMessages, 'cs-success', 'Global Messages updated successfully', 5);
        })
        .catch(() => {});
    },
    isValid(id) {
      hideTooltip(this.tooltips.updateMessages);
      const elem = document.getElementById(id).value;
      const feedback = document.getElementById(`${id}-feedback`);

      // eslint-disable-next-line no-unused-expressions
      elem.length < 4 ? (feedback.style.display = 'block') : (feedback.style.display = 'none');

      // diable submit button if length of any input is less than 4 characters
      const globalMessages = this.global_messages;

      for (const ele in globalMessages) {
        const messages = globalMessages[ele].content;
        for (const i in messages) {
          if (messages[i].length < 4) {
            this.isDisabled = true;
            return;
          }
          this.isDisabled = false;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.cs-heading {
  color: #39b546;
}
.description {
  font-size: 14px;
  color: #666666;
}
</style>
