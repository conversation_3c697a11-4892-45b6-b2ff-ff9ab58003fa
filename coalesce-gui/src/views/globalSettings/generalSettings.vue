<template>
  <div>
    <b-form @submit.stop.prevent>
      <div v-if="showPageError === true">
        <h5 class="text-danger mt-5 ml-4">Server error, please contact your system administrator</h5>
      </div>
      <div v-else-if="newSettings.length > 0">
        <dl class="row d-flex">
          <template v-for="(setting, idx) in newSettings">
            <dt :key="idx + '0'" class="d-flex align-items-center mb-3 col-12" :class="!fullWidth ? 'col-md-3' : ''">
              <label :for="'feedback-' + idx" :title="setting.description" class="">{{ setting.name }}</label>
            </dt>
            <dd
              :key="idx + '1'"
              class="mb-3 col-12"
              :class="!fullWidth ? 'col-md-9' : ''"
              v-tooltip.top-start="{
                content: String(setting.value).length === 0 ? 'Cannot be empty' : setting.errorText,
                offset: 5,
                trigger: 'manual',
                classes: 'pl-2 cs-warning',
                show: !validSettingsDelay && !setting.valid && focussed,
              }"
            >
              <b-input
                :disabled="!hasEditPermission"
                @input="validateSettings()"
                :ref="setting.key"
                v-model="newSettings[idx].value"
                :key="setting.key"
                :class="{
                  '': setting.valid && oldSettings[idx].value !== setting.value,
                  'border-danger': !validSettings && !setting.valid,
                }"
              ></b-input>
            </dd>
          </template>
        </dl>
        <div class="d-flex justify-content-end">
          <button
            @click="hasEditPermission && !hasEmpty && validSettings && hasChanged && submit()"
            :disabled="!validSettings"
            class="btn cs-btn px-4"
            :class="!hasError && hasEditPermission && !hasEmpty && validSettings && hasChanged ? 'cs-btn-submit' : 'cs-btn-inactive'"
            v-tooltip="{ content: !hasError && hasEditPermission && !hasEmpty && validSettings && hasChanged ? '' : notSubmittableReason, offset: 8 }"
          >
            <span
              v-tooltip.left="{
                classes: tooltips.submit.type,
                content: tooltips.submit.content,
                trigger: 'manual',
                offset: 30,
                show: tooltips.submit.visible,
              }"
              >Save Settings</span
            >
          </button>
        </div>
      </div>
      <div v-else>
        <h5 class="text-info mt-5 ml-4">Loading...</h5>
      </div>
    </b-form>
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved, quotes, no-useless-escape */
// eslint-disable-next-line no-unused-vars
import { showTooltip, hideTooltip } from '@/utils/tooltip-alerts';
import { mapGetters } from 'vuex';
import PermissionManager from '@/utils/PermissionManager';
import '@/utils/common';
import _ from 'lodash';

export default {
  name: 'GeneralSettings',
  props: {
    firstTime: {
      type: Boolean,
      default() {
        return false;
      },
    },
    settings: {
      type: Array,
      default() {
        return [];
      },
    },
    focussed: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tooltips: {
        submit: {
          visible: false,
          timeout: null,
          type: 'cs-success',
          invisibleInSeconds: 2, // default is 0, will not clear the tooltip
          content: '',
        },
      },
      hasError: false,
      showPageError: false,
      validationTypes: [
        {
          nameContains: 'hostname',
          type: 'url',
          errorText: 'Needs to be a valid hostname',
        },
        {
          nameContains: 'port',
          type: 'port',
          errorText: 'Needs to be a number between 1 and 65535',
        },
        {
          nameContains: '.*',
          type: 'text',
          errorText: 'Minimum 3 characters',
        },
      ],
      oldSettings: [],
      newSettings: [],
      validSettings: true,
      validSettingsDelay: true,
      validSettingsTimeout: null,
      notSubmittableReason: '',
      ignoredSettingskeys: ['webhost', 'webport'],
    };
  },
  computed: {
    ...mapGetters(['userPermissions']),
    fullWidth() {
      return this.firstTime;
    },
    pm() {
      if (!this.userPermissions) return undefined;

      const pm = new PermissionManager(this.userPermissions);
      return pm;
    },
    hasListPermission() {
      return this.pm && this.pm.hasPermission({ can: 'list-general-settings', forGroup: 'globalsettings' });
    },
    hasEditPermission() {
      return this.pm && this.pm.hasPermission({ can: 'edit-general-settings', forGroup: 'globalsettings' });
    },
    hasEmpty() {
      let hasEmpty = false;

      this.newSettings.findIndex(setting => {
        if (String(setting.value).length === 0) {
          hasEmpty = true;
        }
        return hasEmpty;
      });

      return hasEmpty;
    },
    hasChanged() {
      return !_.isEqual(this.oldSettings, this.newSettings);
    },
  },
  created() {
    this.$store
      .dispatch('globalSettings/getGeneralSettings')
      .then(() => {
        const settings = [];
        this.settings.forEach(setting => {
          if (this.firstTime || !this.ignoredSettingskeys.includes(setting.key)) {
            settings.push(setting);
          }
        });
        this.newSettings = JSON.parse(JSON.stringify(settings, null, 2));

        if (this.hasListPermission !== true || !Array.isArray(this.newSettings) || this.newSettings.length === 0) {
          console.warn('missing permissions, OR newSettings is not an array, or has no data', this.hasListPermission, this.newSettings);
          this.showPageError = true;
          return;
        }

        this.showPageError = false;
        this.newSettings = this.newSettings.map(setting => {
          const idx = this.validationTypes.findIndex(check => {
            const validation = new RegExp(check.nameContains, 'i');
            return validation.test(setting.name);
          });
          const validation = this.validationTypes[idx];

          // eslint-disable-next-line no-param-reassign
          setting.valid = true; // assumes valid by default
          // eslint-disable-next-line no-param-reassign
          setting.validationType = validation.type;
          // eslint-disable-next-line no-param-reassign
          setting.errorText = validation.errorText;

          return setting;
        });

        this.oldSettings = JSON.parse(JSON.stringify(this.newSettings, null, 2));

        // INSERT DEFAULTS FOR WEB HOST AND PORT
        this.newSettings = this.newSettings.map(setting => {
          if (setting.name.match(/web.*port/i) && String(setting.value).length === 0) {
            // eslint-disable-next-line no-param-reassign
            setting.value = location.port || '80';
          }
          if (setting.name.match(/web.*hostname/i) && String(setting.value).length === 0) {
            // eslint-disable-next-line no-param-reassign
            setting.value = location.hostname;
          }

          return setting;
        });
      })
      .catch(err => {
        console.error('err in globalSettings/getGeneralSettings', err);
        this.showPageError = true;
      });
  },
  mounted() {
    console.debug('Mounted General Settings');
    if (!this.hasEditPermission) this.notSubmittableReason = 'Insufficient permissions';
  },
  methods: {
    validateSettings() {
      this.hasError = false;
      this.validSettings = true;

      if (this.validSettingsTimeout) {
        this.validSettingsDelay = true;
        clearTimeout(this.validSettingsTimeout);
      }

      this.newSettings = this.newSettings.map(setting => {
        const newSetting = setting;

        // start with assumption of validity
        newSetting.valid = true;

        if (setting.validationType === 'url') {
          // Hostname regex. Checks for (optional) http(s):// prefix and (optional) :port suffix
          if (!setting.value.match(/^([a-zA-Z0-9]+([\-][a-zA-Z0-9]+)*)(\.([a-zA-Z0-9]+([\-][a-zA-Z0-9]+)*)+)*(\:[0-9]{1,5})?$/i)) {
            newSetting.valid = false;
            this.validSettings = false;
          }
        } else if (setting.validationType === 'port') {
          const port = parseInt(setting.value, 10);
          if (!String(setting.value).match(/^[0-9]*$/) || port < 1 || port > 65535) {
            newSetting.valid = false;
            this.validSettings = false;
          }
        } else if (setting.value.length < 3) {
          newSetting.valid = false;
          this.validSettings = false;
        }

        return newSetting;
      });

      if (!this.hasEditPermission) this.notSubmittableReason = 'Insufficient permissions';
      else if (this.hasEmpty) this.notSubmittableReason = 'Please fill in empty fields';
      else if (!this.validSettings) this.notSubmittableReason = 'Please fix the problems first';
      else if (!this.hasChanged) this.notSubmittableReason = 'Nothing has changed';
      else this.notSubmittableReason = '';

      this.validSettingsTimeout = setTimeout(() => {
        this.validSettingsDelay = this.validSettings;
      }, 700);

      return this.validSettings;
    },
    submit() {
      const submitSettings = [];
      this.newSettings.forEach(setting => {
        const { key, name, description, value } = setting;
        submitSettings.push({ key, name, description, value });
      });

      hideTooltip(this.tooltips.submit);

      this.$store
        .dispatch('globalSettings/updateGeneralSettings', submitSettings)
        .then(result => {
          this.oldSettings = JSON.parse(JSON.stringify(this.newSettings, null, 2));
          showTooltip(this.tooltips.submit, 'cs-success', 'Updated Successfully');
          console.debug('Success?', result);
        })
        .catch(err => {
          this.hasError = true;
          showTooltip(this.tooltips.submit, 'cs-error', `Could not update settings: ${String(err)}`);
          console.debug('Failure :/ ?', err);
        });
    },
  },
};
</script>

<style lang="scss">
@import '../../styles/cs-styles.scss';
dd .form-control:focus {
  box-shadow: none;
}
</style>
<style lang="scss">
.input-error {
  & .tooltip-arrow {
    border-color: #dc3545 !important;
  }
  & .tooltip-inner {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
  }
}
</style>
