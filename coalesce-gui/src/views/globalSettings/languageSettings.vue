<template>
  <section class="content" id="evaluate" style="background-color:#fff; padding-bottom:50px;" v-cloak>
    <div class="subjects-content">
      <h3 class="subjects-trimester-title">Self Evaluation Page</h3>

      <div class="self-evaluation-rows">
        Number of Rows: &nbsp;
        <input v-model="number" type="number" min="1" max="10" name="rows" class="rows-textbox" number />
        <div class="btn btn-blueviolet btn-inline-block btn-create" @click="addRow(number)" v-show="rows.length < 10">Add Row</div>
      </div>

      <div class="box-body box-self-evaluation">
        <table id="example2" class="table table-hover table-striped sortable">
          <thead>
            <tr>
              <th>Language Code And Name</th>
              <th>Character Set</th>
              <th>Default</th>
              <th>CS Value</th>
              <th>Remove</th>
            </tr>
          </thead>

          <tbody>
            <tr v-for="(row, idx) in rows" :key="idx">
              <td>
                <select v-model="row.subject">
                  <option>- - - -</option
                  ><br />
                  <option value="ENGLISH">ENGLISH</option>
                  <option value="MATH">France</option>
                  <option value="EWE">EWE</option>
                </select>
              </td>
              <td>
                <select v-model="row.section">
                  <option>- - - -</option
                  ><br />
                  <option value="IP">IP</option>
                  <option value="IQ">IQ</option>
                </select>
              </td>
              <td>
                <select name="NAME THIS BASED ON THE SUBJECT CODE SELECTED VALUE" v-model="row.grade">
                  <option>- - - -</option
                  ><br />
                  <option value="A+">A+</option>
                  <option value="A">A</option>
                </select>
              </td>
              <td>
                <span>1</span>
              </td>
              <td>
                <button @click="onRemove(row)">Remove</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="confirmation-buttons-self-evaluation">
        <pre>{{ saveData }}</pre>
        <input type="hidden" name="_token" />
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'Evaluate',
  data() {
    return {
      number: 0,
      rows: [],
      saveData: null,
    };
  },
  mounted() {
    console.debug('Mounted Language Settings');
  },
  methods: {
    addRow(count) {
      let numRows = this.rows.length;

      if (numRows >= 10) return;

      for (let i = 1; i <= count; i++) {
        this.rows.push({});
        if (++numRows === 10) break;
      }
    },
    onRemove(row) {
      // console.log('this.rows', this.rows);
      console.log('row', row);
      // this.rows.$remove(row);
    },
  },
};
</script>
