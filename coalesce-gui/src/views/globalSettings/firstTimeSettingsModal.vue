<template>
  <b-modal
    v-if="globalSettings.generalSettings.length !== 0"
    id="settingsModal"
    ref="settingsModal"
    title="Welcome! Please configure your site settings"
    centered
    hide-footer
    :no-close-on-backdrop="true"
    @hide="modalHideEvent"
    @ok="modalHideEvent"
  >
    <template v-slot:modal-header>
      <div>
        <h4>Welcome!</h4>
        <h5>Before you begin,<br />Please take a moment to review your <span class="nowrap">site settings</span></h5>
      </div>
    </template>
    <p class="my-4 text-center">
      <general-settings :first-time="true" :settings="globalSettings.generalSettings" :focussed="true"></general-settings>
    </p>
  </b-modal>
</template>

<script>
import { mapGetters } from 'vuex';
// eslint-disable-next-line import/no-unresolved, no-unused-vars
import { showTooltip } from '@/utils/tooltip-alerts';
import generalSettings from './generalSettings.vue';

export default {
  name: 'SiteSettingsModal',
  components: {
    generalSettings,
  },
  data() {
    return {
      completed: false,
    };
  },
  computed: {
    ...mapGetters(['user', 'userPermissions', 'globalSettings']),
  },
  watch: {
    globalSettings: {
      deep: true,
      handler() {
        this.globalSettings.generalSettings.forEach(setting => {
          if (setting.name.match(/web.*hostname/i) && String(setting.value).length !== 0) {
            this.completed = true;
          }
        });

        if (this.completed === true) {
          setTimeout(() => {
            this.$refs.settingsModal.hide('ok');
          }, 2 * 1000);
        }
      },
    },
  },
  created() {},
  mounted() {
    console.debug('Mounted first time settings modal');
    this.$bvModal.show('settingsModal');
  },
  methods: {
    modalHideEvent(bvModalEvt) {
      if (bvModalEvt.trigger === 'ok') {
        this.$bvModal.hide('settingsModal');
        return;
      }
      bvModalEvt.preventDefault();
    },
  },
};
</script>
