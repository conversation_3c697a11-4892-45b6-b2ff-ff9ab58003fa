<template>
  <div>
    <div class="row" v-show="treestate === 'tree'">
      <div class="col-12">
        <b-link class="nav-link" :to="location">
          <i class="fa fa-chevron-right"></i>
          <span class="text">{{ sidebarlocation.title }}</span>
        </b-link>
      </div>
    </div>

    <main-dialog v-if="treestate !== 'tree'" :type="treestate" @menuadd="menuAddNode" @menuedit="menuEditNode"></main-dialog>

    <div class="row">
      <div
        :class="!hasMenuUpdatePermission ? 'd-inline' : 'd-none'"
        class="alert alert-warning text-dark border border-dark py-2 px-5"
        style="position: absolute; top: 1em; right: 0"
      >
        Read Only&nbsp;<info-tooltip placement="bottom" content="You do not have permission<br/>to edit modules"></info-tooltip>
      </div>

      <div :class="{ 'is-disabled': !hasMenuUpdatePermission }" class="col-12">
        <ussd-menu-editor
          v-show="treestate === 'tree'"
          :menu-config="menuConfig"
          :config="menuConfig"
          @context-action-menu="processContextAction"
          @edit-tree-node="editTreeNode"
        >
        </ussd-menu-editor>
      </div>
      <div class="col-2">
        <ussd-debugger :ussd-config="menuConfig"></ussd-debugger>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved */
import { mapGetters } from 'vuex';
import infoTooltip from '@/utils/components/info-tooltip.vue';
import UssdMenuEditor from '@/components/UssdMenuEditor/index.vue';
import UssdDebugger from '@/components/UssdDebugger/index.vue';
import MainDialog from '@/components/menuitems/MainDialog.vue';
// eslint-disable-next-line import/extensions
import PermissionManager from '@/utils/PermissionManager';

export default {
  name: 'UssdMenu',
  components: {
    infoTooltip,
    MainDialog,
    UssdDebugger,
    UssdMenuEditor,
  },
  data() {
    return {
      file: null,
      showMenu: true,
      showHeader: true,
      showDialog: true,
      menuConfig: {},
    };
  },
  computed: {
    ...mapGetters(['currentmodule', 'treestate', 'modules', 'treecontext', 'treecode', 'sidebarlocation', 'userPermissions']),
    view() {
      // debugger;
      return 'USSD Menu';
    },
    dialogType: {
      get() {
        return this.treestate;
      },
      set(value) {
        // debugger;
        this.$store.dispatch('menutree/opendialog', value);
        if (value === 'tree') {
          this.showMenu = true;
          this.showHeader = true;
          this.showDialog = false;
        } else {
          this.showMenu = false;
          this.showHeader = false;
          this.showDialog = true;
        }
      },
    },
    location() {
      return '#';
    },
    title() {
      return this.currentmodule.name;
    },
    pm() {
      if (!this.userPermissions) return undefined;

      const pm = new PermissionManager(this.userPermissions);
      return pm;
    },
    hasMenuUpdatePermission() {
      return this.pm && this.pm.hasPermission({ can: 'update', forGroup: 'menu' });
    },
  },
  mounted() {},
  methods: {
    processContextAction(menuConfig, treeNode, menuSelection) {
      switch (menuSelection.item.mode) {
        case 'delete':
          this.$store.dispatch('modules/deleteitem', { nodeId: treeNode.id, moduleId: this.$route.params.moduleId });
          break;
        case 'after':
          // debugger
          this.dialogType = menuSelection.item.type.type;
          this.$store.dispatch('menutree/opendialog', menuSelection.item.type.type);
          this.$store.dispatch('menutree/setcontext', {
            action: 'create',
            type: menuSelection.item.type.type,
            treeNode,
            menuSelection,
            mode: 'add',
            data: {},
          });
          break;
        case 'insert':
          // debugger
          this.dialogType = menuSelection.item.type.type;
          this.$store.dispatch('menutree/opendialog', menuSelection.item.type.type);
          this.$store.dispatch('menutree/setcontext', {
            action: 'create',
            type: menuSelection.item.type.type,
            treeNode,
            menuSelection,
            mode: 'add',
            data: {},
          });
          break;
        default:
          break;
      }
      // debugger
      // console.log(treeNode);
    },
    editTreeNode(event, params) {
      // debugger
      // alert(JSON.stringify(params.node, null, 3))
      this.$store.dispatch('menutree/setcontext', {
        action: 'edit',
        node: params.node,
      });
      this.$store.dispatch('menutree/editdialog', {
        type: params.node.type,
        nodeId: params.node.id,
        moduleId: this.$route.params.moduleId,
      });
    },
    menuAddNode(data, type) {
      this.$store.dispatch('modules/addmenunode', {
        type: type,
        context: {
          data: this.treecontext.data,
          node: this.treecontext.treeNode,
          position: this.treecontext.menuSelection.position,
          item: this.treecontext.menuSelection.item,
          moduleId: this.$route.params.moduleId,
        },
      });
    },
    menuEditNode(data, type) {
      // debugger;
      this.$store.dispatch('modules/editmenunode', {
        type: type,
        data: this.treecontext.data,
        moduleId: this.$route.params.moduleId,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.ussdmenu {
  &-container {
    margin: 30px;
  }
  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}

.fixed-top {
  z-index: 3000;
}

.marvel-device.iphone5s,
.marvel-device.iphone5c {
  top: 100px;
  right: 20px;
  position: fixed;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.45) !important;
}

.submit-button {
  margin: 30px 0 0 20px;
}
.is-disabled {
  opacity: 0.7;
  pointer-events: none;
}
</style>
