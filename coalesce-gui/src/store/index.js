import Vue from 'vue';
import Vuex from 'vuex';
import getters from './getters';
import app from './modules/app';
import settings from './modules/settings';
import user from './modules/user';
import modules from './modules/modules';
import menutree from './modules/menutree';
import ussdmenu from './modules/ussdMenu';
import component from './modules/component';
// import connector from './modules/connector';
import connectorEndpoint from './modules/connector-endpoint';
import launchpad from './modules/launchpad';
import globalSettings from './modules/global-settings';
import elasticSearch from './modules/elasticsearch';

Vue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    app,
    settings,
    user,
    modules,
    menutree,
    ussdmenu,
    component,
    //    connector,
    connectorEndpoint,
    launchpad,
    globalSettings,
    elasticSearch,
  },
  getters,
});

export default store;
