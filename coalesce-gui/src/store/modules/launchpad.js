/* eslint-disable no-empty-pattern */
/* eslint-disable import/no-unresolved */
import { launch, services, updateUssdCodes } from '@/api/launchpad';

const state = {
  components: [],
};

const mutations = {
  //  SET_COMPONENTS: (state, comps) => {
  //         state.components = comps;
  //     }
};

const actions = {
  services({}, params) {
    // debugger;
    return new Promise((resolve, reject) => {
      services({ params: params })
        .then(response => {
          //      response.data.preprod.data.forEach(item => {
          //     if (item.predate)  item.predate = moment(item.predate).add((params.tz_offset), 'm').format("YYYY-MM-DD HH:mm");
          //     if (item.proddate) item.proddate = moment(item.proddate).add((params.tz_offset), 'm').format("YYYY-MM-DD HH:mm");
          //  });
          // response.data.prod.data.forEach(item => {
          //     if (item.predate)  item.predate = moment(item.predate).add((params.tz_offset), 'm').format("YYYY-MM-DD HH:mm");
          //     if (item.proddate) item.proddate = moment(item.proddate).add((params.tz_offset), 'm').format("YYYY-MM-DD HH:mm");
          //  });
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  launch({}, params) {
    // debugger;
    return new Promise((resolve, reject) => {
      launch({ params: params })
        .then(response => {
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  updateUssdCodes({}, params) {
    // debugger;
    return new Promise((resolve, reject) => {
      updateUssdCodes({ params: params })
        .then(response => {
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
