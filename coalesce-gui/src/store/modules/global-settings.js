/* eslint-disable no-empty-pattern */
/* eslint-disable import/no-unresolved */
import { getGlobalMessages, updateGlobalMessages, getGeneralSettings, updateGeneralSettings } from '@/api/global-settings';

const state = {
  globalSettings: {
    generalSettings: [],
    globalMessages: [],
  },
};

const mutations = {
  SET_GENERAL_SETTINGS: (state, settings) => {
    state.globalSettings.generalSettings = settings;
  },
  SET_GLOBAL_MESSAGES: (state, messages) => {
    state.globalSettings.globalMessages = messages;
  },
};

const actions = {
  getAll({ dispatch }) {
    return new Promise((resolve, reject) => {
      dispatch('getGeneralSettings')
        .then(() => resolve({}))
        .catch(() => reject({}));
      dispatch('getGlobalMessages')
        .then(() => resolve({}))
        .catch(() => reject({}));
    });
  },
  getGeneralSettings({ commit }) {
    return new Promise((resolve, reject) => {
      getGeneralSettings()
        .then(response => {
          if (response.ok && Array.isArray(response.data) && response.data.length > 0) {
            commit('SET_GENERAL_SETTINGS', response.data);
            resolve(response.data);
          } else reject(new Error('Unable to retrieve general'));
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  updateGeneralSettings({ commit }, data) {
    // console.log("data", data);
    return new Promise((resolve, reject) => {
      updateGeneralSettings(data)
        .then(response => {
          if (response.ok === true) {
            commit('SET_GENERAL_SETTINGS', data);
            resolve(response);
          } else {
            reject(new Error('Problem updating general settings :/'));
          }
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  updateGlobalMessages({}, data) {
    // console.log("data", data);
    return new Promise((resolve, reject) => {
      updateGlobalMessages(data)
        .then(response => {
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  getGlobalMessages({ commit }) {
    return new Promise((resolve, reject) => {
      getGlobalMessages()
        .then(response => {
          if (response.code === 20000 && response.data && response.data.menu_code && response.data.menu_code.global_messages) {
            commit('SET_GLOBAL_MESSAGES', response.data.menu_code.global_messages);
          }
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
