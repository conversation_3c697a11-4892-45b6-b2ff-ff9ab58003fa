/* eslint-disable import/no-unresolved */
import { updatemenucode, create, list, listall, getmodule, addmenunode, edititem, deleteitem, renameModule, deleteModule } from '@/api/modules';

const state = {
  modules: [],
  currentmodule: {},
  menucode: {},
};

const mutations = {
  SET_MENU_MODULES: (state, menus) => {
    state.modules = menus;
  },
  UPDATE_MODULE_NAME: (state, data) => {
    if (data.responseId === 1) {
      for (let i = 0; i < state.modules.length; i++) {
        if (state.modules[i].id === data.id) {
          state.modules[i].name = data.newName;
        }
      }
    }
  },
  SET_CURRENT_MODULE: (state, menu) => {
    state.currentmodule = menu;
  },
  ADD_MENU_MODULE: (state, menu) => {
    state.modules.push(menu);
  },
  SET_MENU_CODE: (state, code) => {
    let menucode = code;
    if (typeof menucode === 'string') {
      menucode = JSON.parse(menucode);
    }
    if (Object.keys(menucode).length === 0) {
      // state.menucode = {text: 'Right click here to add your first node', id: -1, type: 'root', state: {selected: true}}
      state.menucode = [
        'service',
        {
          // text: 'Right click here to add your first node',
          // name: 'Right click here to add your first node',
          text: '',
          name: '',
          id: -1,
          type: 'service',
          state: { selected: true },
        },
        '',
      ];
    } else {
      state.menucode = menucode;
    }
  },
  SET_MODULE_BY_ID: (state, id) => {
    for (let i = 0; i < state.modules.length; i++) {
      if (parseInt(state.modules[i].id, 10) === parseInt(id, 10)) {
        state.currentmodule = state.modules[i];
        break;
      }
    }
  },
  DELETE_MODULE: (state, id) => {
    state.modules = state.modules.filter(item => item.id !== id);
  },
};

const actions = {
  create({ commit }, params) {
    // debugger;
    return new Promise((resolve, reject) => {
      create({ params: params })
        .then(response => {
          commit('SET_CURRENT_MODULE', response.data);
          commit('ADD_MENU_MODULE', response.data);
          resolve(response.data);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  listall() {
    return new Promise((resolve, reject) => {
      // TODO will need to pass user id
      const userId = 1;
      listall(userId)
        .then(response => {
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  list({ commit }) {
    return new Promise((resolve, reject) => {
      // TODO will need to pass user id
      const userId = 1;
      list(userId)
        .then(response => {
          commit('SET_MENU_MODULES', response.data);
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  setcurrent({ commit }, params) {
    commit('SET_MODULE_BY_ID', params.moduleId);
  },
  selectmodule({ commit }, params) {
    return new Promise((resolve, reject) => {
      getmodule({
        userId: 1,
        moduleId: params.moduleId,
      })
        .then(response => {
          // debugger
          commit('SET_MENU_CODE', response.data.code);
          resolve(response.data);
          // debugger
          /*
          if (Array.isArray(response.data) && response.data.length > 0) {
              commit("SET_MENU_CODE", response.data[0].code);
              resolve(response.data[0])
          }
          else reject('Invalid menu code');
          */
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  updatemenucode({ commit }, params) {
    return new Promise((resolve, reject) => {
      // debugger;
      updatemenucode(params)
        .then(response => {
          // TODO fix returned code.
          // debugger;
          commit('SET_MENU_CODE', response.menutree);
          resolve(response.menutree);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  rename({ commit }, params) {
    return new Promise((resolve, reject) => {
      renameModule(params)
        .then(response => {
          if (response.ok) commit('UPDATE_MODULE_NAME', response.data);
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  delete({ commit }, params) {
    return new Promise((resolve, reject) => {
      deleteModule(params)
        .then(response => {
          console.log('response', response);
          if (response.code === 20000 && response.affectedRows === 1) commit('DELETE_MODULE', response.module.id);
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  addmenunode({ commit }, params) {
    return new Promise((resolve, reject) => {
      // debugger;
      addmenunode(params)
        .then(response => {
          // TODO fix returned code.
          // debugger;
          commit('SET_MENU_CODE', response.menutree);
          resolve(response.menutree);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  editmenunode({ commit }, params) {
    return new Promise((resolve, reject) => {
      // debugger;
      edititem(params)
        .then(response => {
          // TODO fix returned code.
          // debugger;
          commit('SET_MENU_CODE', response.menutree);
          resolve(response.menutree);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  deleteitem({ commit }, params) {
    return new Promise((resolve, reject) => {
      // debugger;
      deleteitem(params)
        .then(response => {
          // TODO fix returned code.
          // debugger;
          commit('SET_MENU_CODE', response.menutree);
          resolve(response.menutree);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
