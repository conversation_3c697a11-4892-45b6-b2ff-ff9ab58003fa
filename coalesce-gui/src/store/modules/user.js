/* eslint-disable no-empty-pattern */
/* eslint-disable import/no-unresolved */
import { login, logout, getInfo, updateUser, inviteUser, listUsers, deleteUser, forgotten, getUmmsVersion } from '@/api/user';
import { listRoles, createRole, updateRole, listPermissions, deleteRole } from '@/api/role';
import { resetRouter } from '@/router';
import { setRememberToken, getToken, getTokenExpiry, setToken, setTokenExpiry, removeToken, removeRememberToken } from '@/utils/auth';

const state = {
  token: getToken(),
  tokenExpiry: getTokenExpiry(),
  name: '',
  email: '',
  picture: '',
  // ---- INFO is a JSON object (retrieved as a STRING from the database and PARSED into JSON here),
  //      it stores variable hot-swappable information about the user... for example, OTP for the first-time login.
  info: {},
  role: [],
  user: {},
};

const mutations = {
  SET_USER: (state, user) => {
    state.user = user;
  },
  SET_TOKEN: (state, token) => {
    state.token = token;
  },
  SET_TOKEN_EXPIRY: (state, tokenExpiry) => {
    state.tokenExpiry = tokenExpiry;
  },
  SET_NAME: (state, name) => {
    state.name = name;
  },
  SET_EMAIL: (state, email) => {
    state.email = email;
  },
  SET_INFO: (state, info) => {
    for (const key in state.info) state.info[key] = info[key];
  },
  SET_ROLE: (state, role) => {
    role.forEach((value, index) => {
      state.role[index] = role[index];
    });
  },
  SET_PICTURE: (state, picture) => {
    state.picture = picture;
  },
};

const actions = {
  // eslint-disable-next-line no-empty-pattern
  forgotten({}, setData) {
    return new Promise((resolve, reject) => {
      forgotten(setData)
        .then(res => {
          resolve(res);
        })
        .catch(err => {
          reject(err);
        });
    });
  },
  // ----------
  // ---- Called with .dispatch("user/login") - see views/login.js - where 'user' is the module we are in now... and 'login' is the method here
  //
  login({ commit }, loginData) {
    // ---- TOKEN REFRESH -- no uname/pword combination
    //      will refresh the EXPIRY
    if (typeof loginData.username === 'undefined') {
      return new Promise((resolve, reject) => {
        login(loginData)
          .then(resp => {
            const { expires, token, rememberToken } = resp.data;

            commit('SET_TOKEN_EXPIRY', expires);
            commit('SET_TOKEN', token);
            setToken(token);
            setTokenExpiry(expires);

            const resolveData = {
              ok: true,
              newExpiry: expires,
              newToken: token,
            };

            if (rememberToken) {
              // ---- we renewed the token using a 'remember token', so we should assign this...
              setRememberToken(rememberToken);
              //      ... and the renewed login token in order to allow a successful 'automated' login
              resolveData.rememberToken = rememberToken;
            }

            resolve(resolveData);
          })
          .catch(err => {
            reject(err.message || err);
          });
      });
    }

    const { username, password } = loginData;
    let { remember, resetRequest } = loginData;

    remember = typeof remember === 'boolean' ? remember : false;
    resetRequest = typeof resetRequest === 'boolean' ? resetRequest : false;

    return new Promise((resolve, reject) => {
      // this 'login' method is imported from api/user.js (see imports at the top of the file)
      login({ username: username.trim(), password, remember, resetRequest })
        .then(response => {
          const { data } = response;

          if (data.rememberToken) setRememberToken(data.rememberToken);

          // debugger;
          const email = username.trim();
          commit('SET_TOKEN', data.token);
          commit('SET_TOKEN_EXPIRY', data.expires);
          // by now we've logged in successfully, this is a valid username(email)
          commit('SET_EMAIL', email);

          setToken(data.token);
          setTokenExpiry(data.expires);

          resolve(data.user);
        })
        .catch(error => {
          // ---- NOTE: if 'forgotPassToken' is TRUE.... then this WILL reject, because it will be caught as a "successful" internal action
          //      The purpose of the rejection, is that we will NOT login on a password reset request, so it is correctly "caught" here
          reject(error);
        });
    });
  },

  // DELETE user  (delete)
  deleteUser({}, deleteData) {
    return new Promise((resolve, reject) => {
      deleteUser(deleteData)
        .then(response => {
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  // PUT user info (update)
  updateUser({ commit, state }, updateData) {
    // ---- Let's set the new state based on the updateData (new state), otherwise the previous state
    //      Ignore the password of course
    const newUserState = {};
    for (const key in state.user) {
      if (key !== 'password') {
        if (updateData[key]) newUserState[key] = updateData[key];
        // ---- explicitly exclude ID and EMAIL, these MUST be set in the 'updateData'
        //      if not, then we expect (and want) the 'updateUser' method to fail
        else if (key !== 'id' && key !== 'email') newUserState[key] = state.user[key];
      }
    }
    return new Promise((resolve, reject) => {
      updateUser(updateData)
        .then(response => {
          if (updateData.picture) {
            commit('SET_PICTURE', updateData.picture);
            newUserState.picture = updateData.picture;
          }
          commit('SET_USER', newUserState);

          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  // PUT user info (update)
  inviteUser({}, inviteData) {
    return new Promise((resolve, reject) => {
      inviteUser(inviteData)
        .then(response => {
          console.debug('*** response/ store/module/user.js -> inviteUser() ***\n', response);
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  // list users
  listUsers() {
    return new Promise((resolve, reject) => {
      listUsers()
        .then(response => {
          // console.log('response11111111111111111111111', response)
          console.debug('*** response/ store/module/user.js -> listUsers() ***\n', response);
          resolve(response);
        })
        .catch(error => {
          console.log('error', error);
          reject(error);
        });
    });
  },
  // list roles
  listRoles({}, ummsVersion) {
    return new Promise((resolve, reject) => {
      listRoles(ummsVersion)
        .then(response => {
          console.debug('*** response/ store/module/user.js -> listRoles() ***\n', response);
          resolve(response);
        })
        .catch(error => {
          console.error('error', error);
          reject(error);
        });
    });
  },
  // list Permissions
  listPermissions({}, ummsVersion) {
    console.debug('listPermissions() -> ummsVersion', ummsVersion);
    return new Promise((resolve, reject) => {
      listPermissions(ummsVersion)
        .then(response => {
          console.debug('*** response/ store/module/user.js -> listPermissions() ***\n', response);
          resolve(response);
        })
        .catch(error => {
          console.error('error', error);
          reject(error);
        });
    });
  },
  // create Role
  createRole({}, roleData) {
    console.debug('createRole() -> roleData', roleData);
    return new Promise((resolve, reject) => {
      createRole(roleData)
        .then(response => {
          console.debug('*** response/ store/module/user.js -> createRole() ***\n', response);
          resolve(response);
        })
        .catch(error => {
          console.error('error', error);
          reject(error);
        });
    });
  },
  // update Role
  updateRole({}, roleData) {
    console.debug('updateRole() -> roleData', roleData);
    return new Promise((resolve, reject) => {
      updateRole(roleData)
        .then(response => {
          console.debug('*** response/ store/module/user.js -> updateRole() ***\n', response);
          resolve(response);
        })
        .catch(error => {
          console.error('error', error);
          reject(error);
        });
    });
  },
  // delete Role
  deleteRole({}, role) {
    // console.log("role", role);
    return new Promise((resolve, reject) => {
      deleteRole(role)
        .then(response => {
          console.debug('*** response/ store/module/user.js -> deleteRole() ***\n', response);
          resolve(response);
        })
        .catch(error => {
          console.error('error', error);
          reject(error);
        });
    });
  },

  // get user info
  getInfo({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      const token = getToken();
      if (typeof token === 'string') {
        commit('SET_TOKEN', token);
        getInfo(state.email)
          .then(response => {
            // debugger;
            if (!response.data) {
              reject('Verification failed, please Login again.');
            }

            const { firstname, lastname, picture, email, info, role } = response.data;

            commit('SET_NAME', `${firstname} ${lastname}`);
            commit('SET_PICTURE', picture);
            commit('SET_EMAIL', email);
            commit('SET_INFO', info);
            commit('SET_ROLE', role);
            commit('SET_USER', response.data);

            dispatch('globalSettings/getGeneralSettings', {}, { root: true })
              .then(() => {
                resolve();
              })
              .catch(err => {
                reject(new Error('Error inside successful getInfo(), during getGeneralSettings() request -> ', err.message || err));
              });
          })
          .catch(error => {
            reject(new Error('Error in getInfo() -> ', error));
          });
      } else {
        // reject("Verification failed, please Login again.");
      }
    });
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout(state.token)
        .then(() => {
          commit('SET_TOKEN', '');
          // ---- Reset NAME because this is used to check if there is user info
          //      If logged out... We want to forcibly get the user info again
          commit('SET_NAME', '');
          commit('SET_USER', {});
          removeRememberToken();
          removeToken();
          resetRouter();
          resolve();
        })
        .catch(error => {
          reject(new Error('Error in logout() -> ', error));
        });
    });
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '');
      removeToken();
      resolve();
    });
  },

  // get User-management microservice Version
  getUmmsVersion({}) {
    return new Promise((resolve, reject) => {
      getUmmsVersion()
        .then(response => {
          //   console.log('getUmmsVersion response', response)
          //   console.debug("*** response/ store/module/user.js -> getUmmsVersion() ***\n", response);
          resolve(response);
        })
        .catch(error => {
          reject(new Error('Error in getUmmsVersion() -> ', error));
        });
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
