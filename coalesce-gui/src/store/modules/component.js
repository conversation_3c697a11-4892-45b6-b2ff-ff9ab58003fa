/* eslint-disable no-empty-pattern */
/* eslint-disable import/no-unresolved */
import { create, list, getcomponent, setcomponent, addLibrary, removeLibrary } from '@/api/component';

const state = {
  components: [],
};

const mutations = {
  SET_COMPONENTS: (state, comps) => {
    state.components = comps;
  },
  ADD_COMPONENT: (state, comp) => {
    state.components = [...state.components, comp];
  },
};

const actions = {
  addLibrary({}, params) {
    // debugger;
    return new Promise((resolve, reject) => {
      addLibrary({ params: params })
        .then(response => {
          resolve(response.data);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  removeLibrary({}, params) {
    // debugger;
    return new Promise((resolve, reject) => {
      removeLibrary({ params: params })
        .then(response => {
          resolve(response.data);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  create({ commit }, params) {
    // debugger;
    return new Promise((resolve, reject) => {
      create({ params: params })
        .then(response => {
          // getting newly created component from database and adding it to vuex store
          getcomponent({ params: { componentId: response.data.id } })
            .then(componentResponse => {
              commit('ADD_COMPONENT', componentResponse.data.comp[0]);
            })
            .catch(error => {
              reject(error);
            });
          resolve(response.data);
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  list({ commit }, params) {
    return new Promise((resolve, reject) => {
      // debugger
      list({ params: params })
        .then(response => {
          commit('SET_COMPONENTS', response.data.components);
          resolve(response.data.components);
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  getcomponent({}, params) {
    return new Promise((resolve, reject) => {
      // debugger
      getcomponent({ params: params })
        .then(response => {
          resolve(response.data);
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  setcomponent({}, params) {
    return new Promise((resolve, reject) => {
      // debugger
      setcomponent({ params: params })
        .then(response => {
          resolve(response.data.comp);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
