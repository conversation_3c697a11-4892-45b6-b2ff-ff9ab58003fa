/* eslint-disable no-empty-pattern */
/* eslint-disable import/no-unresolved */
import { create, list, get, update } from '@/api/connector';

const state = {
  connectors: [],
};

const mutations = {
  SET_CONNECTORS: (state, connectors) => {
    state.connectors = connectors;
  },
  ADD_CONNECTOR: (state, connector) => {
    state.connectors = [...state.connectors, connector];
  },
  SET_CONNECTOR: (state, params) => {
    const { connectorId, connector } = params;
    // eslint-disable-next-line array-callback-return
    state.connectors.map(stateConnector => {
      if (stateConnector.id === connectorId) {
        const { description, data } = connector;
        // eslint-disable-next-line no-param-reassign
        stateConnector.description = description;
        // eslint-disable-next-line no-param-reassign
        stateConnector.data = JSON.stringify(data);
      }
    });
  },
};

const actions = {
  create({ commit }, params) {
    return new Promise((resolve, reject) => {
      create({ params })
        .then(response => {
          // ---- getting newly created connector from database and adding it to vuex store
          // Must get it from DB so that we can get the database ID used for the connector
          get({ connectorId: response.data.id })
            .then(getResponse => {
              const { connector } = getResponse.data;
              commit('ADD_CONNECTOR', connector);
              resolve(connector);
            })
            .catch(error => {
              reject(error);
            });
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  list({ commit }, params) {
    return new Promise((resolve, reject) => {
      // debugger
      list({ params: params })
        .then(response => {
          commit('SET_CONNECTORS', response.data.connectors);
          resolve(response.data.connectors);
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  get({}, params) {
    return new Promise((resolve, reject) => {
      // debugger
      const { connectorId } = params;
      get({ connectorId })
        .then(response => {
          const { connector } = response.data;
          // ---- temporary code to repair MISSING information in connectors
          connector.type = connector.type ? connector.type : 'HTTP';
          if (connector.type === 'HTTP') {
            connector.data.auth = connector.data.auth ? connector.data.auth : { type: 'none', username: '', password: '' };
            connector.data.headers = connector.data.headers ? connector.data.headers : [];
          }
          resolve(connector);
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  update({ commit }, params) {
    return new Promise((resolve, reject) => {
      // debugger
      update({ params })
        .then(response => {
          // successfully set the connector... let's update the local connectors
          commit('SET_CONNECTOR', params);
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
