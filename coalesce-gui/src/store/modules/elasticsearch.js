/* eslint-disable no-empty-pattern */
/* eslint-disable import/no-unresolved */
import { search, fields } from '@/api/elasticsearch';

const state = {};
const mutations = {};
const actions = {
  search({}, params) {
    return new Promise((resolve, reject) => {
      search(params)
        .then(response => {
          resolve(response);
        })
        .catch(error => {
          reject(new Error('Error in elasticsearch.search() -> ', error));
        });
    });
  },
  fields({}, params) {
    return new Promise((resolve, reject) => {
      fields(params)
        .then(response => {
          resolve(response);
        })
        .catch(error => {
          reject(new Error('Error in elasticsearch.search() -> ', error));
        });
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
