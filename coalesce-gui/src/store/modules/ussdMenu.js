/* eslint-disable no-empty-pattern */
/* eslint-disable import/no-unresolved */
import { push, command, loadconfig } from '@/api/ussdmenu';

const state = {};

const mutations = {};

const actions = {
  push({}, params) {
    return new Promise((resolve, reject) => {
      push(params)
        .then(response => {
          // Code here in case you want to SAVE STATE or similar
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  command({}, params) {
    return new Promise((resolve, reject) => {
      command(params)
        .then(response => {
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  loadconfig({}, params) {
    return new Promise((resolve, reject) => {
      loadconfig(params)
        .then(response => {
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
