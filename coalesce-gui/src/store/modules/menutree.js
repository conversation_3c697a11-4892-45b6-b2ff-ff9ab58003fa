/* eslint-disable import/no-unresolved */
import { gettreenode } from '@/api/menutree';

const state = {
  treestate: 'tree',
  context: {},
};

const mutations = {
  OPEN_DIALOG: (state, name) => {
    state.treestate = name;
  },
  CLOSE_DIALOG: state => {
    state.treestate = 'tree';
  },
  SET_CONTEXT: (state, ctxt) => {
    state.context = ctxt;
  },
};

const actions = {
  editdialog({ commit }, params) {
    return new Promise((resolve, reject) => {
      // debugger
      gettreenode(params)
        .then(response => {
          // debugger
          const context = {
            data: response.data,
            mode: 'edit',
          };
          commit('SET_CONTEXT', context);
          commit('OPEN_DIALOG', response.data.type);
          resolve(context);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  opendialog({ commit }, name) {
    commit('OPEN_DIALOG', name);
  },
  closedialog({ commit }) {
    commit('CLOSE_DIALOG');
  },
  setcontext({ commit }, ctxt) {
    // debugger
    commit('SET_CONTEXT', ctxt);
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
