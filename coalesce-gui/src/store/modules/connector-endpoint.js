/* eslint-disable no-empty-pattern */
/* eslint-disable import/no-unresolved */
import _ from 'lodash';
import { create, list, get, update, remove } from '@/api/connector_endpoints';

const POLL_INTERVAL = 40 * 1000;

const state = {
  pollInterval: null,
  connectorEndpoints: [
    /*
    {
      type: 'hux',
      endpoints: [
        {
          id: 1,
          name: 'Smartshop',
          description: 'Hux Endpoint for Smartshop',
          preprod: {
            hostname: 'preprod-hostname',
            port: 4021,
            url: '/',
            timeout: 1000,
          },
          prod: {
            hostname: 'preprod-hostname',
            port: 4021,
            url: '/',
            timeout: 1000,
          },
        }
      ]
    }
    */
  ],
};

const mutations = {
  SET_POLL_INTERVAL: (state, newInterval) => {
    state.pollInterval = newInterval;
  },
  SET_CONNECTOR_ENDPOINTS: (state, connectorEndpoints) => {
    state.connectorEndpoints = connectorEndpoints;
  },
  REMOVE_ENDPOINT: (state, endpointId) => {
    console.warn('REMOVE_ENDPOINT mutation untested! Exiting.');
    return;
    // eslint-disable-next-line no-unreachable
    let endpointIndex;
    const typeIndex = state.connectorEndpoints.findIndex(connectorEndpoint => {
      const { endpoints } = connectorEndpoint;
      // debugger;
      endpointIndex = endpoints.findIndex(endpoint => {
        // debugger;
        return endpoint.id === endpointId;
      });
      return endpointIndex !== -1;
    });

    // eslint-disable-next-line no-unreachable
    if (typeIndex === -1 || endpointIndex === -1) {
      // debugger;
      return;
    }

    // debugger;
    delete state.connectorEndpoints[typeIndex].endpoints[endpointId];
  },
  SET_ENDPOINT: (state, updatedEndpoint) => {
    let endpointIndex;
    const typeIndex = state.connectorEndpoints.findIndex(connectorEndpoint => {
      const { endpoints } = connectorEndpoint;
      endpointIndex = endpoints.findIndex(endpoint => {
        return endpoint.id === updatedEndpoint.id;
      });

      return endpointIndex !== -1;
    });

    state.connectorEndpoints[typeIndex].endpoints[endpointIndex] = updatedEndpoint;
  },
  ADD_ENDPOINT: (state, newEndpoint) => {
    const { type } = newEndpoint;
    const typeIndex = state.connectorEndpoints.findIndex(connectorEndpoint => {
      return connectorEndpoint.connector_type === type;
    });

    if (typeIndex === -1) {
      console.error('Cannot store connector endpoint, no associated connector type!', newEndpoint);
      return;
    }

    state.connectorEndpoints[typeIndex].endpoints.push(newEndpoint);
  },
};

const actions = {
  create({ commit }, newEndpoint) {
    return new Promise((resolve, reject) => {
      create(newEndpoint)
        .then(response => {
          const { endpoint } = response;
          console.debug('created Connector Endpoint:\n', endpoint);
          commit('ADD_ENDPOINT', endpoint);
          resolve(endpoint);
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  list({ state, commit }) {
    return new Promise((resolve, reject) => {
      // if this request is made --- (re)start the poll interval
      console.debug(`Setting connector endpoint poll to run every ${POLL_INTERVAL / 1000} seconds. Will update endpoints in Store.`);
      clearInterval(state.pollInterval);
      const pollInterval = setInterval(() => {
        console.debug('Polling to check for changes in connector endpoints...');
        list()
          .then(response => {
            const connectorEndpointsBefore = [];
            const connectorEndpointsAfter = [];
            state.connectorEndpoints.forEach(connector => {
              const type = connector.connector_type;
              connector.endpoints.forEach(endpoint => {
                connectorEndpointsBefore.push(`${type}.${endpoint.name}`);
              });
            });
            response.connectorEndpoints.forEach(connector => {
              const type = connector.connector_type;
              connector.endpoints.forEach(endpoint => {
                connectorEndpointsAfter.push(`${type}.${endpoint.name}`);
              });
            });

            if (!_.isEqual(connectorEndpointsBefore, connectorEndpointsAfter)) {
              console.debug('connector endpoints changed for sidebar menu ... updating store', response.connectorEndpoints);
              commit('SET_CONNECTOR_ENDPOINTS', response.connectorEndpoints);
            }
          })
          .catch(error => {
            clearInterval(state.pollInterval);
            console.error('Stopped endpoint list interval');
            // clearInterval(state.pollInterval);
            if (String(error.message).match(/403/)) {
              console.error('Reason: Unauthenticated');
            } else {
              console.error('Unknown reason, error:\n', error.message || error);
            }
          });
      }, POLL_INTERVAL);

      commit('SET_POLL_INTERVAL', pollInterval);

      // get endpoints NOW. poll will finish on it's own accord in "POLL_INTERVAL" time, we do not want to wait that long to return the data
      list()
        .then(response => {
          commit('SET_CONNECTOR_ENDPOINTS', response.connectorEndpoints);
          resolve(response.connectorEndpoints);
        })
        .catch(error => {
          console.debug('POLLING for connector endpoints. ERROR:\n', error.message || error);
          reject(error);
        });
    });
  },

  get({}, endpointId) {
    return new Promise((resolve, reject) => {
      get(endpointId)
        .then(response => {
          const { endpoint } = response;

          resolve(endpoint);
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  update({ commit }, endpointToUpdate) {
    return new Promise((resolve, reject) => {
      update(endpointToUpdate)
        .then(response => {
          const { endpoint } = response;
          // successfully updated the connector endpoint... let's update the store
          commit('SET_ENDPOINT', endpoint);
          resolve(endpoint);
        })
        .catch(error => {
          reject(error);
        });
    });
  },

  remove({ commit }, endpointId) {
    return new Promise((resolve, reject) => {
      remove(endpointId)
        .then(() => {
          commit('REMOVE_ENDPOINT', endpointId);
          resolve(endpointId);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
