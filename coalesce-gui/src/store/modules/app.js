import Cookies from 'js-cookie';

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false,
    config: {},
    location: '',
  },
  device: 'desktop',
  currentlanguage: {
    code: 'ENG',
    title: 'English',
    name: 'ENG_text',
    text: '',
  },
  langs: [
    // TODO Hard coded for now, needs to come from config
    {
      code: 'ENG',
      title: 'English',
      name: 'ENG_text',
      text: '',
    },
    {
      code: 'FRA',
      title: 'French',
      name: 'FRA_text',
      text: '',
    },
    {
      code: 'EWE',
      title: 'Ewe',
      name: 'EWE_text',
      text: '',
    },
  ],
};

const mutations = {
  TOGGLE_SIDEBAR: state => {
    state.sidebar.opened = !state.sidebar.opened;
    state.sidebar.withoutAnimation = false;
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1);
    } else {
      Cookies.set('sidebarStatus', 0);
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0);
    state.sidebar.opened = false;
    state.sidebar.withoutAnimation = withoutAnimation;
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device;
  },
  SET_LOCATION: (state, item) => {
    // eslint-disable-next-line prefer-destructuring
    state.sidebar.location = item[0];
    // alert(JSON.stringify(item, null, 3))
  },
  SET_LANGUAGE: (state, lang) => {
    for (const l in state.langs) {
      if (l.code === lang) {
        state.currentlanguage = l;
        break;
      }
    }
  },
};

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR');
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation);
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device);
  },
  setSidebarLocation({ commit }, item) {
    commit('SET_LOCATION', item);
  },
  setLanguage({ commit }, lang) {
    commit('SET_LANGUAGE', lang);
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
