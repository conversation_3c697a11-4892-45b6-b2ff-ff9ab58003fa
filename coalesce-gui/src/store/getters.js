// eslint-disable-next-line no-unused-vars
function listLabelsOrig(code) {
  let labels = [];
  if (typeof code !== 'undefined') {
    if (Array.isArray(code)) {
      for (let i = 0; i < code.length; i++) {
        labels = labels.concat(listLabels(code[i]));
      }
    }
    if (typeof code.children !== 'undefined' && code.children.length > 0) {
      labels = labels.concat(listLabels(code.children));
    }
    if (typeof code.type === 'string' && code.type === 'label') {
      labels.push(code.name);
    }
  }
  // TODO remove any duplicates
  return labels;
}

function removeDups(names) {
  const unique = {};
  names.forEach(i => {
    if (!unique[i]) {
      unique[i] = true;
    }
  });
  return Object.keys(unique);
}

function listLabels(code) {
  // debugger
  let labels = [];
  if (typeof code[0] === 'string' && code[0] === 'label') {
    labels.push(code[1].name);
  }

  if (typeof code[3] !== 'undefined' && Array.isArray(code[3])) {
    for (let i = 0; i < code[3].length; i++) {
      labels = labels.concat(listLabels(code[3][i]));
    }
  }
  // TODO remove any duplicates
  return labels;
}

const getters = {
  sidebar: state => state.app.sidebar,
  device: state => state.app.device,
  langs: state => state.app.langs,
  token: state => state.user.token,
  tokenExpiry: state => state.user.tokenExpiry,
  picture: state => state.user.picture,
  name: state => state.user.name,
  email: state => state.user.email,
  user: state => state.user.user,
  userPermissions: state => state.user.user.groupedPermission,
  globalSettings: state => state.globalSettings.globalSettings,
  modules: state => {
    return state.modules;
  },
  components: state => {
    return state.component.components;
  },
  /*
  connectors: state => {
    return state.connector.connectors;
  },
  */
  connectorEndpoints: state => {
    return state.connectorEndpoint.connectorEndpoints;
  },
  treestate: state => state.menutree.treestate,
  treecontext: state => state.menutree.context,
  currentmodule: state => state.modules.currentmodule,
  treecode: state => {
    let result = null;
    try {
      if (typeof state.modules.menucode === 'string') {
        result = JSON.parse(state.modules.menucode);
      } else {
        result = state.modules.menucode;
      }
    } catch (err) {
      result = {};
    }
    return result;
  },
  treelabels: state => {
    return removeDups(listLabels(state.modules.menucode)).sort();
  },
  sidebarlocation: state => state.app.sidebar.location,
  currentlang: state => state.app.currentlanguage,
};
export default getters;
