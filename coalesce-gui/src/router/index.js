/* eslint-disable import/no-unresolved */
import Vue from 'vue';
import Router from 'vue-router';
/* Layout */
import Layout from '@/layout';

Vue.use(Router);

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login/choose-password',
    component: () => import('@/views/login/choose-password'),
    hidden: true,
  },

  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true,
  },

  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        meta: { title: 'Dashboard', icon: 'dashboard' },
      },
    ],
  },

  {
    path: '/',
    component: Layout,
    redirect: '/launchpad',
    children: [
      {
        path: 'launchpad',
        name: 'launchpad',
        component: () => import('@/views/launchpad/index'),
        meta: { title: 'Launchpad', icon: 'launchpad' },
      },
    ],
  },
  {
    path: '/',
    component: Layout,
    redirect: '/usermanagement',
    children: [
      {
        path: 'usermanagement',
        name: 'usermanagement',
        component: () => import('@/views/usermanagement/index'),
        meta: {
          title: 'User Management',
          icon: 'user',
          permission: [{ group: 'user', permissions: ['list'] }],
        },
      },
    ],
  },
  {
    path: '/',
    component: Layout,
    redirect: '/globalsettings',
    children: [
      {
        path: 'globalsettings',
        name: 'globalsettings',
        component: () => import('@/views/globalSettings/index'),
        meta: {
          title: 'globalsettings',
          icon: 'user',
          // Temporarily commented out .... NEW permission set here
          // permission: [{ group: 'globalsettings', permissions: ['list-site-settings'] }],
        },
      },
    ],
  },

  {
    path: '/',
    component: Layout,
    redirect: '/userprofile',
    children: [
      {
        path: 'userprofile',
        name: 'user-profile',
        component: () => import('@/views/userprofile/index'),
        meta: { title: 'User Profile', icon: 'user' },
      },
    ],
  },

  {
    path: '/',
    component: Layout,
    redirect: '/inviteuser',
    children: [
      {
        path: 'inviteuser',
        name: 'invite-user',
        component: () => import('@/views/inviteuser/index'),
        meta: { title: 'Invite User', icon: 'user' },
      },
    ],
  },

  {
    path: '/menu',
    component: Layout,
    redirect: '/menu-editor',
    children: [
      {
        path: 'menu-editor/:moduleId',
        name: 'menueditor',
        component: () => import('@/views/ussdmenu/index'),
        meta: { title: 'USSD Menu', icon: 'dashboard', rightbar: true },
      },
      {
        path: 'newmodule',
        name: 'newmodule',
        component: () => import('@/views/newmodule/index'),
        meta: { title: 'USSD Menu', icon: 'dashboard' },
      },
    ],
  },
  {
    path: '/component',
    component: Layout,
    redirect: '/comp-editor',
    children: [
      {
        path: 'comp-editor/:componentId',
        name: 'componenteditor',
        component: () => import('@/views/compeditor/index'),
        meta: { title: 'Component Menu', icon: 'dashboard' },
      },
      {
        path: 'newcomponent',
        name: 'newcomponent',
        component: () => import('@/views/newcomponent/index'),
        meta: { title: 'Component Menu', icon: 'dashboard' },
      },
    ],
  },
  {
    path: '/connector/endpoint',
    component: Layout,
    redirect: '/',
    children: [
      {
        path: ':connectorType/edit/:endpointId',
        name: 'edit',
        component: () => import('@/views/connector/index'),
        meta: { title: 'Edit Connector Endpoint', icon: 'dashboard' },
      },
      {
        path: ':connectorType/create',
        name: 'create',
        component: () => import('@/views/connector/index'),
        meta: { title: 'Create Connector Endpoint', icon: 'dashboard' },
      },
    ],
  },
  {
    path: 'external-link',
    component: Layout,
    children: [
      {
        path: 'https://panjiachen.github.io/vue-element-admin-site/#/',
        meta: { title: 'External Link', icon: 'link' },
      },
    ],
  },
  {
    path: '/403',
    component: () => import('@/views/403'),
    hidden: true,
  },
  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true,
  },

  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true },
];

const createRouter = () => {
  return new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes,
  });
};

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
