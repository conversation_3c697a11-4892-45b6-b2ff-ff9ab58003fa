/* eslint-disable import/no-unresolved */
import Vue from 'vue';
import Vuelidate from 'vuelidate';
import BootstrapVue, { IconsPlugin } from 'bootstrap-vue';
import Toast from 'vue-toastification';
import 'vue-toastification/dist/index.css'; // Import the CSS styles

import 'normalize.css/normalize.css'; // A modern alternative to CSS resets

import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap-vue/dist/bootstrap-vue.css';

import '@/styles/index.scss'; // global css

import '@/icons'; // icon
import '@/permission'; // permission control
import csio from '@/cs-web-sockets'; // socket.io for web sockets

import VTooltip from 'v-tooltip'; // https://github.com/Akryum/v-tooltip#usage
import IdleVue from 'idle-vue';
import lockoutManager from '@/components/LockoutManager'; // get token from cookie

import fa from './font-awesome-icons';
import router from './router';
import store from './store';
import App from './App.vue';

const { FontAwesomeIcon } = fa;

Vue.component('font-awesome-icon', FontAwesomeIcon);

Vue.use(BootstrapVue);
Vue.use(IconsPlugin);
Vue.use(Vuelidate);
// Vue.use(VeeValidate);
Vue.use(VTooltip);

Vue.use(Toast, {
  position: 'top-right', // Default position
  timeout: 5000, // Default timeout
});

// ---- Idle time is in seconds
//      Idle time cannot be less than 1 minutes to avoid lockout problems --- but it's preferable for it to be > 5 minutes
const IDLE_TIME_SECONDS = 5 * 60; // 5 minutes

const eventsHub = new Vue();
Vue.use(IdleVue, {
  store,
  eventEmitter: eventsHub,
  idleTime: IDLE_TIME_SECONDS * 1000,
});

Vue.config.productionTip = false;

Vue.prototype._csio = csio;

/**
 * EXAMPLE OF Web Sockets IO
  setTimeout(() => {
    console.debug('running csio event');
    csio.emit('test-event', { with: 'some data' });
    setTimeout(() => {
      csio.emit('test-event', { with: 'some more data' });
    }, 5000);
  }, 5000);

  csio.on('return-event', data => {
    console.debug('we got a shot back!!', data);
  });
 *
 */

function renderAppProps(h) {
  return h(App, {
    props: { idleSeconds: IDLE_TIME_SECONDS },
  });
}
// eslint-disable-next-line no-new
new Vue({
  el: '#app',
  router,
  store,
  components: {
    lockoutManager,
  },
  data() {
    return {
      renewInterval: null,
      idleTimeout: null,
      idleSeconds: IDLE_TIME_SECONDS,
    };
  },
  methods: {},
  render: renderAppProps,
  /*
  render: function(h) {
    return h(App, {
      props: { idleSeconds: this.idleSeconds },
    });
  },
  */
});
