const socketio = require('socket.io-client');

const socket = socketio();

socket.on('connect', () => {
  console.debug('WS IO connected');
});

socket.on('disconnect', reason => {
  console.debug('WS IO disconnected', reason);
});

socket.on('connect_error', error => {
  console.error('WS IO Error :', error);
});

const RESERVED_EVENTS = [
  //
  'connect',
  'connect_error',
  'disconnect',

  'disconnecting',
  'newListener',
  'removeListener',
];

class csio {
  static enabled() {
    return !!socket;
  }

  static emit(channel, data) {
    if (RESERVED_EVENTS.includes(channel)) {
      console.error(`Attempted to use Reserved WS IO event '${data}' in Socket IO Emit(...). Cannot emit reserved events.`);
      return;
    }
    socket.emit(channel, data);
  }

  static on(channel, callback) {
    socket.on(channel, callback);
  }

  static off(channel) {
    socket.off(channel);
  }
}

export default csio;
