import { library, dom } from '@fortawesome/fontawesome-svg-core';
import {
  faCircleNotch,
  faCog,
  faColumns,
  faPaperPlane,
  faPlug,
  faLaptopCode,
  faSitemap,
  faChevronDown,
  faChevronUp,
  faPlusCircle,
  faTag,
  faTimes,
  faChevronLeft,
  faChevronRight,
  faAngleUp,
  faAngleDown,
  faAngleLeft,
  faAngleRight,
  faRedo,
  faMobileAlt,
  faAlignCenter,
  faAlignJustify,
  faAlignRight,
  faAlignLeft,
  faFlag,
  faInfoCircle,
  faSave,
  faUserLock,
  faEdit,
  faSort,
  faSortDown,
  faSortUp,
  faBars,
  faPlay,
  faUser,
  faLock,
  faSearch,
  faFilter,
  faSync,
  faLongArrowAltRight,
  faRocket,
  faImage,
  faPlus,
  faTrashAlt,
} from '@fortawesome/free-solid-svg-icons';
import { faEye as farEye, faEyeSlash as farEyeSlash, faSave as farSave } from '@fortawesome/free-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

library.add(
  faCircleNotch,
  faCog,
  faSearch,
  faFilter,
  faColumns,
  faPaperPlane,
  faPlug,
  faLaptopCode,
  faSitemap,
  faMobileAlt,
  faRedo,
  faFlag,
  faSave,
  faSort,
  faSortDown,
  faSortUp,
  faUserLock,
  faUser,
  faLock,
  faBars,
  faPlay,
  farSave,
  faUserLock,
  faEdit,
  farEye,
  farEyeSlash,
  faInfoCircle,
  faAlignCenter,
  faAlignJustify,
  faAlignRight,
  faAlignLeft,
  faAngleUp,
  faAngleDown,
  faAngleLeft,
  faAngleRight,
  faChevronDown,
  faChevronUp,
  faChevronLeft,
  faChevronRight,
  faPlusCircle,
  faPlus,
  faTag,
  faTimes,
  faSync,
  faLongArrowAltRight,
  faRocket,
  faImage,
  faTrashAlt,
);

dom.watch();

export default { FontAwesomeIcon };
