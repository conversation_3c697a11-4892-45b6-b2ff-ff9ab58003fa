/* eslint-disable import/no-unresolved */
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css'; // progress bar style
import { getRememberToken, getToken } from '@/utils/auth'; // get token from cookie
import getPageTitle from '@/utils/get-page-title';
import jwt from 'jsonwebtoken';
import router from './router';
import store from './store';

NProgress.configure({ showSpinner: true }); // NProgress Configuration

const whiteList = ['/login']; // no redirect whitelist

router.beforeEach(async (to, from, next) => {
  // start progress bar
  NProgress.start();

  let hasToken = getToken();
  let rememberToken = getRememberToken();

  if (!hasToken) {
    if (rememberToken) {
      console.debug("Found 'Remember' token - attempting to reauthenticate");
      try {
        // this request will SET the token with the new token, if it is correct, we must assign it locally
        await store.dispatch('user/login', { token: rememberToken });
        hasToken = getToken();
        rememberToken = getRememberToken(); // get new replacement remember token
        console.debug('Successful re-authentication with remember token');
      } catch (e) {
        console.error('Error attempting to re-authenticate with remember token: ', e);
      }
    } else {
      console.debug('No authentication token, no remember token. Only whitelisted paths will be allowed.');
    }
  }

  // debugger;
  // set page title
  document.title = getPageTitle(to.meta.title);

  // determine whether the user has logged in

  if (!hasToken) {
    /* has no token */
    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next();
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.path}`);
      NProgress.done();
    }
  } else {
    const tokenData = jwt.decode(hasToken);
    if (to.path === '/login') {
      // if is logged in, redirect to the home page
      next({ path: '/' });
      NProgress.done();
    } else {
      // ---- not login page.... let's check permission
      const { permission } = to.meta;

      // ---- Is an array of permissions? .... then we must check them
      //    if it were not an array, then this route has no permission requirements
      if (Array.isArray(permission)) {
        console.debug("router.beforeEach() - We need 'all' these permissions to proceed", permission);

        let allowed = false;

        if (tokenData.groupedPermission) {
          const { groupedPermission } = tokenData;

          const hasIndex = permission.findIndex(p => {
            const { permissions, group } = p;
            const found = groupedPermission.findIndex(gp => {
              const matchingPermissions = gp.permissions.filter(oneP => {
                return permissions.includes(oneP);
              });
              if (gp.group === group && matchingPermissions.length > 0) {
                return gp;
              }

              return false;
            });
            if (found !== -1) {
              return true;
            }

            return false;
          });

          if (hasIndex === 0) {
            allowed = true;
          }
        } else {
          let requiredPermissions = [];
          permission.forEach(p => {
            p.permissions.forEach(permissionItem => {
              requiredPermissions.push(p.group + permissionItem);
            });
          });
          requiredPermissions = [...new Set(requiredPermissions)]; // remove duplicates

          requiredPermissions.forEach(p => {
            if (tokenData.permission.includes(p)) {
              allowed = true;
            }
          });
        }
        if (!allowed) {
          const code = '403';
          console.warn(`Does not have permission to view this page. Redirecting to ${code}`);
          next({ path: `/${code}` });
          NProgress.done();
          return;
        } // else continue because we have the necessary permissions to proceed ....
      }

      const hasGetUserInfo = store.getters.name;

      if (hasGetUserInfo) {
        next();
      } else {
        try {
          // get user info
          await store.dispatch('user/getInfo');
          next();
        } catch (error) {
          // remove token and go to login page to re-login
          await store.dispatch('user/resetToken');
          // TODO Message.error(error || "Has Error");
          next(`/login?redirect=${to.path}`);
          NProgress.done();
        }
      }
    }
  }
});

router.afterEach(() => {
  // finish progress bar
  NProgress.done();
});
