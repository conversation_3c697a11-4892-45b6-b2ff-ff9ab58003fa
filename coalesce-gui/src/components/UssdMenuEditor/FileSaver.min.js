(function(a,b){if("function"==typeof define&&define.amd)define(["exports"],b);else if("undefined"!=typeof exports)b(exports);else{var c={exports:{}};b(c.exports),a.FileSaver=c.exports}})(this,function(a){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var b=a.saveAs=b||function(a){if(!("undefined"==typeof a||"undefined"!=typeof navigator&&/MSIE [1-9]\./.test(navigator.userAgent))){var b=a.document,c=function(){return a.URL||a.webkitURL||a},d=b.createElementNS("http://www.w3.org/1999/xhtml","a"),e=function(a){var b=new MouseEvent("click");a.dispatchEvent(b)},f=/constructor/i.test(a.HTMLElement)||a.safari,g=/CriOS\/[\d]+/.test(navigator.userAgent),h=a.setImmediate||a.setTimeout,i=function(a){h(function(){throw a},0)},j=function(a){setTimeout(function revoker(){"string"==typeof a?c().revokeObjectURL(a):a.remove()},40000)},k=function(a,b,c){b=[].concat(b);for(var d,e=b.length;e--;)if(d=a["on"+b[e]],"function"==typeof d)try{d.call(a,c||a)}catch(a){i(a)}},l=function(a){return /^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(a.type)?new Blob(["\uFEFF",a],{type:a.type}):a},m=function(b,i,m){m||(b=l(b));var n,o=this,p=b.type,q=p==="application/octet-stream",r=function(){k(o,["writestart","progress","write","writeend"])};return o.readyState=o.INIT,"download"in d?(n=c().createObjectURL(b),void h(function(){d.href=n,d.download=i,e(d),r(),j(n),o.readyState=o.DONE},0)):void function fs_error(){if((g||q&&f)&&a.FileReader){var d=new FileReader;return d.onloadend=function(){var b=g?d.result:d.result.replace(/^data:[^;]*;/,"data:attachment/file;"),c=a.open(b,"_blank");c||(a.location.href=b),b=void 0,o.readyState=o.DONE,r()},d.readAsDataURL(b),void(o.readyState=o.INIT)}if(n||(n=c().createObjectURL(b)),q)a.location.href=n;else{var e=a.open(n,"_blank");e||(a.location.href=n)}o.readyState=o.DONE,r(),j(n)}()},n=m.prototype,o=function(a,b,c){return new m(a,b||a.name||"download",c)};return"undefined"!=typeof navigator&&navigator.msSaveOrOpenBlob?function(a,b,c){return b=b||a.name||"download",c||(a=l(a)),navigator.msSaveOrOpenBlob(a,b)}:(n.abort=function(){},n.readyState=n.INIT=0,n.WRITING=1,n.DONE=2,n.error=n.onwritestart=n.onprogress=n.onwrite=n.onabort=n.onerror=n.onwriteend=null,o)}}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||void 0)});