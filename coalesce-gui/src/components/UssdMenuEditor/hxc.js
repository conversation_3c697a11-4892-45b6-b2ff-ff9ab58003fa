/**
 * Author : <PERSON> .
 *
 * Description: HXC Global Client Side Functionality.
 */
/* eslint-disable */

let hxc = {};   // create the HXC global object which will be used to store system wide client data

hxc.themeNameList = "black-tie,blitzer,cupertino,dark-hive,dot-luv,eggplant,excite-bike,flick,hot-sneaks,humanity,jface,le-frog,mint-choc,overcast,pepper-grinder,redmond,smoothness,south-street,start,sunny,swanky-purse,trontastic,ui-darkness,ui-lightness,vader".split(",");

hxc.uiProps = [];
hxc.editParams = [];
hxc.tableParams = [];
hxc.dynamics = [];
hxc.spinCount = 0;

// initialize delimiter list
hxc.delim = {table_row   : "|#|",
             table_col   : "#|#",
             form_param  : "&!&",
             list_data   : "$##$",
             list_row    : "$#$",
             list_col    : "#$#",
             msg_var_row : "!^!",
             msg_var_col : "^!^",
             tabs        : "*^*"};

hxc.okMessage = function(st)
{
    hxc.showMsg("Message", replace(st, "\n", "<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"));
};

hxc.errorMessage = function(st)
{
    hxc.showMsg("Error", replace(st, "\n", "<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"));
};

hxc.waitFor = function(who, what)
{
	var p = who.split("."), i, tot = '';
	for (i=0; i<p.length; i++)
	{
		tot += p[i];
		if (!eval('(' + tot + ' != null)'))
		{
			setTimeout("hxc.waitFor('" + who + "'," + what + ");", 100);
			return;
		}
		tot += '.'
	}
	what();
};

// preloads a list of js files. list is a comma separated string with specific scripts to load in the following format : <plugin-id>/<script-filename-without-extension>
hxc.preLoad = function(list, id, prefix)
{
	var scrs = list.split(","), n;

    for (n=0; n<scrs.length; n++)
    {
        var nm = (id ? id + '/' : '') + scrs[n], ni = nm.replace("/", "_");
       
        if (ni.length > 0)
        {
	        if (!hxc.scripts) hxc.scripts = {};
	
	        if (!hxc.scripts[nm])
	        {
	        	if (document.getElementById(ni) == null)
	        	{
		            var headID = document.getElementsByTagName("head")[0], it = hxc.scripts[nm] = document.createElement('script');
		            it.type = 'text/javascript';
		            it.src =  (prefix==null ? 'content/plugin/'+nm : nm) + ".js";
		            it.id = ni;
		            headID.appendChild(it);
	            }
	        }
        }
    }
};

// loads a comma separated list of scripts using a semi-absolute path (excluding '.css')
//
// example: hxc.loadStyles("web/css/windows,content/plugin/org.concurrent.provision.config/css/pe");    // will load windows.css from the core and 
//																											 pe.css from the specified plugin location
hxc.loadStyles = function(list)
{
    var l = list.split(","), n;

    for (n=0; n<l.length; n++)
    {
        var nm = l[n];
        if (!hxc.styles) hxc.styles = {};

        if (!hxc.styles[nm])
        {
            var headID = document.getElementsByTagName("head")[0], it = hxc.styles[nm] = document.createElement('link');
            it.type = 'text/css';
			it.rel  = 'stylesheet';
            it.href = nm + ".css";
            headID.appendChild(it);
        }
    }
};

hxc.scriptLoaded = function (w)
{
    if (!hxc.scriptState) hxc.scriptState = {};
	hxc.scriptState[w] = true;
};

hxc.loadThenCall = function(file, func, prefix)
{
    if (!hxc.scriptState) hxc.scriptState = {};
    var scs = file.split(","), alll = true;
    for (s in scs)
	    if (!hxc.scriptState[scs[s]])
	    {
	    	//alert(scs[s] + ") " + hxc.scriptState[scs[s]] );
 			alll = false
 	    	hxc.preLoad(scs[s], null, prefix);
	    }
	
	if (alll) 
	   	setTimeout(func + "()", 100);
	else 
	{
		if (prefix == null)
			setTimeout("hxc.loadThenCall('"+file+"','"+func+"');", 100);
		else 
		   	setTimeout("hxc.loadThenCall('"+file+"','"+func+"','"+prefix+"');", 100);
	}
};

let StringBuffer = function()
{
    this.idx = 0;
    this.buffer = [];
    this.append = function(s)
    {
        this.buffer[this.idx++] = s;
        return this;
    }
    this.clear = function(s)
    {
        this.buffer = [];
    }
    this.toString = function()
    {
        return this.buffer.join('');
    }
};

let showLog = function()
{
    alert(hxc.log.join("\n"));
    hxc.log = [];
};

hxc.showLog = function(a, intab)
{
	if (intab)
	{
		var tab = window.open("about:blank", "tab", ""), tmp = tab.document;
		tmp.write('<head><title>HXCUI Log</title></head><body style="font-family:courier">' + a + "</body>");
		if (window.focus) tab.focus();
		tmp.close();
		
	} else {
	    $('#dialog').remove();
	    var str = '<div id="dialog" style="overflow:auto;background-color:white;" title="System Log">'+a+'</div>';
	    $('#dialog_container').html(str);
	    $('#dialog').dialog({autoOpen:false,height:hxc.appHeight-100,width:hxc.appWidth-100,modal:true,resizable:true,
	    	close: function()
	    	{

	    	},
	        buttons: [{text:"Ok", click:function() {
	                        $(this).dialog("close");
	                        $('#dialog').remove();
	                    }}]});
	    $('#dialog').dialog('open');
	}   
};

let logout = function()
{
    $.post("service", "service=logout", function(res) {top.location = "";} );
};

let chooseMenu = function(item_id)
{


	// close all menus
    $.each(allUIMenus, function(i){if (allUIMenus[i].menuOpen) {allUIMenus[i].kill();};});

    // save for later
    hxc.lastMenu = item_id;
    
    // to server
    hxc.doXHR(item_id);
};

// call this with a index int the list of supported themes to change the theme at runtime
let selectTheme = function(tidx)
{
    var themename = hxc.themeNameList[tidx];
    var tref = "content/css/" + themename + "/jquery-ui-1.10.4.custom.min.css";
    $("#active_theme").attr({href: tref});
    return false;
};

hxc.dialogKey = function(e)
{
	if (e.which == 13 || e.which == 89 || e.which == 121) // [enter] or [Y] or [y] 
		hxc.yesFunc();

	hxc.confirmDialog.dialog("close");
	$('#dialog').remove();
	e.preventDefault();
    $(document).unbind('keypress', hxc.dialogKey);
};

hxc.confirm = function(caption, msg, yesFunc)
{
   $('#dialog').remove();
   $('#dialog_container').html('<div id="dialog" title="'+caption+'"><p><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 0 0;"></span>'+msg+'</p></div>');

   hxc.confirmDialog = $('#dialog').dialog
   	(
   		{	autoOpen  : false,
   			width     : 400,
   			modal     : true,
   			resizable : false,
	    	close: function()
	    	{

	    	},
		      buttons   : 
		      	[{text: "Yes", click : function() 
			      	{
			      		$(this).dialog("close");
			      		$('#dialog').remove();
			      		yesFunc();
			      	    $(document).unbind('keypress', hxc.dialogKey);

			      	}
		      	},{text: "No", click : function() 
			         {
			         	$(this).dialog("close");
			            $('#dialog').remove();
			            $(document).unbind('keypress', hxc.dialogKey);

			         }
		      	}]
   		}
   	);
   hxc.yesFunc = yesFunc;
   $('#dialog').dialog('open');
   $(document).bind('keypress', hxc.dialogKey);
};

hxc.showMsg = function(caption, msg, cod, w, icon)
{
    $('#dialog').remove();
    var str = '<div id="dialog" title="'+caption+'"><p><span class="ui-icon ui-icon-'+(icon || 'alert')+'" style="float:left; margin:0 7px 0 0;"></span>'+msg+'</p></div>';
    $('#dialog_container').html(str);

    $('#dialog').dialog({autoOpen:false,width:(w || 400),modal:true,resizable:false,
    	close: function()
    	{
 
    	},
        buttons: [{text:"Ok",click:function() {
                        $(this).dialog("close");
                        $('#dialog').remove();
                        if (cod) setTimeout(cod, 250);
                    }}]});
    $('#dialog').dialog('open');
};

hxc.showDialog = function(caption, msg, cod)
{
    $('#dialog').remove();
    var str = '<div id="dialog" style="overflow:auto;background-color:white;" title="'+caption+'">'+msg+'</div>';
    $('#dialog_container').html(str);

    $('#dialog').dialog({autoOpen:false,height:500,width:570,modal:true,resizable:true,
    	close: function()
    	{

    	},
        buttons: [{text:"Ok",click: function() {
                        $(this).dialog("close");
                        $('#dialog').remove();
                        if (cod) setTimeout(cod, 250);
                    }}]});
    $('#dialog').dialog('open');
};

hxc.htmlDisplay = function(caption, html, wid)
{
    $('#dialog').remove();
    $('#dialog_container').html('<div id="dialog" title="'+caption+'">'+html+'</div>');
    $('#dialog').dialog({autoOpen:false,width:wid,modal:true,resizable:false,
    	close: function()
    	{
    		
    	}});
    $('#dialog').dialog('open');
};


hxc.log = [];

hxc.startLog = function ()
{
    hxc.lastTime = new Date().getTime();
};

hxc.stopLog = function (s)
{
    hxc.log.push(s + " - " + Number(new Date().getTime()-hxc.lastTime));
};


hxc.doXHR = function (func_id, params)
{
	hxc.idleTime = 0;
	hxc.lastFunc = func_id;
	hxc.lastParams = params;
	
	hxc.spinCount++;
   $('#spinner').css("visibility", "visible");
  // $('html, body, button, a, image').css('cursor', 'wait');

   var data = "func_id=" + func_id;

   if (params) data += "&" + params;

   $.post("service", data,
    function(res) {
        //hxc.stopLog("Querying : "+params);
        if (res.substring(0, 5) == '[msg]')         // show a message
        {
            //alert(res.substring(5));
            hxc.showMsg("Message", replace(res.substring(5), "\n", "<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"));

        } else if (res.substring(0, 5) == '[cod]') { // run some js code
            try {
                eval(res.substring(5));
            } catch (err) {
         	    res = res.substring(5);
         	    err = "" + err;
        		if (res.length > 256) res = res.substring(0, 256);
       			if (err.length > 256) err = err.substring(0, 128) + "<b> . . . </b>" + err.substring(err.length - 128);
            	hxc.showMsg("Error", "<b>Code Returned by the server did not execute succesfully :</b><br>" + err + "<br><br><b>Code:</b><br>" + res);
            }

        } else {                                    // otherwise paste into application container
            $("#app_container").html(res);
        }
        // reset the cursor
    //    $('html, body, image').css('cursor', 'default');
    //    $('button, a').css('cursor', 'pointer');
        
        hxc.spinCount--;
        if (hxc.spinCount == 0)
           $('#spinner').css("visibility", "hidden");
        
    });
};

hxc.doGetFile = function (func_id, params)
{
   // $('html, body, button, a, image').css('cursor', 'wait');
    var sb = [], i;

    for (i in params)
        sb.push("&" + i + "=" + params[i]);

    top.location = "service?func_id=" + func_id +  sb.join("");
 
    //$('html, body, image').css('cursor', 'default');
    //$('button, a').css('cursor', 'pointer');
};

hxc.doXHRUTF = function (func_id, params, callback)
{
   hxc.idleTime = 0;
   hxc.lastFunc = func_id;
	
   hxc.spinCount++;
   $('#spinner').css("visibility", "visible");
   //$('html, body, button, a, image').css('cursor', 'wait');

   if (params) params["func_id"] = func_id;
  
   $.ajax({
      type: 'POST',
      url: "service",
      dataType: "text",
      contentType: "application/x-www-form-urlencoded;charset=UTF-8",
      data: params,
      success: function(res) {
    	    if (typeof callback != 'undefined') {
    	    	callback(res);
    	    }
            //hxc.stopLog("Querying : "+params);
    	    else if (res.substring(0, 5) == '[msg]')         // show a message
            {
                //alert(res.substring(5));
                hxc.showMsg("Message", replace(res.substring(5), "\n", "<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"));

            } else if (res.substring(0, 5) == '[cod]') { // run some js code
                try {
                    eval(res.substring(5));
                } catch (err) {
                   if (console) console.error(err);
             	    res = res.substring(5);
             	    err = "" + err;
            		if (res.length > 256) res = res.substring(0, 256);
           			if (err.length > 256) err = err.substring(0, 128) + "<b> . . . </b>" + err.substring(err.length - 128);
                	hxc.showMsg("Error", "<b>Code Returned by the server did not execute succesfully :</b><br>" + err + "<br><br><b>Code:</b><br>" + res);
                }

            } else {                                    // otherwise paste into application container
                $("#app_container").html(res);
            }
            // reset the cursor
     //       $('html, body, image').css('cursor', 'default');
     ///       $('button, a').css('cursor', 'pointer');
            hxc.spinCount--;
            if (hxc.spinCount == 0)
               $('#spinner').css("visibility", "hidden");
        }
    });
};

hxc.stdService = function (func_id, params)
{
	hxc.menu_id = "coreService";
   hxc.doXHR(func_id, params);
};

hxc.reloadQ = " You need to reload to see your new menu options. Would you like to do so now?";

hxc.levelsChanged = function()
{
    hxc.confirm("Notice:", "Your access levels in the system has changed." + hxc.reloadQ,
    function () {top.location = top.location;});
};

hxc.menusChanged = function()
{
    hxc.confirm("Notice:", "System Plugins has been modified." + hxc.reloadQ,
    function () {top.location = top.location;});
};

hxc.autoHover = function()
{
    $('.f_b').hover(
        function(){$(this).removeClass('ui-state-default').addClass('ui-state-focus');},
        function(){$(this).removeClass('ui-state-focus').addClass('ui-state-default');}
    );
};

hxc.updateConcurrency = function(name, func, params)
{
    hxc.confirm("Concurrency Error", "The " + name + " has been updated since you started editing it.<br><br>Would you like to retrieve the new data?",
        function () {
           hxc.doXHR(func, params);
        });
};

hxc.refreshPage = function(name, func, params)
{
    hxc.confirm("Page Outdated", "The " + name + " is different from your page.<br><br>Would you like to retrieve the latest data?",
        function () {
           hxc.doXHR(func, params);
        });
};

hxc.initViewPort = function () {

    // the more standards compliant browsers (mozilla/netscape/opera/IE7) use window.innerWidth and window.innerHeight
    if (typeof window.innerWidth != 'undefined')
    {
        hxc.appWidth  = window.innerWidth - 260,
        hxc.appHeight = window.innerHeight
    }
    // IE6 in standards compliant mode (i.e. with a valid doctype as the first line in the document)
    else if (typeof document.documentElement != 'undefined' && typeof document.documentElement.clientWidth != 'undefined' && document.documentElement.clientWidth != 0)
    {
        hxc.appWidth  = document.documentElement.clientWidth - 260,
        hxc.appHeight = document.documentElement.clientHeight
    }
    else {    // older versions of IE
        hxc.appWidth  = document.getElementsByTagName('body')[0].clientWidth - 260,
        hxc.appHeight = document.getElementsByTagName('body')[0].clientHeight
    }

    // check fullscreen support and attach handlers for fullscreen events
    var docElm = document.documentElement;
    if (docElm.requestFullscreen) {						// W3C
        document.addEventListener("fullscreenchange", hxc.fullScreenEvent, false);
    }
    else if (docElm.mozRequestFullScreen) {				// Gecko - Mozilla
    	document.addEventListener("mozfullscreenchange", hxc.fullScreenEvent, false);
    }
    else if (docElm.webkitRequestFullScreen) {			// Webkit / Blink / Chrome
    	document.addEventListener("webkitfullscreenchange", hxc.fullScreenEvent, false);
    }
    else if (docElm.msRequestFullscreen) {				// Trident / MS
   	 
    	document.addEventListener("msfullscreenchange", hxc.fullScreenEvent, false); 
    }
    
    hxc.appHeight = hxc.appHeight - $("#top_header").height() - 20;

    //COOP hxc.checkWrapping(false);
};

hxc.fullScreenEvent = function(evt)
{
	var isFull = document.fullscreen || document.mozFullScreen || document.webkitIsFullScreen || document.msFullScreenElement != null;
	
//	console.log(isFull ? "Entered | FullScreen" : "Exited | FullScreen");
};

hxc.addCss = function (id, cssCode)
{
    if (document.getElementById(id) != null)
         $('#' + id).remove();

    var styleElement = document.createElement("style");
    styleElement.setAttribute("id", id);
    
    styleElement.type = "text/css";
    if (styleElement.styleSheet) {
        styleElement.styleSheet.cssText = cssCode;
    } else {
        styleElement.appendChild(document.createTextNode(cssCode));
    }
    document.getElementsByTagName("head")[0].appendChild(styleElement);
}

$(window).resize(function() {
    hxc.initViewPort();
});

$(function()
{
	// -- pushlet ---------
	// var headID = document.getElementsByTagName("head")[0], it = hxc.pushlet = document.createElement('script'), ni = "pushlet";
    // it.type = 'text/javascript';
    // it.src =  "?service=pushlet";
    // it.id = ni;
    // headID.appendChild(it);

    setTimeout("hxc.modScreen();", 100);
});

$.extend(
    $.fn.disableBackspace = function()
    {
        return this.each(function()
        {
            if (/*$.browser.msie*/ isIE())
            {
                $(this).keydown(function(e) {
                    if (e.keyCode == 8)
                    {
                        //alert(e.target.nodeName);
                        //if $('input[type=checkbox]:focus').size() > 0) return false;
                        if (e.target.nodeName == 'INPUT')
                        {
                            if (e.target.type == 'checkbox') return false;
                            if (e.target.type == 'radio') return false;
                            return true;
                        }
                        return false;
                    }
                    return true;
                });
            }
        });
    });

$.extend(
    $.fn.disableTextSelect = function()
    {
        return this.each(function()
        {
            if($.browser.mozilla){      //  Firefox
                $(this).css('MozUserSelect','none');
            } else if($.browser.msie) { //  IE
                $(this).bind('selectstart', function() {return false;} );
            } else {                    //  Opera, etc.
                $(this).mousedown(function(){return false;});
            }
        });
    });

$.extend(
    $.fn.disableElementDrag = function()
    {
        return this.each(function()
        {
            $(this).mousedown(function(){return false;});
        });
    });
// $('.noSelect').disableTextSelect();// No text selection on elements with a class of 'noSelect'


hxc.checkMandatory = function(mlist)
{
    var fields = mlist.split(","), sb = [], i;
    for (i=0; i<mlist.length; i++)
    {
        var cf = fields[i],
            cv = hxc.formManager.currentForm.runtime.parameters[cf];
        if (cf)
        {
            var lb = document.getElementById(cf + "_lbl");
            if (cv.length == 0)
            {
                var dv = (lb ? lb.innerHTML : cf).split("(")[0];
                sb.push("<li>"+ dv +"</li>");
            }
        }
    }

    if (sb.join("").length > 0)
    {
       hxc.errorMessage("The following fields are mandatory:<ul>" + sb.join("") + '</ul>');
       return false;
    }
    return true;
};

// decompress compressed JSON code.
hxc.decompressJSON = function(data, sym)
{
	var top = 126, bottom = 33, dontUse = ":,[]{}'\"\\", ids = [], idl = top - bottom - dontUse.length, i, n = bottom;
	for (i=0; i < idl; i++)
	{
		while (dontUse.indexOf(String.fromCharCode(n)) != -1) n++;
		ids[String.fromCharCode(n++)] = i;
	}

	var symTab = sym.split(","), l = data.length, sb = [];
	for (i = 0; i<l; i++)
	{
		var c = data.charAt(i);
		if (dontUse.indexOf(c) != -1)
		{
			sb.push(c);
		} else {
			var cd = c;
			c = data.charAt(++i);
			while (dontUse.indexOf(c) == -1)
			{
				cd += c;
				c = data.charAt(++i);
			}
			
			// handle any size symbol
			var tot = 0, mul = 1, k;
			for (k=0; k<cd.length; k++)
			{
				tot += ids[cd.charAt(k)] * mul;
				mul *= idl;
			}
			
			sb.push(symTab[tot]);
			i--; // backtrack once
		}
	}
	return sb.join("");
};

// inserts a field programmatically after form load time
hxc.insertField = function(target, type, fname, value, fcontents, extra)
{
    var f = hxc.formManager.currentForm, attr = {name:fname};
    
    if (extra)
    	for (p in extra) attr[p] = extra[p];
    	
    f.runtime.parameters[fname] = value;
    f.initCode = [];
    
    var k = f.buildField({name:type,attributes:attr,contents:fcontents||[]});
    		 
    $(target).html(k);
    
    eval(f.initCode.join(""));
};

//inserts a field programmatically after form load time
hxc.replaceField = function(target, type, fname, value, fcontents, extra)
{
    var f = hxc.formManager.currentForm, attr = {name:fname};
    
    if (extra)
    	for (p in extra) attr[p] = extra[p];
    	
    f.runtime.parameters[fname] = value;
    f.initCode = [];
    
    var k = f.buildField({name:type,attributes:attr,contents:fcontents||[]});
    		 
    var div = document.createElement('div');
    div.innerHTML = k;
    target.replaceChild(div.firstChild, document.getElementById(fname));
    
    eval(f.initCode.join(""));
};

hxc.sendActions = function()
{

	setTimeout("hxc.sendActions()", 30000);
};

// inserts a field programmatically after form load time
hxc.insertFieldContent = function(target, type, fname, value, fcontents, extra)
{
    var f = hxc.formManager.currentForm, attr = {name:fname};
    
    if (extra)
    	for (p in extra) attr[p] = extra[p];
    	
    f.runtime.parameters[fname] = value;
    f.initCode = [];
    
    var k = f.buildField({name:type,attributes:attr,contents:fcontents||[]}), p1 = k.indexOf("><"), p2 = k.lastIndexOf("><");
    k = k.substring(p1+1, p2+1);
    		 
    $(target).html(k);
    
    eval(f.initCode.join(""));
};

// inserts a form programmatically at runtime
hxc.insertForm = function(formName, caption, pluginId, buttons, params, bodyMarkup, withFrame)
{
	buildDoc('<form name="'	
				+formName+ '"' + (withFrame ? '' : ' frameless="true"') + ' plugin="'
				+pluginId+ '"><runtime behaviour="def" parameters="'
				+(params || '')+'"/>'
				+(bodyMarkup || '') + '<behaviour name="def" caption="'
				+caption+'" mode="edit">'
				+(buttons || '<cancel label="Save" function="save" icon="plus"/><back/>')+'</behaviour></form>','');
};

hxc.updateLogThread = function(func_id)
{
    hxc.doXHR(func_id, "c=" + (hxc.updCount || "0"));
    if (hxc.logUpdating)
        setTimeout("hxc.updateLogThread('"+func_id+"');", 3500);
};

hxc.getFormParameters = function ()
{
	return hxc.formManager.currentForm.runtime.parameters;
};

hxc.updateLog = function(content)
{
    $("#rlog").html(content);
};

hxc.uiDisabled = function(which, isDisabled, noCheckValue)
{
	var list = which.split(","), i;
	for (i=0; i<list.length; i++)
	{
		var cmpn = list[i], chkn = cmpn + "_check", frmn = cmpn + "_frm", lbln = cmpn + "_lbl";
			cmp = $('#' + cmpn), chk = $('#' + chkn), frm = $('#' + frmn), lbl = $('#' + lbln), disabled = isDisabled;
		
   		if (disabled)
   		{
			if (frm) hxc.uiEnable(frmn);
			if (lbl) hxc.uiEnable(lbln);
            hxc.uiEnable(chkn);
        } else {
			if (frm) hxc.uiDisable(frmn);
			if (lbl) hxc.uiDisable(lbln);
            hxc.uiDisable(chkn);
		}	        

       	if (chk.length && !noCheckValue)
       		disabled = chk.is(":checked");

		if (cmp)
			if (!disabled || !isDisabled)
				hxc.uiDisable(cmpn);
			else 		
				hxc.uiEnable(cmpn);
	}  
};

hxc.enabled = function(which, enabled)
{
	var list = which.split(","), i;
	for (i=0; i<list.length; i++)
	{
		var cmpn = list[i], chkn = cmpn + "_check", frmn = cmpn + "_frm", lbln = cmpn + "_lbl";
			cmp = $('#' + cmpn), chk = $('#' + chkn), frm = $('#' + frmn), lbl = $('#' + lbln);
		
   		if (enabled)
   		{
			if (frm) hxc.uiEnable(frmn);
			if (lbl) hxc.uiEnable(lbln);
            if (chk) hxc.uiEnable(chkn);
			if (cmp) hxc.uiEnable(cmpn);
        } else {
			if (frm) hxc.uiDisable(frmn);
			if (lbl) hxc.uiDisable(lbln);
            if (chk) hxc.uiDisable(chkn);
			if (cmp) hxc.uiDisable(cmpn);
		}	        
	}  
};

hxc.createOptions = function(list)
{
    var it = list.split(","), sb = [], n = 0, lc = hxc.delim["list_col"], rc = hxc.delim["list_row"];
    sb.push("0" + hxc.delim["list_data"]);
    for (v in it)
    {
        sb.push(n + lc + it[v] + rc);
        n++;
    }
    var res = sb.join("");
    res = res.substring(0, res.length - rc.length);
    return res;
};


// =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
// =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
// =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-

hxc.fieldHandlers = {
	
	text: function(child, name, tip, label, labelConst, val, initC, width, form, extraParams) // =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
	{
	   var  sb = [], pf, a = child.attributes,
	   		max  = a.max, min  = a.min, chck = a.check, ac = a.autocomplete, chrs = a.keyvalid,
	      	maxlen = a.maxlen || 16, ism = (a.type == 'money'), dir = a.dir || "ltr";
	
	    sb.push('<div id="'+name+'_all">');
	    
	    if (chck)
	    {
	        sb.push('<input id="'+name+'_check" '+(tip?' title="'+tip+'"':'')+' onclick="hxc.textCheck(\''+name+
	                    '\',\''+chck+'\');" class="checkbox_field" type="checkbox" style="margin:0px;padding:0px;margin-top:3px;" '+(val != chck ?'checked' : '') + '>&nbsp;');
	        if (val == chck)
	            initC.push("hxc.uiDisable('"+name+"');"); // "+(val==chck ? 'Dis' : 'En')+"
	
	        hxc.addValidation(name, chrs, label, chck);
	
	    } else {
	        hxc.addValidation(name, chrs, label);
	    }
	
		if (a.labelpos && a.labelpos=="left")
		{
			    if (label) sb.push(labelConst+name+'" style="display:inline-block;width:'+(a.labelwidth ? a.labelwidth+"px" : "100px")+';text-align:right">'+label+'&nbsp;</label>');
	    	sb.push('<div class="text_cnt_field" style="display:inline-block;'+stdStyleMarkup(child)+'">');
		} else {
	    	if (label) sb.push(labelConst+name+'">'+label+'</label>');
	    	sb.push('<div class="text_cnt_field" style="'+stdStyleMarkup(child)+'">');
	    }
	
	    if (ism) 
	        if (pf = form.runtime.parameters["prefix"])
	            sb.push('<span class="currency">' + pf + "</span>");
	
	    sb.push('<input class="text_field input_default '+stdClassMarkup(child)+(max||min?' text_right':'')+
	        '" style="width:'+width+
	        '" id="'+name+
	        '" onchange="hxc.onChange(\''+name+
	        '\');" name="'+name+
	        '" MAXLENGTH="' + maxlen +
	        '" dir="' + dir +
	        '" ' + (ac ? 'autocomplete="' + ac + '"' : "") +
	        ' tabindex='+(form.tabIndex++) + 
	        ' type="text" value="'+val+
	        ''+stdAttribsMarkup(child));
	    if (chrs) 
	    	sb.push(' return hxc.validateOnKey(event, \'' + chrs + '\')"');
	    	
	    if (tip) 
	    	sb.push(' title="'+tip+'"');
	    sb.push(form.extraParams(extraParams));
	    sb.push('/>');
	
	    if (ism) 
	    	sb.push('<span class="currency">' + form.runtime.parameters["suffix"] + "</span>");
	
	    sb.push('</div></div>');
	    
	    // if we have a max or a min turn it into a spinner
	    if (max || min)
	    {
	    	initC.push('$("#'+name+'").spin({max:'+(max||9999)+',min:'+(min||0)+'});');
	    	initC.push('$("#'+name+'").css("border-top-right-radius","0px");');
	    	initC.push('$("#'+name+'").css("border-bottom-right-radius","0px");');
	        
	    }
	    return sb.join('');
	 }
	 ,
	 repeat:function(child, name, tip, label, labelConst, val, initC, width, form) // =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
	 {
	    var times = val.split(","), ti, sb = [];
	    sb.push('<div style="' + stdStyleMarkup(child) + '" >');
	    for (ti = 0; ti < times.length; ti++)
	    {
	        var ei = 0;
	        for (;ei < child.contents.length; ei++)
	        {
	            var echild = child.contents[ei];
	            
	            if (echild.type == ID_ELEMENT)
	            {
	                var v = echild.attributes["name"] || echild.attributes["id"], newname = times[ti] + "_" + v;
	                echild.attributes["eventname"] = v;
	
	                echild.attributes["name"] = newname;
	                
	                echild.attributes["label"] = times[ti];
	               
	                sb.push(form.buildField(echild) + '<br/>');
	
	                echild.attributes["name"] = v;
	            }
	        }
	    }
	    sb.push('</div>');
	    return sb.join('');
	 }
	 ,panel:function(child, name, tip, label, labelConst, val, initC, width, form)
	 {
		var sb = [], a = child.attributes;

		sb.push('<div');
		if (name) sb.push(' id="' + name + '"');
		sb.push(' class="display' + stdClassMarkup(child) + '"');
		sb.push(' style="' + stdStyleMarkup(child) + '">');
		 
		if (!a.defer) 
		{
			sb.push(form.buildMarkup(child, true, null, true));
		} else {
			if (hxc.defer == null) hxc.defer = [];
			hxc.defer[name] = child;
		}
		sb.push('</div>');
		
		return sb.join('');
	 }
};

//hxc.fieldHandlers["clientbutton"] = function(child, name, tip, label, labelConst, val, initC, width, form)
//{
//	var func = child.attributes['code'];
//	
//	if (!func) 
//	{
//		alert("please add the 'code' attribute to youre 'clientbutton'");
//		return ""; 
//	}
//	
//	var hi = child.attributes['icon'], img = hi ? '<span class="ui-icon ui-icon-'+hi+'"/>' : '';
//	sb.push('<button class="ui-state-default ui-corner-all '+stdClassMarkup(child)+
//	   '" style="height:30px;padding:2px;padding-right:5px;padding-bottom:5px;line-height:0;'+(w ? "width:"+w+"px" : "")+stdStyleMarkup(child)+
//	   '" tabindex='+(this.tabIndex++) + ' id="'+name);
//	
//	if (tip) sb.push('" title="'+tip);
//	
//	sb.push(
//	   '" onclick="'+func+'();return false;"><span href="#" style="margin:0px;padding:0px;padding-left:'+(hi?22:7)+'px" class="fg-button '+(hi ? 'fg-button-icon-left ' : '')+
//	   '">'+img+ label +'</span></button><br />');
//}

hxc.fieldHandlers["ibut"] = function(child, name, tip, label, labelConst, val, initC, width, form)
{
    var hd = formatSizeH(child.attributes['height'], "200"), wd = formatSizeW(child.attributes['width'], "200"), src = child.attributes['src'];
    return('<span id="' + name + '" title="' + tip +'" class="field_label f_b f-button ui-state-default" style="cursor:pointer;font-size:12px"><image src="'+src+'" style="vertical-align:middle;width:'+wd+';height:'+hd+stdStyleMarkup(child)+'" class="' + stdClassMarkup(child) + '">&nbsp;' + (child.attributes['label'] || '') + '</span>');
};


hxc.fieldHandlers['frame'] = function(child, name, tip, label, labelConst, val, initC, width, form)
{
    var ht = formatSizeH(child.attributes['height'], 'x'), wd = formatSizeW(child.attributes['width'], 'x'), sb = [],
		sizStl = (ht != 'x' ? 'height:'+ht+';' : '') + (wd != 'x' ? 'width:'+wd+';' : '');

    sb.push('<div id="'+name+'_frm" style="'+sizStl+stdStyleMarkup(child)+'" class="frame_field' + stdClassMarkup(child)+'"><span class="ui-widget ui-widget-content frame_label" style="background-position:50% 70%;'+sizStl+stdStyleMarkup(child)+'">');

    chck = child.attributes["check"];

    if (chck)
    {
        sb.push('<input id="'+name+'_check" '+(tip?' title="'+tip+'"':'')+' onclick="hxc.textCheck(\''+name+
            '\',\''+chck+'\');hxc.onChange(\''+name+'\');'+
            '" type="checkbox" class="checkbox_field" style="margin:0px;padding:0px;padding-top:5px;" '+(val != chck ?'checked' : '') + '>&nbsp;');
        if (val == chck) initC.push("hxc.uiDisable('"+name+"');");
    }
    sb.push((label||val||'')+'</span><div id="'+name+'" class="frame_cnt">' + form.buildMarkup(child) + '</div></div>');
    
    return sb.join('');
};

hxc.fieldHandlers['flexibar'] = function(child, name, tip, label, labelConst, val, initC, width, form)
{
    var sb = [];
	
    var hd = formatSizeH(child.attributes['height'], "200"), wd = formatSizeW(child.attributes['width'], "200"), src = child.attributes['src'];
	sb.push('<div id="' + name + '" class="flexigrid' + stdClassMarkup(child) + '">');
	sb.push('<div class="hDiv" style="width:'+wd+';height:'+hd+stdStyleMarkup(child)+'padding:0px;margin:0px;bborder-top:none">');
	 
	sb.push(form.buildMarkup(child));
	
	sb.push('</div></div>');
	
	return sb.join('');
};

hxc.initKB = function()
{
	var d = $(window);
	 
	d.keyup(function(e)
	{
		if (e.which == 17) hxc.isCtrl = false;
		//console.log(e.which);
	}
	);

	d.keydown(function(e)
	{
		if (e.which == 17) hxc.isCtrl = true;
		if (e.which == 81 && hxc.isCtrl == true) // ctrl-q 
		{
			hxc.doXHR(hxc.lastMenu || "manageUsers", null);
			e.preventDefault();
		}
		/*if (e.which == 65 && hxc.isCtrl == true) // ctrl-a 
		{
			if (hxc.lastFunc)
			{
				hxc.doXHR(hxc.lastFunc, hxc.lastParams);
				e.preventDefault();
			}
		}*/// removed as this sometimes prevents the a key from working due to detecting isCtrl even when CTRL is not pressed.
	}
	);
	
	// the following gets set into the home template
	// hxc.sessionTimeout = 60;
	
	hxc.idleTime = 0;
	hxc.warnTime = 30; // this is the number of seconds before session expiry during which the user is warned that the session is about to expire
	hxc.Idle();
	
	// uncomment below to visualize timeout counter
	//
	// hxc.showIdle = function()
	// {
	// 	var o = document.getElementById("el_show");
	// 	if (!o)
	// 	{
	// 		$(document.body).append('<div id="el_show" style="opacity:0.5;background:yellow;position:absolute;top:0px;left:0px;z-index:1000;width:10px;height:20px;border:1px solid orange">bb</div>');
	// 		o = document.getElementById("el_show");
	// 	}
	// 	o.style.width =  (hxc.idleTime*4) + "px"
	// 	o.innerHTML = hxc.idleTime;
	// 	
	// 	setTimeout("hxc.showIdle()", 1000);
	// };
	// hxc.showIdle();
};

hxc.Idle = function()
{
	if (hxc.idleTime >= hxc.sessionTimeout - hxc.warnTime) 
	{
		var caption = "Warning", msg = "Your session will expire in <span id=sessnm>" + (hxc.warnTime-1) + "</span> seconds, \n\nClick cancel to continue without logging out...";
		
	    $('#sessis').remove();
	    var str = '<div id="sessis" title="'+caption+'"><p><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 0 0;"></span>'+msg+'</p></div>';
	    $('#dialog_container').html(str);

	    $('#sessis').dialog({autoOpen:false,width:400,modal:true,resizable:false,
	    	close: function()
	    	{
	    		
	    	},
	        buttons: [{text:"Cancel", click:function() {
	                        $(this).dialog("close");
	                        $('#sessis').remove();
	                        hxc.doXHR("coreClientKeepAlive", "");
	                  		hxc.cancelLast = true;
	                    }}]});
	    $('#sessis').dialog('open');
		
		 hxc.lastSeconds();

	} else {
		hxc.idleTime++;
		setTimeout("hxc.Idle()", 1000);
	} 
};

hxc.lastSeconds = function()
{
	// switch back to idle checking
	if (hxc.cancelLast)
	{
		delete (hxc.cancelLast);
      setTimeout("hxc.Idle()", 1000);
		return;
	}
	
	if (hxc.idleTime >= hxc.sessionTimeout) 
	{
		logout();
		return;
	} else {
		$("#sessnm").html(hxc.sessionTimeout - hxc.idleTime);
		setTimeout("hxc.lastSeconds()", 1000);
		hxc.idleTime++;
	} 
	
};

hxc.isWrapped = function (elementId) 
{
    var span = document.getElementById(elementId);
    if (span)
    {
    	var ot = span.innerHTML;
    	span.innerHTML = '! ' + span.innerHTML;
    	var h1 = az_dhtml.findObjHeight(span);
    	span.innerHTML = "!";
    	var h2 = az_dhtml.findObjHeight(span);
    	span.innerHTML = ot;
    	if (h2 != h1) return true;
    }
    return false;
};

hxc.checkWrapping = function (timed)
{
	hxc.responsiveSpan("lastLogin");
	hxc.responsiveSpan("curUser");
	hxc.responsiveSpan("theVersion");
	hxc.responsiveSpan("cmpy_txt1");
	hxc.responsiveSpan("cmpy_txt2");
	hxc.responsiveSpan("prod_txt");

	hxc.responsiveImage($(".header"), "customer_logo", 46);

	if (timed) setTimeout("hxc.checkWrapping(" + (timed ? 'true' : 'false') + ")", 100);
};

hxc.responsiveImage = function(jqTestEl, imgEl, testH)
{
	var w = parseInt(jqTestEl.width()), h = parseInt(jqTestEl.height()), ol = $g(imgEl);
	
	if (w > parseInt(hxc.appWidth) || h > testH)
	{
		ol.setAttribute("data-old-resize", hxc.appWidth);

		ol.parentNode.parentNode.style.width = "50px";
		ol.parentNode.style.width = "48px";
		ol.parentNode.style.overflow = "hidden";
	} else {
		var w = ol.getAttribute("data-old-resize");
		if (hxc.appWidth > w)
		{
			ol.parentNode.parentNode.style.width = null;
			ol.parentNode.style.width = null;
			ol.removeAttribute("data-old-resize");
		}
	}
};

hxc.responsiveSpan = function(name)
{
	var textName = "data-" + name,  idName = textName + "-idx",  o = document.getElementById(name),  od = o.getAttribute(textName);
	if (o)
	{
		if (od == null)
		{
			var it = od = o.innerHTML;
			o.setAttribute(textName, it);
			o.setAttribute(idName, 0);
			it = it.split("\t")
			o.innerHTML = it[0];
		}
		if (hxc.isWrapped(name))
		{
			var it = o.getAttribute(textName).split("\t"), idx = o.getAttribute(idName);
			if (idx < it.length-1)
			{
				idx++;
				o.innerHTML = it[idx];
				o.setAttribute(idName, idx);
			}
		} else {
			var it = od.split("\t");
			var idx = o.getAttribute(idName);
			if (idx > 0) idx--; 
			var old = o.innerHTML;
			o.innerHTML = it[idx];
			if (hxc.isWrapped(name))
				o.innerHTML = old;
			else
				o.setAttribute(idName, idx);
		}
	}

};

hxc.modScreen = function()
{
    // add fading handlers
    var el = $(".stats");
    
    // some fading
    hxc.statsVisible = true; hxc.statsFading = true;
    el.unbind("mousemove").bind("mousemove", function ()
    {
    	if (!hxc.statsVisible && !hxc.statsFading)
    	{
    		$(".stats").fadeTo(500, 1, function(){ hxc.statsFading=false; hxc.statsVisible = true; });
    		hxc.statsFading = true;
    	}
    	if (hxc.stTim) clearTimeout(hxc.stTim);
        hxc.stTim = setTimeout('$(".stats").fadeTo(1000, 0.25, function(){ hxc.statsVisible=false; })', 10000);
    });
    // fade stats away in 5
    setTimeout('$(".stats").fadeTo(1000, 0.25, function(){ hxc.statsFading=false; hxc.statsVisible=false; })', 1000);
    
	var fc = $("#form_container");
	fc.css("top", "60px")
	
	var th = $(".header");
	th.css("height", "40px")
	var th = $("#topheader");
	th.css("height", "40px")
	
	hxc.switchProduct = function (img, txt)
	{
		document.getElementById("prod_txt").innerHTML = txt;		
		document.getElementById("prod_img").src = img;		
	};

	hxc.initViewPort();
};

$.showprogress = function(progTit, progText, progImg)
{
    $.hideprogress();
    $("BODY").append('<div id="processing_overlay"></div>');
    $("BODY").append(
      '<div id="processing_container">' +
        '<h1 id="processing_title">' + progTit + '</h1>' +
        '<div id="processing_content">' +
          '<div id="processing_message">'+ progText + 
                      '<br/><br/>' + progImg + '</div>' +
        '</div>' +
      '</div>');
     
    var pos = 'fixed'; //($.browser.msie && parseInt($.browser.version) <= 6 ) ? 
               //'absolute' : 'fixed'; 
    
    $("#processing_container").css({
        position: pos,
        zIndex: 99999,
        padding: 0,
        margin: 0
    });
        
    $("#processing_container").css({
        minWidth: $("#processing_container").outerWidth(),
        maxWidth: $("#processing_container").outerWidth()
    });
      
    var top = (($(window).height() / 2) - 
      ($("#processing_container").outerHeight() / 2)) + (-75);
    var left = (($(window).width() / 2) - 
      ($("#processing_container").outerWidth() / 2)) + 0;
    if( top < 0 ) top = 0;
    if( left < 0 ) left = 0;
    
    $("#processing_container").css({
        top: top + 'px',
        left: left + 'px'
    });
    $("#processing_overlay").height( $(document).height() );
};

$.hideprogress = function()
{
    $("#processing_container").remove();
    $("#processing_overlay").remove();
};

hxc.doUploadLogo = function()
{
	var    ext = ".jpg,.png,.gif".split(","),
	     frame = document.getElementById("fupframe"), 
	  innerDoc = frame.contentDocument || frame.contentWindow.document, 
	      form = innerDoc.getElementById('fupfrm'), 
	     field = innerDoc.getElementById('ufile');

	var oneFound = false;
	for (var i=0; i< ext.length; i++)
	{
		var exts = ext[i];
		if (field.value.length == 0 || field.value.lastIndexOf(exts) != field.value.length - exts.length)
		{
		} else {
			oneFound = true;
		}
	}
	if (oneFound)
		form.submit();
	else
		alert("Please choose a file that ends with one of the following extensions : '"+ext.join(", ")); 
};



hxc.uploadLogo = function(e)
{
    var isOpen = document.getElementById('fupframe') != null;

    if (isOpen)
    {
    	
    } else {
	    Opentip.styles.dialog = {  "extends":"glass", tipJoint: "top left" 		};
		//Opentip.styles.b  = { extends:"dark", tipJoint: "bottom" 		};
	
		var code = "<iframe id=fupframe style='width:300px;color:white;height:280px;border:none;color:blue'></iframe>";
		hxc.tip1 = new Opentip(document.getElementById("body_area"), code, 
				{ 
					target:document.getElementById("customer_logo"),
					style: "dialog", 
					showOn: "creation", 
					hideOn:null,
					stemLength:10, 
					stemBase:10,
					showEffectDuration: 0,
				    hideEffectDuration: 0,
					hideTrigger:null,//"closeButton",
					borderColor:"#707070", 
					shadowBlur:"3", 
					shadowColor:"rgba(0,0,0,0.25)",
					removeElementsOnHide:true
				});
		hxc.tip1.show();
		
		var el = document.getElementById("fupframe"), eld = (el.contentWindow) ? el.contentWindow : (el.contentDocument.document) ? el.contentDocument.document : el.contentDocument;
		
		eld.document.open();
		eld.document.write("<body style='color:#4040ff'><b style='font-family:arial;font-size:14px;border-bottom:1px solid silver;color:black'>Insert Customer Logo</b><br/><br/><span style='font-family:arial;font-size:12px'><span style=\"font-weight:bold;color:black\">Step 1: </span>Click the <i style='color:black' >Browse</i> button below to select an image file from your local machine which will be used for your logo.<br/><br/><form method=post name=fupfrm id=fupfrm enctype='multipart/form-data' action='/upi/service'><input style='color:black' name=ufile id=ufile type=file /><br/><br/><span style='font-weight:bold;color:black'>Step 2: </span>Click the <i style='color:black'>Upload</i> button below to upload the selected image file.</br></br><span style='color:black'><button onclick='window.parent.hxc.doUploadLogo();return false;'>Upload</button></span><button onclick='window.parent.hxc.fileUploadCancelled();return false;' style='float:right'>Cancel</button><br/><br/><b style='color:black'>Alternatively</b>, you may also click the <i style='color:black' >Cancel</i> button above to the right to exit this dialog.</form></span></body>");
		eld.document.close;
    }

	$("#customer_logo").one("mousedown", hxc.uploadLogo);
};

hxc.fileUploadDone = function()
{
    $('#fupframe').remove();
    hxc.tip1.hide();
    var img = document.getElementById("customer_logo");
    img.src = "content/mainlogo?" + new Date().getTime();
};

hxc.fileUploadCancelled = function()
{
    $('#fupframe').remove();
    hxc.tip1.hide();
};

hxc.loadTimePicker = function()
{
	if (!hxc.hasTimePicker)
	{
//		hxc.loadStyles("web/css/jquery.datetimepicker")
// 	    hxc.preLoad("content/js/jquery-ui-timepicker-addon", null, "++");
		hxc.hasTimePicker = true;
	}
};

//Normal Ajax Function
hxc.doAjax = function (func_id, params, successCallback, errorCallback, alwaysCallback)
{
	try 
	{
	   hxc.idleTime = 0;
	   hxc.lastFunc = func_id;
	   hxc.lastParams = params;

	   hxc.spinCount++;
      $('#spinner').css("visibility", "visible");

	   var callParames = "func_id=" + func_id;
	   if (params) callParames += "&" + params;
	   $.ajax({
		   url: "service",
		   type: 'POST',
		   data: callParames,
		   dataType: "json"
	   }).done( function(data, textStatus, jqXHR) {
		   successCallback(data, textStatus, jqXHR);
	   }).fail( function(jqXHR, textStatus, errorThrown) {
		   if (typeof errorCallback !== "undefined") errorCallback(jqXHR, textStatus, errorThrown);
	   }).always( function(data, textStatus, jqXHR) {
         hxc.spinCount--;
         if (hxc.spinCount == 0)
            $('#spinner').css("visibility", "hidden");
		   if (typeof alwaysCallback !== "undefined") alwaysCallback(data, textStatus, jqXHR);
	   });
	}
	catch(err)
	{
		if (console) console.error("hxc.doAjax(" + func_id + "," + parms + ",...) Error: " + err);
	}
};

hxc.showSystemLogs = function(startPos, endPos)
{
   hxc.doXHR("viewSystemErrorLogs", "start=" + startPos + "&end=" + endPos);
};

hxc.errorMessageWithLogRef = function(errorMessage, startPos, endPos)
{
   $('#dialog').remove();
   $('#dialog_container').html('<div id="dialog" title="Error"><p><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 0 0;"></span>'+errorMessage+'</p></div>');

   hxc.confirmDialog = $('#dialog').dialog
      (
         {  autoOpen  : false,
            width     : 400,
            modal     : true,
            resizable : false,
         close: function()
         {

         },
            buttons   : 
               [{text: "More...", click : function() 
                  {
                     $(this).dialog("close");
                     $('#dialog').remove();
                     hxc.showSystemLogs(startPos, endPos);
                      $(document).unbind('keypress', hxc.dialogKey);

                  }
               },{text: "OK", click : function() 
                  {
                     $(this).dialog("close");
                     $('#dialog').remove();
                     $(document).unbind('keypress', hxc.dialogKey);

                  }
               }]
         }
      );
   $('#dialog').dialog('open');
   $(document).bind('keypress', hxc.dialogKey);   
};
