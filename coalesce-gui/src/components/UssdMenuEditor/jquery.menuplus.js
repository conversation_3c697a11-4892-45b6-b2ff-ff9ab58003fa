/* eslint-disable */
$.fn.menuplus = function() {
// TODO
}

$.fn.menuplusserialize = function (config, reset) 
{
    var formData = {};
    var $form = $(this);
    
    $form.find("input").each(function(offset, field){
        try {
            var $field = $(field);
            var inputName = $field.attr('name');
            if (typeof inputName !== 'undefined' && inputName !== null && inputName.length > 0) {
                // Purposely done to ignore form fields that have no name attribute.
                // Add the attribute to your code rather than changing this, or things will break.
                var newValue = null; 
                switch ($field.attr("type")) {
                    case 'radio':
                        if ($field.prop('checked')) newValue = $field.val();
                    break;
					default:
					$field.val($.trim($field.val()));
                    newValue = $field.val();
                    break;
                }
                if (typeof newValue !== 'undefined' && newValue !== null) {
                    formData[inputName] = newValue;
                }
            }
        }
        catch(err) {
            console.log(err);
        }
    });

    $form.find("select").each(function(offset, field){
        try {
            var $field = $(field);
            var inputName = $field.attr('name');
            if (typeof inputName !== 'undefined' && inputName !== null && inputName.length > 0) {
                // Purposely done to ignore form fields that have no name attribute.
                // Add the attribute to your code rather than changing this, or things will break.
                var newValue = $field.val();

                if (typeof newValue !== 'undefined' && newValue !== null) {
                    if (typeof newValue == 'string' && newValue.indexOf('{') >= 0) {
                        try {
                            var json = JSON.parse(newValue);
                            for (var key in json) {
                                formData[key] = $.trim(json[key]);
                            }
                        }
                        catch(err) {
                            console.log('Invalid JSON:: '+newValue);
                        }
                    }
                    formData[inputName] = $.trim(newValue);
                }

                //var option = $form.find('#'+inputName+' option:checked').val();
                //formData[inputName] = option;// $field.find(inputName+' option:checked').val();
            }
        }
        catch(err) {
            console.log(err);
        }
    });
	
	if ($form.length > 0 && typeof reset !== 'undefined' && reset !== null && reset) {
		$form[0].reset();    // (A) optional
    }

    return formData;
};

$.fn.menuplusdeserialize = function (config, reset) 
{
	var $form = $(this);
	
	if ($form.length > 0 && typeof reset !== 'undefined' && reset !== null && reset) {
		$form[0].reset();    // (A) optional
    }
    
    for (var fieldName in config) {
        var value = config[fieldName];
        // Find one or more fields
        var $field = $form.find('[name=' + fieldName + ']');
        if ($field.length > 0) {
            // Checkboxes and Radio types need to be handled differently
            if ($field[0].type == "radio" || $field[0].type == "checkbox") 
            {
                var $fieldWithValue = $field.filter('[value="' + value + '"]');
                var isFound = ($fieldWithValue.length > 0);
                // Special case if the value is not defined; value will be "on"
                if (!isFound && value == "on") {
                    $field.first().prop("checked", true);
                } else {
                    $fieldWithValue.prop("checked", isFound);
                } 
            } else { // input, textarea
                $field.val(value);
            }
        }
    }
    return this;
};

(function($){
	$.fn.menuplusPopup = function(){
		return this.each(function(){
			var obj = $(this)
			obj.find('.field').click(function() { //onclick event, 'list' fadein
			obj.find('.list').fadeIn(400);
			
			$(document).keyup(function(event) { //keypress event, fadeout on 'escape'
				if(event.keyCode == 27) {
				obj.find('.list').fadeOut(400);
				}
			});
			
			obj.find('.list').hover(function(){ },
				function(){
					$(this).fadeOut(400);
				});
			});
			
			obj.find('.list li').click(function() { //onclick event, change field value with selected 'list' item and fadeout 'list'
			obj.find('.field')
				.val($(this).html())
				.css({
					'background':'#fff',
					'color':'#333'
				});
			obj.find('.list').fadeOut(400);
			});
		});
	};
})(jQuery);

(function($) {
    $.fn.draggable = function(opt) {

        opt = $.extend({handle:"",cursor:"move"}, opt);
		var $el;
        if(opt.handle === "") {
            $el = this;
        } else {
            $el = this.find(opt.handle);
        }

        return $el.css('cursor', opt.cursor).on("mousedown", function(e) {
			var $target = $(e.target).closest('.nodrag');
			if ($target.length > 0) return;
			var $drag;
            if(opt.handle === "") {
                $drag = $(this).addClass('draggable');
            } else {
                $drag = $(this).addClass('active-handle').parent().addClass('draggable');
            }
            var z_idx = $drag.css('z-index'),
                drg_h = $drag.outerHeight(),
                drg_w = $drag.outerWidth(),
                pos_y = $drag.offset().top + drg_h - e.pageY,
                pos_x = $drag.offset().left + drg_w - e.pageX;
            $drag.css('z-index', 1100).parents().on("mousemove", function(e) {
                $('.draggable').offset({
                    top:e.pageY + pos_y - drg_h,
                    left:e.pageX + pos_x - drg_w
                }).on("mouseup", function() {
                    $(this).removeClass('draggable').css('z-index', z_idx);
                });
            });
        }).on("mouseup", function(e) {
            if(opt.handle === "") {
                $(this).removeClass('draggable');
            } else {
                $(this).removeClass('active-handle').parent().removeClass('draggable');
            }
        });

    }
})(jQuery);