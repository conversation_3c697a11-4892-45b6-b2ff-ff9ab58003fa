/* Start of icons for the menu tree */

.jstree-default .jstree-icon:empty {
    width:0px;
    height:22px;
}

.jstree-default .menuplus-dynamic-menu {
    background: url("tree.png") -472px 0px no-repeat;
}

.jstree-default .menuplus-display {
    background: url("tree.png") -472px 0px no-repeat;
}

.jstree-default .menuplus-service {
    background: url("tree.png") -380px -0px no-repeat;
}

.jstree-default .menuplus-goto {
    background: url("tree.png") -646px 0px no-repeat;
}

 .jstree-default .menuplus-parameters {
    background: url("tree.png") -472px 0px no-repeat;
}

 .jstree-default .menuplus-call {
    background: url("tree.png") -570px 0px no-repeat;
}

 .jstree-default .menuplus-hux {
    background: url("tree.png") -570px 0px no-repeat;
}

 .jstree-default .menuplus-menu {
    background: url("tree.png") -513px 0px no-repeat;
}

 .jstree-default .menuplus-catchall {
    background: url("tree.png") -608px 0px no-repeat;
}

 .jstree-default .menuplus-ifexp {
    background: url("tree.png") -684px 0px no-repeat;
}

 .jstree-default .menuplus-if {
    background: url("tree.png") -684px 0px no-repeat;
}

 .jstree-default .menuplus-assigned {
    background: url("tree.png") -684px 0px no-repeat;
}

 .jstree-default .menuplus-equals {
    background: url("tree.png") -684px 0px no-repeat;
}

 .jstree-default .menuplus-else {
    background: url("tree.png") -703px 0px no-repeat;
}

 .jstree-default .menuplus-ask {
    background: url("tree.png") -494px 0px no-repeat;
}

 .jstree-default .menuplus-ussd {
    background: url("tree.png") -380px 0px no-repeat;
}

 .jstree-default .menuplus-label {
    background: url("tree.png") -741px 0px no-repeat;
}

 .jstree-default .menuplus-end {
    background: url("tree.png") -720px 0px no-repeat;
    width:19px;
    height:22px;
}

 .jstree-default .menuplus-match {
    background: url("tree.png") -627px 0px no-repeat;
}

 .jstree-default .menuplus-oneshot {
    background: url("tree.png") -625px 0px no-repeat;
}

 .jstree-default .menuplus-assign {
    background: url("tree.png") -266px 0px no-repeat;
}

 .jstree-default .menuplus-property {
    background: url("tree.png") -266px 0px no-repeat;
}

 .jstree-default .menuplus-clear {
    background: url("tree.png") -266px 0px no-repeat;
}

 .jstree-default .menuplus-set {
    background: url("tree.png") -266px 0px no-repeat;
}

 .jstree-default .menuplus-itemselection {
    background: url("tree.png") -437px 0px no-repeat;
}

 .jstree-default .menuplus-variableitems {
    background: url("tree.png") -703px 0px no-repeat;
}

 .jstree-default .menuplus-process {
    background: url("tree.png") -228px 0px no-repeat;
}

 .jstree-default .menuplus-item {
    background: url("tree.png") -266px 0px no-repeat;
}

 .jstree-default .menuplus-variables {
    background: url("tree.png") -551px 0px no-repeat;
}

 .jstree-default .menuplus-mpvarlist {
    background: url("tree.png") -551px 0px no-repeat;
}

 .jstree-default .menuplus-var {
    background: url("tree.png") -418px 0px no-repeat;
}

 .jstree-default .menuplus-messages {
    background: url("tree.png") -456px 0px no-repeat;
}

 .jstree-default .menuplus-message {
    background: url("tree.png") -418px 0px no-repeat;
}

 .jstree-default .menuplus-sms {
    background: url("tree.png") -380px 0px no-repeat;
}

 .jstree-default .menuplus-default {
    background: url("tree.png") -380px 0px no-repeat;
}

/* End of icons for the menu tree */


/* Start of icons for the popup context menu tree */

.vakata-context .jstree-icon:empty {
    width:19px;
    height:22px;
}

.vakata-context li > a > i:empty {
    width:0px;
    height:0px;
}

.vakata-context .menuplus-dynamic-menu {
    background: url("menu-icons-24.png") -888px 0px no-repeat;
}

.vakata-context .menuplus-display {
    background: url("menu-icons-24.png") -888px 0px no-repeat;
}

.vakata-context .menuplus-service {
    background: url("menu-icons-24.png") -984px -0px no-repeat;
}

.vakata-context .menuplus-goto {
    /*background: url("menu-icons-24.png") -1392px 0px no-repeat;*/
    background: url("tree.png") -646px 0px no-repeat;
    width:19px !important;
    height:22px !important;
}

.vakata-context .menuplus-parameters {
    background: url("menu-icons-24.png") -984px 0px no-repeat;
}

.vakata-context .menuplus-call {
    background: url("menu-icons-24.png") -984px 0px no-repeat;
}

.vakata-context .menuplus-hux {
    background: url("menu-icons-24.png") -984px 0px no-repeat;
}

.vakata-context .menuplus-menu {
    background: url("menu-icons-24.png") -936px 0px no-repeat;
}

.vakata-context .menuplus-catchall {
    background: url("menu-icons-24.png") -984px 0px no-repeat;
}

.vakata-context .menuplus-ifexp {
    background: url("menu-icons-24.png") -576px 0px no-repeat;
}

.vakata-context .menuplus-if {
    background: url("menu-icons-24.png") -576px 0px no-repeat;
}

.vakata-context .menuplus-assigned {
    background: url("menu-icons-24.png") -576px 0px no-repeat;
}

.vakata-context .menuplus-equals {
    background: url("menu-icons-24.png") -576px 0px no-repeat;
}

.vakata-context .menuplus-else {
    background: url("menu-icons-24.png") -576px 0px no-repeat;
}

.vakata-context .menuplus-ask {
    background: url("menu-icons-24.png") -912px 0px no-repeat;
}

.vakata-context .menuplus-ussd {
    background: url("menu-icons-24.png") -1032px 0px no-repeat;
}

.vakata-context .menuplus-label {
    /*background: url("menu-icons-24.png") -1392px 0px no-repeat;*/
    background: url("tree.png") -741px 0px no-repeat;
    width:19px !important;
    height:22px !important;
}

.vakata-context .menuplus-end {
    background: url("menu-icons-24.png") -1368px 0px no-repeat;
}

.vakata-context .menuplus-match {
    background: url("menu-icons-24.png") -1416px 0px no-repeat;
}

.vakata-context .menuplus-oneshot {
    background: url("menu-icons-24.png") -792px 0px no-repeat;
}

.vakata-context .menuplus-assign {
    background: url("menu-icons-24.png") -696px 0px no-repeat;
}

.vakata-context .menuplus-property {
    background: url("menu-icons-24.png") -456px 0px no-repeat;
}

.vakata-context .menuplus-clear {
    background: url("menu-icons-24.png") -360px 0px no-repeat;
}

.vakata-context .menuplus-set {
    background: url("menu-icons-24.png") -408px 0px no-repeat;
}

.vakata-context .menuplus-itemselection {
    background: url("menu-icons-24.png") -1080px 0px no-repeat;
}

.vakata-context .menuplus-variableitems {
    background: url("menu-icons-24.png") -768px 0px no-repeat;
}

.vakata-context .menuplus-process {
    background: url("menu-icons-24.png") -768px 0px no-repeat;
}

.vakata-context .menuplus-item {
    background: url("menu-icons-24.png") -1080px 0px no-repeat;
}

.vakata-context .menuplus-variables {
    background: url("menu-icons-24.png") -1128px 0px no-repeat;
}

.vakata-context .menuplus-mpvarlist {
    background: url("menu-icons-24.png") -768px 0px no-repeat;
}

.vakata-context .menuplus-var {
    background: url("menu-icons-24.png") -1128px 0px no-repeat;
}

.vakata-context .menuplus-messages {
    background: url("menu-icons-24.png") -768px 0px no-repeat;
}

.vakata-context .menuplus-message {
    background: url("menu-icons-24.png") -1128px 0px no-repeat;
}

.vakata-context .menuplus-sms {
    background: url("menu-icons-24.png") -984px 0px no-repeat;
}

.vakata-context .menuplus-default {
    background: url("menu-icons-24.png") -768px 0px no-repeat;
}


.vakata-context .menuplus-sms {
    background: url("menu-icons-24.png") -984px 0px no-repeat;
}

.vakata-context .menuplus-sms {
    background: url("menu-icons-24.png") -984px 0px no-repeat;
}

.vakata-context .menuplus-submenu-variable {
    background: url("menu-icons-24.png") -840px 0px no-repeat;
}

.vakata-context .menuplus-submenu-if {
    background: url("menu-icons-24.png") -96px 0px no-repeat;
}

.vakata-context .menuplus-submenu-call {
    background: url("menu-icons-24.png") -960px 0px no-repeat;
}

.vakata-context .menuplus-submenu-move {
    background: url("menu-icons-24.png") -864px 0px no-repeat;
}

.vakata-context .menuplus-submenu-add {
    background: url("menu-icons-24.png") -1128px 0px no-repeat;
}

.vakata-context .menuplus-submenu-after {
    background: url("menu-icons-24.png") -1080px 0px no-repeat;
}

.vakata-context .menuplus-submenu-service {
    background: url("menu-icons-24.png") -1128px 0px no-repeat;
}

.vakata-context .menuplus-cut {
    background: url("menu-icons-24.png") -1344px 0px no-repeat;
}

.vakata-context .menuplus-copy {
    background: url("menu-icons-24.png") -1296px 0px no-repeat;
}

.vakata-context .menuplus-paste {
    background: url("menu-icons-24.png") -1320px 0px no-repeat;
}

.vakata-context .menuplus-move-up {
    background: url("menu-icons-24.png") -1200px 0px no-repeat;
}

.vakata-context .menuplus-move-down {
    background: url("menu-icons-24.png") -1224px 0px no-repeat;
}

.vakata-context .menuplus-edit {
    background: url("menu-icons-24.png") -961px 0px no-repeat;
}

.vakata-context .menuplus-delete {
    background: url("menu-icons-24.png") -1104px 0px no-repeat;
}

.vakata-context .menuplus-expand {
    background: url("~/images/expand.png") no-repeat;
    background-size: 22px 22px;
}

.vakata-context .menuplus-contract {
    background: url("~/images/collapse.png") no-repeat;
    background-size: 22px 22px;
}

/* End of icons for the popup context menu tree */


