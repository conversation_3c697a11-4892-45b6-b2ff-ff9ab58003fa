/* eslint-disable */
window.showBSModal = function self(options) {

    // see https://www.ovais.me/javascript/bootstrap-3-modal-easy-way/
    var options = $.extend({
            title : '',
            body : '',
            remote : false,
            backdrop : 'static',
            size : false,
            onShow : false,
            onHide : false,
            actions : false
        }, options);

    self.onShow = typeof options.onShow == 'function' ? options.onShow : function () {};
    self.onHide = typeof options.onHide == 'function' ? options.onHide : function () {};

    if (self.$modal == undefined) {
        self.$modal = $('<div class="modal fade"><div class="modal-dialog"><div class="modal-content"></div></div></div>').appendTo('body');
        self.$modal.on('shown.bs.modal', function (e) {
            self.onShow.call(this, e);
            var $buttonFocus = $('.modal-footer .defaultButton');
            if ($buttonFocus.length > 0) {
                $buttonFocus.select();
                $buttonFocus.focus();
            }
        });
        self.$modal.on('hidden.bs.modal', function (e) {
            $modal = $('.modal');
            $modal.off();
            $modal.remove();
            self.onHide.call(this, e);
        });
    }

    var modalClass = {
        small : "modal-sm",
        large : "modal-lg"
    };

    self.$modal.data('bs.modal', false);
    self.$modal.find('.modal-dialog').removeClass().addClass('modal-dialog ' + (modalClass[options.size] || ''));
    var popupDialog = [];
    popupDialog.push('<div class="modal-header">');
    popupDialog.push(   '<button type="button" class="close" data-dismiss="modal" aria-label="Close">');
    popupDialog.push(       '<span aria-hidden="true">&times;</span>');
    popupDialog.push(   '</button>');
    popupDialog.push(   '<h4 class="modal-title">${title}</h4>');
    popupDialog.push('</div>');
    popupDialog.push('<div class="modal-body">${body}</div>');
    popupDialog.push('<div class="modal-footer"></div>');

    self.$modal.find('.modal-content').html(popupDialog.join('').replace('${title}', options.title).replace('${body}', options.body));

    var footer = self.$modal.find('.modal-footer');
    if (Object.prototype.toString.call(options.actions) == "[object Array]") {
        for (var i = 0, l = options.actions.length; i < l; i++) {
            var currentAction = options.actions[i];
            currentAction.onClick = (typeof currentAction.onClick == 'function') ? currentAction.onClick : function () {};
            var buttonHtml =[];
            buttonHtml.push('<button type="button" class="btn ');
            if (typeof currentAction.default !== 'undefined' && currentAction.default === true)
            {
                buttonHtml.push('defaultButton ');
            }
            buttonHtml.push((currentAction.cssClass || ''));
            buttonHtml.push('"');
            
            buttonHtml.push('>');
            buttonHtml.push((currentAction.label || '{Label Missing!}'));
            buttonHtml.push('</button>');
            var $currentButton = $(buttonHtml.join(''));
            $currentButton.appendTo(footer).on('click', currentAction.onClick);
        }
    } else {
        var $closeButton = $('<button type="button" class="btn btn-default defaultButton" data-dismiss="modal">Close</button>');
        $closeButton.appendTo(footer);
    }

    self.$modal.modal(options);
}