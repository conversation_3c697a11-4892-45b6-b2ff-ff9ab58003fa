/* eslint-disable */
(function($) {
    'use strict';

    var html = [];
    html.push('<div class="device">');
    html.push('   <div class="marvel-device iphone5s">');
    html.push('       <div class="phone-panel">');
    
    html.push('           <div class="msisdn"><span class="phone-label">MSISDN</span><span class="phone-msisdn"></span></div>');
    html.push('          <span class="edit-config glyphicon glyphicon-edit" style="display:none"></span>');

    html.push('           <div class="imsi"><span class="phone-label">IMSI</span><span class="phone-imsi"></span></span></div>');
    html.push('       </div>');
    html.push('       <div class="phone-config">');
    html.push('           <form id="form-ussd-test" style="display:none">');
    html.push('               <div>');
    html.push('                   <div class="col-sm-8">');
    html.push('                       <input class="panel-form-control msisdn" id="testmsisdn" name="testmsisdn" value="" autofocus="" required="" minlength="2" maxlength="50" data-placement="top" placeholder="Source MSISDN ..." type="text"/>');
    html.push('                   </div>');
    html.push('               </div>');
    html.push('               <div>');
    html.push('                   <div class="col-sm-8">');
    html.push('                       <input class="panel-form-control imsi" id="testimsi" name="testimsi" value="" autofocus="" minlength="2" maxlength="50" data-placement="top" placeholder="Source IMSI ..." type="text"/>');
    html.push('                   </div>');
    html.push('               </div>');
    html.push('           </form>');
    html.push('       </div>');
    //html.push('        <div class="speaker"></div>');
    html.push('       <div class="screen">');
    html.push('           <div id="menuplus-response" class="test-output" />');
	html.push('           <div class="dialog-cmdline">');
	html.push('           	<form id="form-ussd-test-command" action="/menuplus/menutree/work" method="POST">');
	html.push('               <input type="text" title="USSD Request String" id="menuplus-request" />');
	html.push('           	</form>');
    html.push('           </div>');
    html.push('       </div>');
    //html.push('       <div class="home"></div>');
    html.push('       <div class="bottom-bar"></div>');
    html.push('   </div>');
    html.push('</div>');

    var ussdPlugin = null;
    var newSession = true;
    var previousResponse = false;
    var notUpdated = true;
    var currentUSSDCode = '';

    function isNewRequest(str) {
        return (str[0] === '*' && str[str.length - 1] === '#');
    };

    function responseToHtml(str) {
		var HTMLResponse = '';
		try {
			var jsonResponse = JSON.parse(str);

			HTMLResponse = $('<p class="test-tool-error">Error: '+jsonResponse.faultCode+'<br />Message: '+jsonResponse.faultString+'</p>');
		}
		catch(err) {
			HTMLResponse = str.replace(/&/g, "&amp;")
			.replace(/</g, "&lt;")
			.replace(/>/g, "&gt;")
			.replace(/"/g, "&quot;")
			//.replace(/\\"/g, "\\")
			.replace(/\n/g, "<br/>")
			.replace(/'/g, "&apos;");
		}

        return HTMLResponse;
    };

    function processTestResponse(response) {
        ussdPlugin.$output.empty();
        if (typeof response.action === 'string' && response.action === 'request') {
            previousResponse = true;
        }
        else if (typeof response.action === 'string' && response.action === 'end') {
            previousResponse = false
            notUpdated = true;
        }
        else {
            previousResponse = false;
        }
        ussdPlugin.$output.html(responseToHtml(response.USSDResponseString));
    };

    function fireTestRequest(params)
    {
        var menuUrl = '/menuplus/ussdTest';
        var huxRequest = {
            MSISDN: params.MSISDN,
            USSDRequestString: params.USSDRequestString,
            response: previousResponse
        };

        

        if (typeof params.IMSI !== 'undefined' && params.IMSI != null && params.IMSI.length > 0) {
            huxRequest.IMSI = params.IMSI;
        }

        if (params.newSession || isNewRequest(huxRequest.USSDRequestString)) {
            var reqs = params.USSDRequestString.substr(1, params.USSDRequestString.length-2);
            var i = reqs.indexOf('*');
            if (i > 0)
            {
                currentUSSDCode = reqs.substr(0, i);
                huxRequest.USSDServiceCode = currentUSSDCode;
                huxRequest.USSDRequestString = params.USSDRequestString;
            }
            else
            {
                currentUSSDCode = reqs;
                huxRequest.USSDServiceCode = currentUSSDCode;
                huxRequest.USSDRequestString = params.USSDRequestString;
            }
            

            delete huxRequest.response;
        }
        else {
            huxRequest.USSDServiceCode = currentUSSDCode;
        }
        var jqxhr = $.post( menuUrl, huxRequest, function(resp) {
            processTestResponse(resp);
        })
        .fail(function(error) {
            ussdPlugin.$output.empty();
            ussdPlugin.$output.html(responseToHtml(jqxhr.responseText));
        });
    };

    var UssdTester = function (element, options) {
        this.options    = null;
        this.$element   = null;
        this.enabled    = null;
        notUpdated = true;
        this.init('ussdtester', element, options);
    }

    UssdTester.VERSION  = '1.0.0';

    UssdTester.DEFAULTS = {
        msisdn: '',
        imsi: '',
        draggable: false,
        validation: {
            rules: {
                testmsisdn: {
                    required: true,
                    minlength: 1,
                    digits: true
                },
                testimsi: {
                    required: true,
                    minlength: 1,
                    digits: true
                }
            },
            messages: {
                testmsisdn: "Please enter a valid MSISDN",
                testimsi: "Please enter a valid IMSI"
            }
        }
    }

    UssdTester.prototype.canUseConfig = function (state) {
        // Called after config has been validated, saved or whater.  Currently unused.
        notUpdated = false
        return state;
    }

    UssdTester.prototype.validateConfig = function (success, error) {
        var result = true;
        if (this.formState === 'visible') {
            var imsi = ussdPlugin.$formImsi.val();
            var msisdn = ussdPlugin.$formMsisdn.val();
            if (typeof msisdn === 'undefined' && msisdn === null && msisdn.length <= 0) {
                result = false;
            }
            if (typeof imsi === 'undefined' && imsi === null && imsi.length <= 0) {
                result = false;
            }
            if (result) {
                ussdPlugin.options.msisdn = msisdn;
                ussdPlugin.options.imsi = imsi;
            }
        }
        
        if (result === true && notUpdated && typeof ussdPlugin.options.validConfig === 'function') {
            var details = {
                MSISDN: ussdPlugin.options.msisdn,
                IMSI: ussdPlugin.options.imsi,
                profile: 'dev'
            };
            ussdPlugin.options.validConfig(details, function(status) {
                var newStatus = ussdPlugin.canUseConfig(status);
                if (newStatus) {
                    if (typeof success === 'function') success(newStatus);
                }
                else {
                    if (typeof error === 'function') error(newStatus);
                }
            });
        }
        else {
            if (typeof success === 'function') success(true);
        }
        return result;
    }

    UssdTester.prototype.validConfig = function () {
        var result = false;
        var msisdn = this.options.msisdn;
        if (typeof msisdn !== 'undefined' && msisdn !== null && msisdn.length > 0) {
            result = true;
        }
        return result;
    }

    UssdTester.prototype.init = function (type, element, options) {
        ussdPlugin = this;
        this.enabled   = true;
        this.formState   = 'hidden';
        this.$element  = $(element);
        this.$element.html(html.join(''));
        this.$configForm = $('#form-ussd-test');
        this.$configDisplay = $('#cs-menuplus-test .marvel-device');
        this.$editConfigBtn = $('#cs-menuplus-test .edit-config');

        this.$displayMsisdn = this.$configDisplay.find('.phone-msisdn');
        this.$displayImsi = this.$configDisplay.find('.phone-imsi');
        this.$formMsisdn = this.$configForm.find('#testmsisdn');
        this.$formImsi = this.$configForm.find('#testimsi');

		//this.$userInput = $('#menuplus-request');
		this.$userInput = $('#menuplus-request');
		this.$userInputForm = $('#form-ussd-test-command');
		
        this.$output = $("#menuplus-response");

        this.options = this.getOptions(options);
        this.$configForm.validate(this.options.validation);

        /*if (this.options.draggable) {
            this.$element.find('.device').draggable({
                handle: ".marvel-device"
            });
		}*/
		if (this.options.draggable) {
            this.$element.find('.device').draggable({
                handle: ".marvel-device"
            });
		}
        

        this.$userInput.val('');

        if (this.validConfig()) {
            this.displayConfig();
            ussdPlugin.$editConfigBtn.show();
            this.$userInput.focus();
        }
        else {
            this.formState = 'visible';
            this.$configForm.show();
            this.$formMsisdn.focus();
        }
		this.$editConfigBtn.off().on('click', this.editConfigClick);
		
        this.$userInput.off().on({
            'focusin': this.handleFocusInEvent,
            'keypress': this.handleTestRequestEvent,
		});

		var states = ['Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California',
    'Colorado', 'Connecticut', 'Delaware', 'Florida', 'Georgia', 'Hawaii',
    'Idaho', 'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky', 'Louisiana',
    'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota',
    'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire',
    'New Jersey', 'New Mexico', 'New York', 'North Carolina', 'North Dakota',
    'Ohio', 'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode Island',
    'South Carolina', 'South Dakota', 'Tennessee', 'Texas', 'Utah', 'Vermont',
    'Virginia', 'Washington', 'West Virginia', 'Wisconsin', 'Wyoming'
  ];

		$('#menuplus-request').typeahead({
			source: function (query, process) {
				return $.ajax({
					url: '/menuplus/history',
					data: {term: query},
					dataType: 'json'
				})
				.done(function(response) {
					return process(response);
				});
			},
			minLength: 1,
			appendTo: $('#cs-menuplus-test .device'),
			//selectOnBlur: false,
			autoSelect: false
		});

        this.$element.on({
            'click': this.handleClickEvent,
            'keypress': this.handleKeyPressEvent,
		});
		
		this.$userInputForm.on('submit', function(event) {
			var isvalid = false;
			if (ussdPlugin.formState === 'visible') {
				if (ussdPlugin.$configForm.valid()) {
					if (ussdPlugin.validateConfig(function(status) {
						var params = {
							USSDRequestString: ussdPlugin.$userInput.val(),
							MSISDN: ussdPlugin.options.msisdn,
							IMSI: ussdPlugin.options.imsi,
							newSession: newSession
						};
						fireTestRequest(params);
						newSession = false;
						ussdPlugin.$userInput.val('');
					}));
				}
			}
			else {
				isvalid = true;
			}
			if (isvalid) {
				if (ussdPlugin.validateConfig(function(status) {
					var params = {
						USSDRequestString: ussdPlugin.$userInput.val(),
						MSISDN: ussdPlugin.options.msisdn,
						IMSI: ussdPlugin.options.imsi,
						newSession: newSession
					};
					fireTestRequest(params);
					newSession = false;
					ussdPlugin.$userInput.val('');
				}));
			}
			event.stopPropagation();
			event.preventDefault();
		});
    }

    UssdTester.prototype.handleFocusInEvent = function (event) {
        if (ussdPlugin.formState === 'visible') {
            if (ussdPlugin.$configForm.valid()) {
                if (ussdPlugin.validateConfig(function(status) {
                    ussdPlugin.displayConfig();
                    ussdPlugin.$configForm.hide();
                    ussdPlugin.formState   = 'hidden';
                    notUpdated = true;
                    ussdPlugin.$editConfigBtn.show();
                }));
                return false;
            }
        }
    }

    UssdTester.prototype.editConfigClick = function (event) {
        ussdPlugin.formState = 'visible';
        ussdPlugin.$configForm.show();
        ussdPlugin.$formMsisdn.focus();
        ussdPlugin.$editConfigBtn.hide();
    }

    UssdTester.prototype.displayConfig = function () {
        ussdPlugin.$displayMsisdn.text(ussdPlugin.options.msisdn);
        ussdPlugin.$displayImsi.text(ussdPlugin.options.imsi);
    }

    UssdTester.prototype.handleClickEvent = function (event) {
        if (ussdPlugin.formState === 'hidden') {
            ussdPlugin.$userInput.focus();
        }
    }

    UssdTester.prototype.handleFocusOutEvent = function (event) {
        var result = true;
        console.log('handleFocusOutEvent');
        if (ussdPlugin.formState === 'visible') {
            if (ussdPlugin.$configForm.valid()) {
                if (ussdPlugin.validateConfig(function(status) {
                    ussdPlugin.displayConfig();
                    ussdPlugin.$configForm.hide();
                    ussdPlugin.formState   = 'hidden';
                    notUpdated = true;
                    ussdPlugin.$userInput.removeAttr('disabled');
                    ussdPlugin.$editConfigBtn.show();
                    ussdPlugin.$userInput.focus();
                }));
                return false;
            }
        }
        else if (ussdPlugin.formState === 'hidden') {
            ussdPlugin.$userInput.focus();
        }
        return false;
    }

    UssdTester.prototype.handleKeyPressEvent = function (event) {
        var key = event.which || event.charCode || event.keyCode;
        switch (key) {
            case 13:
                if (ussdPlugin.$configForm.valid()) {
                    if (ussdPlugin.validateConfig(function(status) {
                        ussdPlugin.displayConfig();
                        ussdPlugin.$configForm.hide();
                        ussdPlugin.formState   = 'hidden';
                        notUpdated = true;
                        ussdPlugin.$userInput.removeAttr('disabled');
                        ussdPlugin.$editConfigBtn.show();
                        ussdPlugin.$userInput.focus();
                    }));
                    return false;
                }
                event.stopPropagation();
                event.preventDefault();
            break;
        }
    }

    UssdTester.prototype.handleTestRequestEvent = function (event) {
        var key = event.which || event.charCode || event.keyCode;
        switch (key) {
			case 13:
				ussdPlugin.$userInputForm.submit();
                return false;
            break;
		}
		return true;
    }

    UssdTester.prototype.getDefaults = function () {
        return UssdTester.DEFAULTS;
    }

    UssdTester.prototype.getOptions = function (options) {
        options = $.extend({}, this.getDefaults(), this.$element.data(), options);
    
        return options;
    }

    // UssdTester PLUGIN DEFINITION
    // =========================

    function Plugin(option) {
        return this.each(function () {
        var $this   = $(this);
        var data    = $this.data('cs.ussdtester');
        var options = typeof option == 'object' && option;

        if (!data && /destroy|hide/.test(option)) return;
        if (!data) $this.data('cs.ussdtester', (data = new UssdTester(this, options)));
        if (typeof option == 'string') data[option]();
        })
    }

    var old = $.fn.ussdTester;

    $.fn.ussdTester             = Plugin;
    $.fn.ussdTester.Constructor = UssdTester;


    // UssdTester NO CONFLICT
    // ===================

    $.fn.ussdTester.noConflict = function () {
        $.fn.ussdTester = old;
        return this;
    }

})(jQuery);