/* eslint-disable */
(function($) {
    'use strict';

    var curLang = null;
    var langlist = null;
    var currentconfig = null;
    var variableList = null;

    function updateJsTree($container, configurl) {
        var $jstree = $container.jstree(true);
        if (!$jstree) {
            if (!this.options.configurl)this.options.configurl = configurl;
            $container.jstree(this.options);
            mpJstreePlugin.$jstree = $container.jstree(true);
            $jstree = mpJstreePlugin.$jstree;
        }

        if (typeof $jstree.settings !== 'undefined') $jstree.settings.core.data = toJstreeConfig(currentconfig.service, 'node_0');
        $jstree.refresh(true);
        $jstree.deselect_all(true);
    };

    function toJstreeConfig(cfg, parentid, preserve, parentNode) {// not yet working
        var name = 0, attr = 1, content = 2, chil = 3;
        var name = cfg[name], children = cfg[chil], content = cfg[content];
        var treeConfig = {};
        
        cfg[attr]['parentid'] = parentid;
        
        treeConfig.type = name;
        if (typeof cfg[attr].nodeid !== 'undefined' && cfg[attr].nodeid !== null) treeConfig.id = cfg[attr].nodeid;
        treeConfig.parentid = parentid;
        treeConfig.text = getNodeText(cfg, parentNode);
        
        if (typeof children !== 'undefined') {
                treeConfig.children = [];
                for (var i=0; i<children.length; i++) {
                    treeConfig.children.push(toJstreeConfig(children[i], cfg[attr].nodeid, preserve, cfg));
                }
        }
        return treeConfig;
    };

    function getTreeConfig(obj, callback) {
        var treeConfig = toJstreeConfig(currentconfig.service, 'node_0');
        callback.call(this, treeConfig);
    };

    function duplicateUssdCode(parentid, code) {
        /*var parentNode = getMenuPlusNode(parentid);

        var count = 0;
        for (var us=0; us<parentNode.confignode[3].length; us++) {
            var currentChild = parentNode.confignode[3][us];
            if (currentChild[1].code === code) {
                count++;
            }
        }
        return (count > 1);*/
        return false;
    };

    function hasValue(name) {
        return (typeof name !== 'undefined' && name !== null && name.length > 0);
    };

    function getCatchAllInfo(n)
    {
        var hop = n[attr], hv = hop.variable, hc = hop.code;
        if (hv == null || hv.length == 0 || typeof hv == 'undefined') hv = "";
        var sb = [];

        sb.push("http://");
        sb.push(hop.address);
        sb.push(":");
        sb.push(hop.port);
        sb.push(hop.url);
        sb.push("&nbsp;&rArr;&nbsp;");
        if (typeof hc === 'undefined' || hc === null || hc.length === 0) {
            sb.push("[sent code]");
        }
        else {
            sb.push(hc);
        }
        sb.push(convertVariables(hop.request));
        if (typeof hv === 'undefined' || hv === null || hv.length === 0) {
            sb.push("");
        }
        else {
            sb.push("&nbsp;&rarr;<span class=disp_var>");
            sb.push(findVariableNameById(hv));
            sb.push("</span>");
        }
        return sb.join(' ');
    }

    function getHUXInfo(config)
    {
        var ht =[];
        var attributes = config[attr];
        if (hasValue(attributes.server) && hasValue(attributes.displayname)) {
            /*var parts = attributes.server.split('-');
            parts = parts.splice(1);
            ht.push(parts.join('-'));*/
            ht.push(attributes.displayname);
        }
        else {
            ht.push("http://");
            if (hasValue(attributes.address))ht.push(attributes.address);
            ht.push(":");
            if (hasValue(attributes.port))ht.push(attributes.port);
            if (hasValue(attributes.url))ht.push(attributes.url);
        }

        ht.push("&nbsp;&rArr;&nbsp;");
        if (hasValue(attributes.code)) {
            ht.push(attributes.code);
        }
        else {
            ht.push("[sent code]");
        }
        ht.push(convertVariables(attributes.request));

        if (hasValue(attributes.variable)) {
            ht.push("&nbsp;&rarr;<span class=disp_var>");
            ht.push(findVariableNameById(attributes.variable));
            ht.push("</span>");
        }
        else {
            ht.push("");
        }

        return ht.join(' ');
    };

    function getExpressionTitle(operator) {
        var title = '';
        switch (operator) {
            case "eq" :
            title = 'This node will be traversed if the specified variable equals the specified constant';
            break;
            case "gt" :
            title = 'This node will be traversed when the previous if condition fails to match';
            break;
            case "gte" :
            title = 'This node will be traversed when the previous if condition fails to match';
            break;
            case "lt" :
            title = 'This node will be traversed when the previous if condition fails to match';
            break;
            case "lte" :
            title = 'This node will be traversed when the previous if condition fails to match';
            break;
            case "ne" :
            title = 'This node will be traversed when the previous if condition fails to match';
            break;
            default:
            // TODO error handling
            break;
        }
        return title;
    };

    function getCatchAllInfo(n)
    {
        var hop = n[attr], hv = hop.variable, hc = hop.code;
        if (hv == null || hv.length == 0 || typeof hv == 'undefined') hv = "";
        var sb = [];

        sb.push("http://");
        sb.push(hop.address);
        sb.push(":");
        sb.push(hop.port);
        sb.push(hop.url);
        sb.push("&nbsp;&rArr;&nbsp;");
        if (typeof hc === 'undefined' || hc === null || hc.length === 0) {
            sb.push("[sent code]");
        }
        else {
            sb.push(hc);
        }
        sb.push(convertVariables(hop.request));
        if (typeof hv === 'undefined' || hv === null || hv.length === 0) {
            sb.push("");
        }
        else {
            sb.push("&nbsp;&rarr;<span class=disp_var>");
            sb.push(findVariableNameById(hv));
            sb.push("</span>");
        }
        return sb.join(' ');
    };

    function getExpressionHtml(hop) {
        var html = [], className = 'pe_if_'+hop.operator;
        html.push('<span class="disp_var pe_if_a">');
        html.push(findVariableNameById(hop.variable));
        html.push('</span>');

        html.push('&nbsp;');
        switch (hop.operator) {
            case "eq" :
            html.push('==');
            break;
            case "gt" :
            html.push('&gt;');
            break;
            case "gte" :
            html.push('&gt;=');
            break;
            case "lt" :
            html.push('&lt;');
            break;
            case "lte" :
            html.push('&lt;=');
            break;
            case "ne" :
            html.push('!=');
            break;
            default:
            // TODO error handling
            break;
        }
        html.push('&nbsp;');

        html.push('<span class="disp_const ');
        html.push(className);
        html.push('">');
        html.push(hop.constant);
        html.push('</span>');
        return html.join('');
    };

    function getIfExpressionInfo(node)
    {
        var hop = node[attr];

        return {
            title: getExpressionTitle(hop.operator),
            html: getExpressionHtml(hop)
        };
    };

    function escapeText(str) {
        //if (typeof str === undefined) debugger;
        try {
            return str.replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/\\"/g, "\\")
            //.replace(/\n/g, "<br/>")
            .replace(/'/g, "&apos;");
        }
        catch(err) {
            console.log(err);
        }
    };

    function formatForDisplayInvalid(name, classname) {
        var sb = [];
        sb.push('<span title="Undefined variable" class="disp_var_invalid ');
        if (typeof classname !== 'undefined' && classname !== null) {
            sb.push(classname);
        }
        sb.push('" ');

        var offsets = '';
        if (offsets.length > 0) {
            sb.push('" style="');
            sb.push(offsets);
            sb.push('"');
        }
        sb.push('>');
        sb.push(name);
        sb.push('</span>');
        return sb.join('');
    };

    function formatForDisplay(name, classname) {
        var sb = [];
        sb.push('<span class="disp_var ');
        if (typeof classname !== 'undefined' && classname !== null) {
            sb.push(classname);
        }
        sb.push('" ');

        var offsets = '';
        if (offsets.length > 0) {
            sb.push('" style="');
            sb.push(offsets);
            sb.push('"');
        }
        sb.push('>');
        sb.push(name);
        sb.push('</span>');
        return sb.join('');
    };

    function findVariableNameById(id) {
        var varname = id;

        for (var z=0; z<variableList.length; z++) {
            if (variableList[z].id === id) {
                varname = variableList[z].name;
                break;
            }
        }
        return varname;
    };

    function getConstantForDisplay(id, classname) {
        var sb = [];
        sb.push('<span class="');
        if (typeof classname !== 'undefined' && classname !== null) {
            sb.push(classname);
        }
        else {
            sb.push('disp_const');
        }
        sb.push('">');
        sb.push(id);
        sb.push('</span>');
        return sb.join('');
    };

    function getVariableForDisplay(id, classname) {
        var varname = id;
        var found = false;
        var mpvarlist = currentconfig.mpvarlist;

        for (var z=0; z<variableList.length; z++) {
            if (variableList[z].id === id) {
                varname = variableList[z].name;
                found = true;
                break;
            }
        }
        if (typeof mpvarlist !== 'undefined' && mpvarlist !== null) {
            for (var y=0; y<mpvarlist.length; y++) {
                if (mpvarlist[y].name === id) {
                    varname = mpvarlist[y].name;
                    found = true;
                    break;
                }
            }
        }
        
				varname = formatForDisplay(varname, classname);
        return varname;
    };

    function convertVariables(text) {
        var result,regex = new RegExp(/%([\(\)\^&#*\"$\w:0-9-_]+)%/g);
        var replacements = {};
        
        while(result = regex.exec(text)) {
            replacements[result[1]] = findVariableNameById(result[1], variableList);
        }
        for (var placeholder in replacements) {
            text = text.split('%'+placeholder+'%').join(getVariableForDisplay(placeholder));
        }
        //console.log(text);
        return text;
    };

    function convertLineFeeds(i)
    {
        i = escapeText(i);
        i = convertVariables(i);
        var crImg = '<span class="menuplus-cr-image"></span>';
        return i.replace(/\n/g, crImg).replace(/\\n/g, crImg);
    };

		

    function listVariables(config, variableList) {
        //console.log(JSON.stringify(config, null, 3));
        //console.log(typeof config);
        if (typeof variableList === 'undefined' || variableList == null) variableList = [];

        if (config[name] === 'variables') {
            var varData = config[chil];
            if (typeof varData !== 'undefined' && varData != null) {
                for (var i=0; i<varData.length; i++) {
                    if (varData[i][name] === 'var') variableList.push(varData[i][attr]);
                }
            }
        }
        else if (config[name] === 'service' || config[name] === 'properties' || config[name] === 'mpvarlist' || config[name] === 'langs') {
            return;
        }
        else if (config[name] === 'messages') {
            return;
        }
        else if (config.length > chil) {
            for (var i=0; i<config[chil].length; i++) {
                listVariables(config[chil][i], variableList);
            }
        }
        //else {
            //console.log("Ignoring unexpected type "+typeof config+((typeof config === 'object')?JSON.stringify(config):''));
        //}

        return variableList;
    };

    function getNodeText(cfg, parentNode) {
        var imgdir = path;
        var name = 0, attr = 1, content = 2;
        var attributes = cfg[attr], content = cfg[content];
        var nodeText = [];

				debugger;
        switch (cfg[name]) {
            case "service":
                var i = attributes.name;
                // initialize edit language
                var editLanguage = attributes["editLanguage"] || "ENG";
                if (curLang == null)
                    curLang = editLanguage;

                nodeText.push('<span class="mp_desc pe_srvs" title="Service \'');
                nodeText.push(i);
                nodeText.push('\'"></span>&nbsp;');
                nodeText.push(i);
            break;

            case "goto":
                nodeText.push('<span class="mp_desc pe_goto_hdr" title="Goto - click to edit">&nbsp;Goto ');
                nodeText.push('<span class="menuplus-property-image" title="Edit properties"></span>');

                nodeText.push('<span style="color:#505050;position:relative;left:5px;font-weight:normal">');

                switch(parseInt(attributes.location || 0)) {
                    case 0:
                        nodeText.push("End");
                    break;
                    case 1:
                        nodeText.push("Previous");
                    break;
                    case 2:
                        nodeText.push("Root");
                    break;
                    case 3:
                        nodeText.push('<span class=pe_menu>' +attributes.target+ '&nbsp;&nbsp;</span>');
                    break;
                    default:
                        nodeText.push('&lt;<i style="color:red">none</i>&gt;');
                    break
                }

                nodeText.push('</span>');

                nodeText.push('</span>');;
            break;

            case "parameters":
                nodeText.push('<span class="mp_desc pe_param" title="Parameters">Parameters</span>');
            break;

            case "call": 
                nodeText.push('<span class="mp_desc pe_call" title="Call Server \'');
                nodeText.push(attributes.type);
                nodeText.push('\'">');
                nodeText.push(i);
                nodeText.push('</span>');
            break;

            case "hux":
                nodeText.push('<span class="mp_desc pe_call" title="Call a HUX service - click to edit"">HUX Call ');
                nodeText.push('<span class="menuplus-hux-image" title="Edit properties"></span>');

                nodeText.push('<span style="color:#505050;margin-left:10px;">');
                
                nodeText.push(getHUXInfo(cfg));
                nodeText.push('</span>');
                nodeText.push('</span>');
            break;

            case "menu":
                var menuName = attributes.name|| attributes.code;
                var menuText = menuName?menuName:"Menu";
                menuNumberMap.set(menuName, 1);

                if (!attributes.cnt) attributes.cnt = "000"

                nodeText.push('<span class="mp_desc pe_menu" title="');
                nodeText.push(menuText);
                nodeText.push('" with USSD request %path$#">');
                nodeText.push(convertLineFeeds(menuText));
                nodeText.push('&nbsp;</span>');

            break;

            case "item":
                var menuName = parentNode[1].name|| parentNode[1].code;
                var itemNumber = menuNumberMap.get(menuName);
                if (typeof attributes.number === "undefined" || attributes.number == null)
                {
                    attributes.number = (itemNumber++) + "";
                }
                menuNumberMap.set(menuName, itemNumber);

                var pname = curLang ? curLang + "_text" : "text";	
                var i = attributes[pname] || attributes["text"];
                
                nodeText.push('<span class="mp_desc pe_item" title="Menu item with USSD request %path$');
                nodeText.push(attributes.number);
                nodeText.push('#"><b class="pe_num">');
                nodeText.push(attributes.number);
                nodeText.push('</b> ');
                nodeText.push(convertLineFeeds(i));
                nodeText.push('</span>');

            break;

            case "catchall": 
                nodeText.push('<span class="mp_desc pe_catchall" title="HUX CatchAll - click to edit">HUX Catch All ');

                nodeText.push('<span class="menuplus-catchall-image" title="Edit properties"></span>');

                nodeText.push('<span style="color:#505050;position:relative;left:10px;font-weight:normal">');
                nodeText.push(getCatchAllInfo(cfg));
                nodeText.push('</span>');

                nodeText.push('</span>');
            break;

            case "ifexp":
                var expInfo = getIfExpressionInfo(cfg);
                nodeText.push('<span title="');
                nodeText.push(expInfo.title);
                nodeText.push('" class="mp_desc pe_expression">&nbsp;IF');
                nodeText.push(expInfo.html);
                nodeText.push('</span>');
            break;

            case "assigned":
                nodeText.push('<span title="This node will be traversed if the specified variable has any value" class="mp_desc pe_assigned">&nbsp;IF ');
                nodeText.push(getVariableForDisplay(attributes.variable, 'pe_if_a'));
                nodeText.push('</span>');
            break;

            case "if":
                switch (attributes.type) {
                    case "assigned":
                        nodeText.push('<span title="This node will be traversed if the specified variable has any value" class="mp_desc pe_assigned">&nbsp;IF ');
                        nodeText.push(getVariableForDisplay(attributes.variable, 'pe_if_a'));
                        nodeText.push('</span>');
                    break;
                    case "equals":
                        nodeText.push('<span title="This node will be traversed if the specified variable equals the specified constant" class="mp_desc pe_equal">&nbsp;IF ');
                        nodeText.push(getVariableForDisplay(attributes.variable, 'pe_if_a'));
                        nodeText.push('&nbsp;equals&nbsp;');
                        nodeText.push(getConstantForDisplay(attributes.constant));
                    break;
                    default:
                        nodeText.push('<span class="mp_desc pe_depends" >&nbsp;');
                        nodeText.push(findVariableNameById(attributes.variable));
                        nodeText.push('<span class="pe_assign_eq">&nbsp;being&nbsp;</span>"');
                        nodeText.push(attributes.value);
                        nodeText.push('"</span>');
                    break;
                }
            break;

            case "else":
                nodeText.push('<span class="mp_desc pe_else" title="Performs action if previous IF did not match" /> Else</span>');
            break;

            case "ask":
                var pname = curLang ? curLang + "_text" : "text",
                    i = attributes[pname] || attributes["text"], 
                    s = '<span class="pe_disp">'+convertLineFeeds(i)+'</span>', 
                    isAsk = i.trim().length != 0;


                nodeText.push('<span class="mp_desc pe_ask_hdr">');
                nodeText.push(isAsk?'Ask':'Wait for input');;
                nodeText.push(isAsk ? s: "");
                nodeText.push('<span class="pe_ask_save_hdr">');
                nodeText.push(isAsk?'save reply':'saving it');
                nodeText.push(' as</span>');
                nodeText.push(getVariableForDisplay(attributes.variable));
                nodeText.push('</span>');
                nodeText.push('');
            break;

            case "ussd":
                if (duplicateUssdCode(attributes.parentid, attributes.code)) {
                    nodeText.push('<span class="mp_desc pe_ussd_duplicate" title="USSD Shortcode \'');
                }
                else {
                    nodeText.push('<span class="mp_desc pe_ussd" title="USSD Shortcode \'');
                }
                
                nodeText.push(attributes.code);
                nodeText.push('\'">');
                nodeText.push(attributes.code);
                nodeText.push('</span>');
            break;

            case "label":
                nodeText.push('<span class="mp_desc pe_label_hdr"');
                nodeText.push('title="Label - Use a goto node to jump to this">&nbsp;Label');
                nodeText.push('<span class="pe_label_content">');
                nodeText.push(attributes.name);
                nodeText.push('</span>');
                nodeText.push('</span>');
                nodeText.push('');
                nodeText.push('');
            break;

            case "end":
                nodeText.push('<span class="mp_desc pe_end" title="Ends the user menu session" /> End Session</span>');
            break;

            case "display":
                var pname = curLang ? curLang + "_text" : "text", text = attributes[pname] || attributes["text"];

                nodeText.push('<span class="mp_desc pe_disp_hdr" title="Display a message to the user">&nbsp;Display<span class="pe_disp">');
                nodeText.push(convertLineFeeds(text));
                nodeText.push('</span></span>');
            break;

            case "dynamic-menu":
                var pname = curLang ? curLang + "_text" : "text", text = attributes[pname] || attributes["text"];

                nodeText.push('<span class="mp_desc pe_disp_hdr" title="Display a message to the user">&nbsp;Dynamic-Menu<span class="pe_disp">');
                nodeText.push(convertLineFeeds(text));
                nodeText.push('</span></span>');
            break;

            case "match":
                nodeText.push('<span class="mp_desc pe_match" title="Match static requests to nodes">Match Request</span>&nbsp;&nbsp;');
                nodeText.push('<span class="pe_disp">');
                nodeText.push(attributes.request);
                nodeText.push('</span>');
                nodeText.push('');
            break;

            case "oneshot":
                nodeText.push('<span class="mp_desc pe_oneshot" title="Matches full requests to nodes">OneShot Match</span>&nbsp;&nbsp;');
                nodeText.push('<span class="pe_disp">');
                nodeText.push(convertLineFeeds(attributes.match));
                nodeText.push('</span>');
                nodeText.push('');
                nodeText.push('');
            break;

            case "assign":
                nodeText.push('<span class="mp_desc pe_assign" >');
                nodeText.push(getVariableForDisplay(attributes.variable, 'pe_if_a'));
                nodeText.push('=<span class=disp_const>');
                nodeText.push(attributes.value);
                nodeText.push('</span></span>');
            
            break;

            case "property":
                nodeText.push('<span class="mp_desc pe_property" >');
                nodeText.push(getVariableForDisplay(attributes.variable));
                nodeText.push('= Value Of Property <span class=disp_property>');
                nodeText.push(attributes.property);
                nodeText.push('</span></span>');
            break;
            case "clear":
                nodeText.push('<span class="mp_desc pe_clear">Clear');
                nodeText.push(getVariableForDisplay(attributes.variable, 'pe_if_a'));
                nodeText.push('</span>');
            break;

            case "set":
                var currentVar = attributes.variable;
                if (typeof currentVar !== 'undefined' && currentVar !== null && currentVar.indexOf('=') > 0) {
                    // var contains an '=' char
                    var tmpvar = currentVar.split('=');
                    currentVar = tmpvar[0];
                }

                nodeText.push('<span class="mp_desc pe_set">Set');
                nodeText.push(getVariableForDisplay(attributes.variable, 'pe_if_a'));
                nodeText.push('</span>');
            break;

            case "itemselection":
                /*{
                    // count the number of children and make the index that
                    //if (!parent[attr]["index"])
                    //	parent[attr].index = '1';
                    var len = parent[chil].length, i=0, inum = 0;
                    for (i=0; i<len; i++)
                    {
                        if (parent[chil][i] == n) inum = i+1;
                    }
                    //var inum = n[attr].index
                    return {ic:23, text:'<span class="pe_itemsel" >&nbsp;#'+inum+'</span>'};
                }*/
                // Need to obtain parent from config
                nodeText.push('TODO, Tell me if you ever see this');
            break;

            case "variableitems":
                nodeText.push('<span class="mp_desc pe_vitems" >&nbsp;Variable Items from ');
                nodeText.push(getVariableForDisplay(attributes.variable));
                nodeText.push('</span>');
                nodeText.push('');
            break;

            case "process":
                nodeText.push('<span class="mp_desc pe_proc" title="Process"> Process</span>');
            break;

            case "variables":
                nodeText.push('<span class="mp_desc pe_vars">&nbsp;Variables</span>');
            break;

            case "mpvarlist":// maybe not needed
                nodeText.push('<span class="mp_desc pe_vars">&nbsp;Variables</span>');
            break;

            case "var":// maybe not needed
                nodeText.push('<span class="mp_desc pe_var">');
                nodeText.push(attributes.name);
                nodeText.push(' : <b/>');
                nodeText.push('attributes.description');
                nodeText.push('</b>');
                nodeText.push('</span>');
            break;

            case "messages":// maybe not needed
                nodeText.push('<span class="mp_desc pe_msgs">&nbsp;Messages</span>');
            break;

            case "message":// maybe not needed
                nodeText.push('<span class="mp_desc pe_msg">');
                nodeText.push(attributes.id);
                nodeText.push(' : <b/>');
                nodeText.push(attributes.description);
                nodeText.push('</b>');
                nodeText.push('</span>');
            break;

            case "sms":// maybe not needed
                nodeText.push('<span class="mp_desc pe_sms">');
                nodeText.push(attributes.text);
                nodeText.push('</span>');
            break;

            default:
                nodeText.push(attributes.text);
            break;
        }
        return nodeText.join('');
    }

    var MpJstree = function (element, options) {
        this.options    = null;
        this.$element   = null;
        this.enabled    = null;
        this.init('mpjstree', element, options);
    }

    MpJstree.VERSION  = '1.0.0';

    MpJstree.DEFAULTS = {
        configurl: false,
        selectlanguage: '#displayLanguage',
        readonly: false,
        plugins : ["wholerow", "state", "inlinehtml", "types", "contextmenu"],
        types : treeTypeConfig,
        state : {
            key : "menuplus.default"
        },
        core : {
            multiple: false, // Do not allow multiple nodes to be selected.
            data : getTreeConfig
        },
        contextmenu: {
            select_node: false, // Do not allow multiple nodes to be selected.
            show_at_node: false, // Make menu appear at mouse co-ordinates rather than node
            // NB arrow function used so that this is set correctly.  Won't
            /*items: (node, callback) => {
                MenuPlusContextMenu.showContextMenu(node, callback);
            }*/
        }
    }

    var mpJstreePlugin = null;

    MpJstree.prototype.init = function (type, element, options) {
        mpJstreePlugin = this;
        this.enabled   = true;
        this.$element  = $(element);

        this.options = this.getOptions(options);

        if (!this.options.readonly) {
            this.$element.on("select_node.jstree", options.select_node);
        }

        var $selectLanguage = $(this.options.selectlanguage);
        var $treeContainer = this.$element;
        $selectLanguage.on('change', function(ev) {
            //console.log($selectLanguage.val());
            curLang = $selectLanguage.val();
            updateJsTree($treeContainer);
        });

        if (this.options.configurl !== false) {
            var treeOptions = this.options;
            var jqxhr = $.get(this.options.configurl)
            .done(function(data) {
                variableList = listVariables(data.variables);
                currentconfig = data;
                mpJstreePlugin.$jstree = $(element).jstree(treeOptions);
            })
            .fail(function(err) {
              //mpJstreePlugin.$jstree = $(element).jstree(treeOptions);
            })
            .always(function() {
            
            });
        }
        else {
            $(element).html('No configuration available, please set a configuration on the Server Profile tab');
            //mpJstreePlugin.$jstree = $(element).jstree(this.options);
        }
    }

    MpJstree.prototype.setConfig = function(cfg, langs) {
        langlist = langs;
        currentconfig = cfg;
        variableList = listVariables(cfg);
    }

    MpJstree.prototype.getDefaults = function () {
        return MpJstree.DEFAULTS;
    }

    MpJstree.prototype.getOptions = function (options) {
        options = $.extend({}, this.getDefaults(), this.$element.data(), options);
        //console.log('jstree mode :- '+this.options.readonly);
        if (options.readonly) {
            delete options.dnd;
            delete options.core.check_callback;
            delete options.select_node;
            options.contextmenu.items =  MenuPlusContextMenu.showReadOnlyContextMenu;
        }
        else {
            options.plugins.push("dnd");
            options.contextmenu.items = options.context_menu;
        }
    
        return options;
    }

    MpJstree.prototype.instance = function () {
        return this;
    }

    MpJstree.prototype.reloadConfig = function (configurl) {
        var that = this;
        var jqxhr = $.get(configurl)
            .done(function(data) {
                variableList = listVariables(data.variables);
                currentconfig = data;
                updateJsTree.call(that, that.$element, configurl);
            })
            .fail(function(err) {
              console.log("error "+err);
            })
            .always(function() {
            
            });
    }

    // MpJstree PLUGIN DEFINITION
    // =========================

    function Plugin(option) {
        return this.each(function () {
            var $this   = $(this);
            var currentData    = $this.data();
            var data    = $this.data('mpjstree');
            var options = typeof option == 'object' && option;

            if (!data && /destroy|hide/.test(option)) return;
            if (!data) {
                $this.data('mpjstree', (data = new MpJstree(this, options)));
            }
            if (typeof option == 'string') return data[option]();
            if (Array.isArray(option) && option.length === 2 && typeof option[0] === 'string') {
                return data[option[0]](option[1]);
            }
        })
        
    }

    var old = $.fn.mpJstree;

    $.fn.mpJstree             = Plugin;
    $.fn.mpJstree.Constructor = MpJstree;

    // MpJstree NO CONFLICT
    // ===================

    $.fn.mpJstree.noConflict = function () {
        $.fn.mpJstree = old;
        return this;
    }

})(jQuery);