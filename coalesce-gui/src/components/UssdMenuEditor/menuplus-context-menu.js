/* eslint-disable */
let MenuPlus = null;
window.MenuPlusContextMenu = {
  imgdir: "/",

  simpleMenuItem: function(label, typeAction, icon, shortcut) {
    var menuConfig = {
      label: label,
      type: typeAction,
      action: MenuPlus.contextClick
    };

    var menuType;
    if (typeof typeAction === "string") {
      menuType = MenuPlus.getType(typeAction);
      menuConfig.icon = "menuplus-" + typeAction;
    } else {
      if (typeof typeAction === "object") {
        menuType = MenuPlus.getType(typeAction.type);
        menuConfig.icon = "menuplus-" + typeAction.type;
        menuConfig.mode = typeAction.mode;
      } else {
        if (typeof typeAction === "function") {
          menuConfig.action = typeAction;
				}
				// Remove menu icons
        if (typeof icon !== "undefined") {
					menuConfig.icon = icon;
				}
        else if (
          typeof menuType !== "undefined" &&
          typeof menuType.icon !== "undefined"
        )
          menuConfig.icon = menuType.icon;
        else {
          menuConfig.icon = "fa fa-magic fa-fw";
          //menuConfig.icon = path+'default.png';
				}
      }
		}
		menuConfig.icon = null;

    if (typeof shortcut !== "undefined") menuConfig.shortcut = type.icon;
    return menuConfig;
  },

  addCutCopyPasteSubmenu: function(menu, options) {
    menu.addCopy = MenuPlusContextMenu.simpleMenuItem(
      "Copy",
      MenuPlus.copyNode,
      "menuplus-copy"
    );
    var pasteConfig = MenuPlusContextMenu.simpleMenuItem(
      "Paste",
      MenuPlus.pasteNode,
      "menuplus-paste"
    );
    pasteConfig._disabled = MenuPlus.pasteNodeDisabled;

    menu.addPaste = pasteConfig;
    menu.addCut = MenuPlusContextMenu.simpleMenuItem(
      "Cut",
      MenuPlus.cutNode,
      "menuplus-cut"
    );
    return menu;
  },

  addCutCopyMove: function(menu, options) {
    menu.addCopy = MenuPlusContextMenu.simpleMenuItem(
      "Copy",
      MenuPlus.copyNode,
      "menuplus-copy"
    );
    menu.addCut = MenuPlusContextMenu.simpleMenuItem(
      "Cut",
      MenuPlus.cutNode,
      "menuplus-cut"
    );

    MenuPlusContextMenu.addMoveSubmenu(menu, options);
    return menu;
  },

  addVariableSubmenu: function(menu, options, position) {
    var menuConfig = {
      /*addSet: MenuPlusContextMenu.simpleMenuItem("Set", {
        type: "set",
        mode: position
      }),
      addClear: MenuPlusContextMenu.simpleMenuItem("Clear", {
        type: "clear",
        mode: position
      }),*/
      addAssign: MenuPlusContextMenu.simpleMenuItem("Assign", {
        type: "assign",
        mode: position
      })/*,
      addProperty: MenuPlusContextMenu.simpleMenuItem("Property", {
        type: "property",
        mode: position
      })*/
    };

    menu.addVariableSub = {
      label: "Variable",
      action: function() {
        console.log("addVariableSubmenu");
      },
      icon: "menuplus-submenu-variable",
      submenu: menuConfig
    };
    return menu;
  },

  addCallSubmenu: function(menu, submenuItems) {
    let menuConfig = {};

    submenuItems.forEach(submenuItem => {
        const { methodName, displayName, config } = submenuItem;
        menuConfig[methodName] = MenuPlusContextMenu.simpleMenuItem(displayName, config);
    });

    menu.addCallSub = {
        label: "Call",
        action: function() {
            console.log("addCallSubmenu");
        },
        icon: "menuplus-submenu-call",
        submenu: menuConfig
    };
    return menu;
},

  addMoveSubmenu: function(menu) {
    var moveUp = MenuPlusContextMenu.simpleMenuItem(
      "Move Up",
      MenuPlus.moveUp,
      "menuplus-move-up"
    );
    var moveDown = MenuPlusContextMenu.simpleMenuItem(
      "Move Down",
      MenuPlus.moveDown,
      "menuplus-move-down"
    );
    moveUp._disabled = MenuPlus.cannotMoveNodeUp;
    moveDown._disabled = MenuPlus.cannotMoveNodeDown;
    var menuConfig = {
      addMoveUp: moveUp,

      addMoveDown: moveDown
    };

    menu.addMoveSub = {
      label: "Move",
      action: function() {
        console.log("addMoveSub");
      },
      icon: "menuplus-submenu-move",
      submenu: menuConfig
    };
    return menu;
  },

  addAddSubmenu: function(menu, options, parentType) {
    //var labelText = (typeof label !== 'undefined' && label !== null && label.length > 0)?label:"Add";
    var labelText = "Add";

		var menuConfig = {};
		
		if (treeTypeConfig.childTypeAllowedByName("item", parentType))
			menuConfig.addMenuItem = MenuPlusContextMenu.simpleMenuItem("Menu Item", {
				type: "item",
				mode: "after"
			});

    if (treeTypeConfig.childTypeAllowedByName("display", parentType))
      menuConfig.addDisplay = MenuPlusContextMenu.simpleMenuItem("Display", {
        type: "display",
        mode: "add"
      });
    if (treeTypeConfig.childTypeAllowedByName("ask", parentType))
      menuConfig.addAsk = MenuPlusContextMenu.simpleMenuItem("Ask", {
        type: "ask",
        mode: "add"
      });
    if (treeTypeConfig.childTypeAllowedByName("menu", parentType))
      menuConfig.addMenu = MenuPlusContextMenu.simpleMenuItem("Menu", {
        type: "menu",
        mode: "add"
      });
    if (treeTypeConfig.childTypeAllowedByName("label", parentType))
      menuConfig.addLabel = MenuPlusContextMenu.simpleMenuItem("Label", {
        type: "label",
        mode: "add"
      });
    if (treeTypeConfig.childTypeAllowedByName("goto", parentType))
      menuConfig.addGoto = MenuPlusContextMenu.simpleMenuItem("Goto", {
        type: "goto",
        mode: "add"
      });
    //if (treeTypeConfig.childTypeAllowedByName('match', parentType)) menuConfig.addMatch = MenuPlusContextMenu.simpleMenuItem("Match", {type:'match', mode:'add'});
    if (treeTypeConfig.childTypeAllowedByName("oneshot", parentType))
      menuConfig.addOneShot = MenuPlusContextMenu.simpleMenuItem("OneShot", {
        type: "oneshot",
        mode: "add"
      });

    MenuPlusContextMenu.addVariableSubmenu(menuConfig, options, "add");
    MenuPlusContextMenu.addIfSubmenu(menuConfig, options, "add");
    MenuPlusContextMenu.addCallSubmenu(menuConfig, [
        {
            methodName: "addCallComponent",
            displayName: "Component",
            config: {
                type: "component",
                mode: "add"
            }
        },
        {
            methodName: "addCallModule",
            displayName: "Module",
            config: {
                type: "menuModule",
                mode: "add"
            }
        }
    ]);
    menuConfig.addEnd = MenuPlusContextMenu.simpleMenuItem("End", {
      type: "end",
      mode: "add"
    });

    menu.addSubmenu = {
      label: labelText,
      action: function() {
        console.log("addAddSubmenu");
      },
      icon: "menuplus-submenu-add",
      submenu: menuConfig
    };
    return menu;
  },

  addAfterSubmenu: function(menu, options, parentType) {
		//debugger
    //var labelText = (typeof label !== 'undefined' && label !== null && label.length > 0)?label:"Add";
    var labelText = "After";

		var menuConfig = {};

		if (treeTypeConfig.childTypeAllowedByName("item", parentType))
			menuConfig.addMenuItem = MenuPlusContextMenu.simpleMenuItem("Menu Item", {
				type: "item",
				mode: "after"
			});

    menuConfig.addDynamicMenu = MenuPlusContextMenu.simpleMenuItem("Dynamic Menu", {
      type: "dynamic-menu",
      mode: "after"
    });

    if (treeTypeConfig.childTypeAllowedByName("display", parentType))
      menuConfig.addDisplay = MenuPlusContextMenu.simpleMenuItem("Display", {
        type: "display",
        mode: "after"
      });
    if (treeTypeConfig.childTypeAllowedByName("ask", parentType))
      menuConfig.addAsk = MenuPlusContextMenu.simpleMenuItem("Ask", {
        type: "ask",
        mode: "after"
      });
    if (treeTypeConfig.childTypeAllowedByName("menu", parentType))
      menuConfig.addMenu = MenuPlusContextMenu.simpleMenuItem("Menu", {
        type: "menu",
        mode: "after"
      });
    if (treeTypeConfig.childTypeAllowedByName("label", parentType))
      menuConfig.addLabel = MenuPlusContextMenu.simpleMenuItem("Label", {
        type: "label",
        mode: "after"
      });
    if (treeTypeConfig.childTypeAllowedByName("goto", parentType))
      menuConfig.addGoto = MenuPlusContextMenu.simpleMenuItem("Goto", {
        type: "goto",
        mode: "after"
      });
    //if (treeTypeConfig.childTypeAllowedByName('match', parentType)) menuConfig.addMatch = MenuPlusContextMenu.simpleMenuItem("Match", {type:'match', mode:'after'});
    if (treeTypeConfig.childTypeAllowedByName("oneshot", parentType))
      menuConfig.addOneShot = MenuPlusContextMenu.simpleMenuItem("OneShot", {
        type: "oneshot",
        mode: "after"
      });

		MenuPlusContextMenu.addVariableSubmenu(menuConfig, options, "after");
		MenuPlusContextMenu.addIfSubmenu(menuConfig, options, "after");
        MenuPlusContextMenu.addCallSubmenu(menuConfig, [
            {
                methodName: "addCallComponent",
                displayName: "Component",
                config: {
                    type: "component",
                    mode: "after"
                }
            },
            {
                methodName: "addCallModule",
                displayName: "Module",
                config: {
                    type: "menuModule",
                    mode: "after"
                }
            }
        ]);

    menuConfig.addEnd = MenuPlusContextMenu.simpleMenuItem("End", {
      type: "end",
      mode: "after"
    });

    menu.afterSubmenu = {
      label: labelText,
      action: function() {
        console.log("afterSubmenu");
      },
      icon: "menuplus-submenu-after",
      submenu: menuConfig
    };
    return menu;
  },

  addCommonItems: function(menu, options, parentType) {
    MenuPlusContextMenu.addAddSubmenu(menu, options, parentType);
    MenuPlusContextMenu.addCutCopyPasteSubmenu(menu, options);
    MenuPlusContextMenu.addMoveSubmenu(menu, options);

    return menu;
  },

  addAddMenuItemSubmenu: function(menu, options, parentType) {
    var menuConfig = {
      addDynamicMenu: MenuPlusContextMenu.simpleMenuItem("Dynamic Menu", {
        type: "dynamic-menu",
        mode: "add"
      }),
      addMenuItem: MenuPlusContextMenu.simpleMenuItem("Menu Item", {
        type: "item",
        mode: "add"
      })
    };
		
		MenuPlusContextMenu.addVariableSubmenu(menuConfig, options, "add");
		MenuPlusContextMenu.addIfSubmenu(menuConfig, options, "add");
    //menuConfig.addVariableItems = MenuPlusContextMenu.simpleMenuItem("Variable Items", {type:'variableitems', mode:'add'});
    menuConfig.addDisplay = MenuPlusContextMenu.simpleMenuItem("Display", {
      type: "display",
      mode: "add"
		});
		if (treeTypeConfig.childTypeAllowedByName("menu", parentType))
      menuConfig.addMenu = MenuPlusContextMenu.simpleMenuItem("Menu", {
        type: "menu",
        mode: "add"
      });
    if (treeTypeConfig.childTypeAllowedByName("label", parentType))
      menuConfig.addLabel = MenuPlusContextMenu.simpleMenuItem("Label", {
        type: "label",
        mode: "add"
			});
		if (treeTypeConfig.childTypeAllowedByName("oneshot", parentType))
      menuConfig.addOneShot = MenuPlusContextMenu.simpleMenuItem("OneShot", {
        type: "oneshot",
        mode: "add"
      });
			if (treeTypeConfig.childTypeAllowedByName("call", parentType))
				MenuPlusContextMenu.addCallSubmenu(menuConfig, [
						{
								methodName: "addCallComponent",
								displayName: "Component",
								config: {
										type: "component",
										mode: "add"
								}
						},
						{
								methodName: "addCallModule",
								displayName: "Module",
								config: {
										type: "menuModule",
										mode: "add"
								}
						}
				]);

    menu.addMenuItemSubmenu = {
      label: "Add",
      action: function() {
        console.log("addAddMenuItemSubmenu");
      },
      icon: "menuplus-submenu-add",
      submenu: menuConfig
    };
    return menu;
  },

  addAddMenuItemAfterSubmenu: function(menu, options, parentType, position) {
      console.log('position', position)
    var menuConfig = {
      addMenuItem: MenuPlusContextMenu.simpleMenuItem("Menu Item", {
        type: "item",
        mode: position
      })
		};
		
    //MenuPlusContextMenu.addIfSubmenu(menuConfig, options, position);
    /*menuConfig.addVariableItems = MenuPlusContextMenu.simpleMenuItem(
      "Variable Items",
      { type: "variableitems", mode: position }
    );*/
    menuConfig.addDisplay = MenuPlusContextMenu.simpleMenuItem("Display", {
      type: "display",
      mode: position
		});
		
		MenuPlusContextMenu.addVariableSubmenu(menuConfig, options, position);
		MenuPlusContextMenu.addIfSubmenu(menuConfig, options, position);
        MenuPlusContextMenu.addCallSubmenu(menuConfig, [
            {
                methodName: "addCallComponent",
                displayName: "Component",
                config: {
                    type: "component",
                    mode: position
                }
            },
            {
                methodName: "addCallModule",
                displayName: "Module",
                config: {
                    type: "menuModule",
                    mode: position
                }
            }
        ]);

    menu.addMenuItemAfterSubmenu = {
      label: "After",
      action: function() {
        console.log("addAddMenuItemSubmenu");
      },
      icon: "menuplus-submenu-after",
      submenu: menuConfig
    };
    return menu;
  },

  addIfSubmenu: function(menu, options, position) {
    var elseItem = MenuPlusContextMenu.simpleMenuItem("Else", {
      type: "else",
      mode: position
    });
    elseItem._disabled = MenuPlus.preventAttachToNode;

    var menuConfig = {
      /*addAssigned: MenuPlusContextMenu.simpleMenuItem("Assigned", {
        type: "assigned",
        mode: position
      }),*/
      addExpression: MenuPlusContextMenu.simpleMenuItem("Expression", {
        type: "ifexp",
        mode: position
      })
      //addElse: elseItem
    };

    menu.addIfSub = {
      label: "If",
      action: function() {
        console.log("addIfSubmenu");
      },
      icon: "menuplus-submenu-if",
      submenu: menuConfig
    };
    return menu;
  },

  configureMenuItemIf: function(menu, options, position, label) {
    var elseItem = MenuPlusContextMenu.simpleMenuItem("Else", {
      type: "else",
      mode: position
    });
    elseItem._disabled = MenuPlus.preventAttachToNode;

    var menuConfig = {
      addMenuItemToMenuIf: MenuPlusContextMenu.simpleMenuItem("Menu Item", {
        type: "item",
        mode: position
      }),
      addElseToMenuIf: elseItem,
      /*addVariableItemsToMenuIf: MenuPlusContextMenu.simpleMenuItem(
        "Variable Items",
        { type: "variableitems", mode: position }
      ),*/
      addDisplayToMenuIf: MenuPlusContextMenu.simpleMenuItem("Display", {
        type: "display",
        mode: position
      })
    };

    menu.addMenuIfSub = {
      label: label,
      action: function() {
        console.log("addIfSubmenu");
      },
      icon: "menuplus-submenu-add",
      submenu: menuConfig
    };
    return menu;
  },

  configureMenuItemElse: function(menu, options) {
    var menuConfig = {
      addMenuItemToMenuIf: MenuPlusContextMenu.simpleMenuItem(
        "Menu Item",
        "item"
      ),
      /*addVariableItemsToMenuIf: MenuPlusContextMenu.simpleMenuItem(
        "Variable Items",
        "variableitems"
      ),*/
      addDisplayToMenuIf: MenuPlusContextMenu.simpleMenuItem(
        "Display",
        "display"
      )
    };

    menu.addMenuIfSub = {
      label: "Add",
      action: function() {
        console.log("addIfSubmenu");
      },
      icon: "menuplus-submenu-add",
      submenu: menuConfig
    };
    return menu;
  },

  configureIfSubmenu: function(menuConfig, options, node) {
    var ismenu = false;
    if (typeof node.parent !== "undefined" && node.parent !== null) {
      var parent = MenuPlus.getMenuPlusNode(node.parent);
      if (parent.treenode.type === "menu") {
        ismenu = true;
      }
    }
    /*if (typeof node.parents === 'array') {
			for (var i=0; i<node.parents; i++) {

			}
		}*/
    if (ismenu) {
      MenuPlusContextMenu.configureMenuItemIf(menuConfig, options, "after", "After");
      MenuPlusContextMenu.configureMenuItemIf(menuConfig, options, "add", "Add");
      MenuPlusContextMenu.addCutCopyPasteSubmenu(menuConfig, options);
      MenuPlusContextMenu.addMoveSubmenu(menuConfig, options);
    } else {
      MenuPlusContextMenu.addAfterSubmenu(menuConfig, options, node.type);
      MenuPlusContextMenu.addCommonItems(menuConfig, options, node.type);
    }
  },

  configureElseSubmenu: function(menuConfig, options, node) {
    var ismenu = false;
    if (typeof node.parent !== "undefined" && node.parent !== null) {
      var parent = MenuPlus.getMenuPlusNode(node.parent);
      if (
        parent.treenode.type === "if" ||
        parent.treenode.type === "ifexp" ||
        parent.treenode.type === "assigned" ||
        parent.treenode.type === "equals"
      ) {
        if (typeof parent.treenode.parentid === "string") {
          var grandParent = MenuPlus.getMenuPlusNode(parent.treenode.parentid);
          if (grandParent.treenode.type === "menu") {
            ismenu = true;
          }
        }
      }
    }
    /*if (typeof node.parents === 'array') {
			for (var i=0; i<node.parents; i++) {

			}
		}*/
    if (ismenu) {
      MenuPlusContextMenu.configureMenuItemElse(menuConfig, options);
      MenuPlusContextMenu.addCutCopyPasteSubmenu(menuConfig, options);
      MenuPlusContextMenu.addMoveSubmenu(menuConfig, options);
    } else {
      MenuPlusContextMenu.addCommonItems(menuConfig, options, node.type);
    }
  },

  showContextMenu: function(node, callback) {
    var menuConfig = {};
    var options = {};
    var nodeType = treeTypeConfig.getTypeByName(node.type);
    var parentNode = null;
    var parentType = null;
    if (node.parent === "#") {
			parentNode = MenuPlus.getMenuPlusNode('#');
    } else {
      parentNode = MenuPlus.getMenuPlusNode(node.parent);
		}
		parentType = parentNode.treenode.type;

    if (nodeType.hasOwnProperty("context") && nodeType.context.edit) {
      menuConfig.editNode = MenuPlusContextMenu.simpleMenuItem(
        nodeType.context.edit,
        "edit"
      );
      menuConfig.editNode.separator_after = true;
    }

		console.log('Node type '+node.type);
    switch (node.type) {
      case "service":
				MenuPlusContextMenu.addAddMenuItemSubmenu(menuConfig, options, parentType);
        MenuPlusContextMenu.addCutCopyPasteSubmenu(menuConfig, options);
        MenuPlusContextMenu.addMoveSubmenu(menuConfig, options);
        //MenuPlusContextMenu.addCommonItems(menuConfig, options, node.type);
        //var pasteConfig = MenuPlusContextMenu.simpleMenuItem("Paste", MenuPlus.pasteNode, 'menuplus-paste');
        //pasteConfig._disabled = MenuPlus.pasteNodeDisabled
        //menuConfig.addPaste = pasteConfig;
        break;

      case "ifexp":
      case "if":
      case "assigned":
      case "equals":
        MenuPlusContextMenu.configureIfSubmenu(menuConfig, options, node);
        break;

      case "else":
        MenuPlusContextMenu.configureElseSubmenu(menuConfig, options, node);
        break;

      case "menu":
				//debugger;
        MenuPlusContextMenu.addAfterSubmenu(menuConfig, options, parentType);
        MenuPlusContextMenu.addAddMenuItemSubmenu(menuConfig, options, parentType);
        MenuPlusContextMenu.addCutCopyPasteSubmenu(menuConfig, options);
        MenuPlusContextMenu.addMoveSubmenu(menuConfig, options);
        break;
      case "match":
      case "oneshot":
        MenuPlusContextMenu.addAfterSubmenu(menuConfig, options, parentType);
        MenuPlusContextMenu.addCommonItems(menuConfig, options, node.type);
        break;
      case "item":
        MenuPlusContextMenu.addAddMenuItemAfterSubmenu(
          menuConfig,
          options,
					parentType,
					"after"
				);
				MenuPlusContextMenu.addAddSubmenu(menuConfig, options, node.type);
				MenuPlusContextMenu.addCutCopyPasteSubmenu(menuConfig, options);
				MenuPlusContextMenu.addMoveSubmenu(menuConfig, options);

        //MenuPlusContextMenu.addCommonItems(menuConfig, options, node.type);
        break;

      case "dynamic-menu":
      case "display":
      case "end":
      case "label":
      case "property":
      case "goto":
      case "hux":
      case "catchall":
      case "ask":
      case "assign":
      case "clear":
      case "set":
      case "component":
      case "menuModule":
      case "itemselection":
      case "variableitems":
        MenuPlusContextMenu.addAfterSubmenu(menuConfig, options, parentType);
        MenuPlusContextMenu.addCutCopyMove(menuConfig, options);
        break;

      // internal, so no menu
      case "parameters":
      case "call":
      case "process":
      case "variables":
      case "mpvarlist": // maybe not needed
      case "var": // maybe not needed
      case "messages": // maybe not needed
      case "message": // maybe not needed
      case "sms": // maybe not needed
      break;

      default:
			console.log(
				"Unmatched node type for context menu ||" + node.type + "||"
			);
			break;
    }

    var expandAllConfig = MenuPlusContextMenu.simpleMenuItem(
      "Expand All",
      MenuPlus.expandAll,
      "menuplus-expand"
    );
    //expandAllConfig._disabled = MenuPlus.pasteNodeDisabled
    menuConfig.expandAll = expandAllConfig;

    var contractAllConfig = MenuPlusContextMenu.simpleMenuItem(
      "Collapse All",
      MenuPlus.contractAll,
      "menuplus-contract"
    );
    //expandAllConfig._disabled = MenuPlus.pasteNodeDisabled
    menuConfig.contractAll = contractAllConfig;

    if (nodeType.hasOwnProperty("context") && nodeType.context.del) {
      menuConfig.deleteNode = MenuPlusContextMenu.simpleMenuItem(
        nodeType.context.del,
        "delete"
      );
      menuConfig.deleteNode.separator_before = true;
    }

    //$('#cs-menuplus-debug').append('<pre>'+JSON.stringify(menuConfig, null, 2)+'</pre>') ;
    return menuConfig;
  },

  showReadOnlyContextMenu: function(node, callback) {
    var menuConfig = {};

    var expandAllConfig = MenuPlusContextMenu.simpleMenuItem(
      "Expand All",
      MenuPlus.expandAll,
      "menuplus-expand"
    );
    menuConfig.expandAll = expandAllConfig;

    var contractAllConfig = MenuPlusContextMenu.simpleMenuItem(
      "Collapse All",
      MenuPlus.contractAll,
      "menuplus-contract"
    );
    menuConfig.contractAll = contractAllConfig;

    //$('#cs-menuplus-debug').append('<pre>'+JSON.stringify(menuConfig, null, 2)+'</pre>') ;
    return menuConfig;
  },
  init: function(menu) {
    MenuPlus = menu;
  }
};
