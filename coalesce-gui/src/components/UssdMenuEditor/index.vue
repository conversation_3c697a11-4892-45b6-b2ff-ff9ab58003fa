<template>
  <div class="row treecanvas" @contextmenu="showContextMenu">
    <div class="col-12">
      <div style="padding: 0 15px;">
        <div id="testJq"></div>
        <div id="menutree">
          <div id="jstree-container"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved,no-unused-vars */
import { mapGetters } from 'vuex';

import MainDialog from '@/components/menuitems/MainDialog.vue';
import Vue from 'vue';

// import "jstree";

// const uuid = require('uuid/v1');

// let jstreePlugins = require("./jstree-plugins.js");

// debugger
const menuplusTypes = require('./menuplus-types');
const menuplusContextMenu = require('./menuplus-context-menu');
const menuplusJstree = require('./menuplus-jstree');
// let menuplusTables = require("./menuplus-tables");
// let jqueryMenuplus = require("./jquery.menuplus");
const menuplus = require('./menuplus');
// let varPopup = require("./var_popup");
const util = require('./util');

window.MenuPlusContextMenu.init(window.MenuPlus);

const { $ } = window;

function thisContext(node, x, y) {}

function download(filename, text) {
  const element = document.createElement('a');
  element.setAttribute('href', `data:text/plain;charset=utf-8,${encodeURIComponent(text)}`);
  element.setAttribute('download', filename);

  element.style.display = 'none';
  document.body.appendChild(element);

  element.click();

  document.body.removeChild(element);
}

export default {
  name: 'UssdMenuEditor',
  components: {},
  props: {
    isActive: {
      type: Boolean,
      default: false,
    },
    menuConfig: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      callback: null,
      origConfig: null,
      mode: null,
    };
  },
  computed: {
    ...mapGetters(['treecode']),
  },
  async mounted() {
    // debugger
    if (typeof window.MP_config === 'undefined') window.MP_config = {};
    // debugger
    await this.$store.dispatch('modules/selectmodule', this.$route.params);

    // debugger
    // let json = JSON.parse(this.treecode)
    const newconfig = {};
    window.MenuPlus.copyConfig(this.treecode, newconfig);
    window.MP_config.root = newconfig;

    const langs = {
      ENG: 'English',
      FRA: 'French',
      EWE: 'Ewe',
    };
    window.MenuPlus.init(false, this.treecode, langs);

    // https://github.com/dmlzj/vue-jstree
    const $container = $('#menutree');
    // inlineedit.coalesce
    $container.on('inlineedit.coalesce', this.editEvent);
    $container.on('inlinesave.coalesce', this.saveEvent);
    /*
     * NOTE: inlinesave.coalesce event is also caught in DEBUGGER (../UssdDebugger/index.vue)
     */
  },
  beforeDestroy() {
    $('#menutree').off('inlineedit.coalesce', this.editEvent);
    $('#menutree').off('inlinesave.coalesce', this.saveEvent);
  },
  methods: {
    showContextMenu(event) {
      // alert("test")
      // debugger
      if (this.treecode.length > 3 && this.treecode[3].length > 0) {
        //
      } else {
        $('#menutree')
          .find('.jstree-node')
          .children('.jstree-anchor')
          .trigger('contextmenu');
        $('#jstree-contextmenu').focus();
      }

      // $(".jstree-contextmenu").css({top: event.pageY, left: event.pageX});
    },
    contextClick(menu) {
      const $jstree = $.jstree.reference(menu.reference);
      const treeNode = $jstree.get_node(menu.reference);
      this.$emit('context-action-menu', this.menuConfig, treeNode, menu);
    },

    processUpdate(type, data) {
      // action, $dialog, node, formData, config, mode
      this.callback(type, null, this.origConfig, data, null, this.mode);
    },

    treeEvent(eventName, ev, data) {
      this.$emit(eventName, ev, data);
      // alert(data.node.id)
    },

    editEvent(event, data) {
      // debugger;
      this.callback = data.callback;
      this.origConfig = data.params.origConfig;
      this.mode = data.params.mode;

      // Create new vue instance to render under li of edited node
      // see https://css-tricks.com/creating-vue-js-component-instances-programmatically/ for explanation

      const MainDialogComponent = Vue.extend(MainDialog);
      const props = {
        type: data.params.node.type,
        value: data.params.config,
        mode: data.params.mode,
      };
      const self = this;
      const inst = new MainDialogComponent({
        propsData: props,
        store: this.$store,
        router: this.$router,
        created() {
          const EVENTS = [
            { name: 'menuadd', callback: () => console.log('menuadd') },
            { name: 'menuedit', callback: () => console.log('menuedit') },
            {
              name: 'menudata',
              callback: callbackData => {
                self.processUpdate('menudata', callbackData);
              },
            },
          ];
          for (const e of EVENTS) {
            this.$on(e.name, e.callback);
          }
        },
      });
      inst.$mount();
      data.el.appendChild(inst.$el);

      // console.log('got event '+JSON.stringify(data.params, null, 3));
      // data.callback(data.id, 'calling you');
    },
    saveEvent(event, config) {
      // alert(JSON.stringify(config, null, 2))
      // debugger
      this.$store.dispatch('modules/updatemenucode', {
        data: config.data,
        moduleId: this.$route.params.moduleId,
      });
    },
  },
};
</script>

<style src="@/components/UssdMenuEditor/style.css"></style>

<style>
.jstree-default .jstree-icon:empty {
  width: 0px;
  height: 22px;
}
.vakata-context li > a > i:empty {
  width: 0px;
  height: 0px;
}

.treecanvas {
  min-width: 100vh;
  min-height: calc(100vh - 100px);
}
</style>
