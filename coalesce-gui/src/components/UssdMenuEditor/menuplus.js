/* eslint-disable */
/* Start of menuplus closure variables */
	var name = 0, attr = 1, content = 2, children = 3;
	var pluginPath = "content/plugin/org.concurrent.menuplus/";
	var appHelp   = {      
				hux : "This node sends a HUX request on to another service application using a specific or the received USSD shortcode.",
				catchall: "This node causes any lower down node that is unmatched to be passed along to a HUX service.",
				goto: "This node goes back to the previous menu, or back to the root of the menu, or to a specific named menu or label."};

    
	// Closure variable used to quickly find existing nodes
	// and map between jstree nodes and config nodes
	var path = '/images/';
	var copyBuffer = {
		type: 'confignode',
		data: null
	};

	var menuNumberMap = new Map(); // the key is a menu name, the value is the current item number.
							 // Used where no menu number is provided by the person editing.

	/* End of menuplus closure variables */
	
	
		/* Start of closure used to manage node ID's */

		var nodeLookup = new Map();
		var nextNodeId = 1;
		var useUUID = true; // Setting this to false will use a numeric ID instead.
		// 0 is reserved for the root tree

		// See https://stackoverflow.com/questions/105034/create-guid-uuid-in-javascript for discussion around this.
		/*
			this is a slightly cobbled function based on that discussion.
			This version was chosen to avoid the duplicate random issues experienced in some version of chrome.
			To be on the safe side, I also check for duplicates in the getLookupKey function.
		*/
		function generateUUID() { // Public Domain/MIT
			var d = new Date().getTime();
			if (typeof performance !== 'undefined' && typeof performance.now === 'function'){
				d += performance.now(); //use high-precision timer if available
			}
			return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
				var r = (d + Math.random() * 16) % 16 | 0;
				d = Math.floor(d / 16);
				return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
			});
		}

		function forEachTreeNode(cb) {
			nodeLookup.forEach(cb);
		};

		function clearNodeLookup() {
			nodeLookup.clear(); // Cleardown the lookup table
			nextNodeId = 1;
		};

		function getLookupKey() {
			var key = null;
			if (useUUID) {
				var uuid = generateUUID();
				while (nodeLookup.has('node_'+uuid)) {
					uuid = generateUUID();
					console.log('Duplicate UUID found '+uuid);
				}
				key = uuid;
			}
			else {
				key = nextNodeId++;
			}
			return 'node_'+key;
		};

		function addToLookupTable(treeNode, confignode, preserve) {
			var nodeid = null;
			if (typeof confignode[1].nodeid !== 'undefined' && confignode[1].nodeid != null) {
				nodeid = confignode[1].nodeid;
				if (nodeLookup.has(nodeid) && !preserve) {
					// Unlikely to ever happen, but if we get a clash, dump the ID and get a new one.
					// doesn't harm to be paranoid :)
					nodeid = null;
				}
			}

			// <10 is to detect old node ID's that got saved in a
			// config file and just replace them.  
			if (nodeid == null || nodeid.length < 10) {
				nodeid = getLookupKey();
			}
			if (!preserve) treeNode.id = nodeid;
			confignode[1].nodeid = nodeid;
			nodeLookup.set(nodeid, {
				treenode: treeNode,
				confignode: confignode
			});
			return nodeid;
		};

		function getMenuPlusNode(node_id) {
			//debugger
			let result = null;
			if (node_id === '#') {
				for (var [key, value] of nodeLookup) {
					if (value.treenode.type === 'service') {
						result = value;
						break;
					}
				}
			}
			else {
				let parts = node_id.split('_'); // needed in case we get the id from an 'a' tag
				if (parts.length > 1) {
					result = nodeLookup.get(parts[0]+'_'+parts[1]);
				}
			}
			return result;
		};

		function getMenuPlusNodeId(parentNode) {
			//debugger
			let node = getMenuPlusNode(parentNode.id)
			return node.treenode.id
		}

		function getMenuPlusNodeChild(node_id, offset) {
			var result = null;
			var parts = node_id.split('_'); // needed in case we get the id from an 'a' tag
			if (parts.length > 1) {
				var node = nodeLookup.get(parts[0]+'_'+parts[1]);
				if (node.confignode.length >= 4) {
					var children = node.confignode[3];
					if (children.length > offset) {
						result = children[offset];
					}
				}
			}
			
			return result;
		};

		function getMenuPlusNodeParent(node) {
			var result = null;
			var parent_id = node[1].parentid;
			if (typeof parent_id !== 'undefined' && parent_id !== null) {
				result = getMenuPlusNode(parent_id)
			}

			return result;
		};

		/* End of closure used to manage node ID's */
	

	function logJson(json) {
		console.log(JSON.stringify(json, null, 2));
	};

	window.MenuPlus = {
		curLang: null,
		langs: null,
		imgdir: '/',
		$container: null,

		convertLineFeeds : function(i)
		{
			i = MenuPlus.escapeText(i);
			i = MenuPlus.convertVariables(i);
			
			var crImg = [];
			crImg.push('<img src="');
			crImg.push('/');
			crImg.push('images/crs.png" style="position:relative;top:');
			crImg.push((document.all!= null)?"0":"5");
			crImg.push('px;margin-left:4px;margin-right:4px" align=bottom border=0/>');

			return i.replace(/\n/g, crImg.join('')).replace(/\\n/g, crImg.join(''));
		},

		escapeText : function(str) {
			//if (typeof str === undefined) debugger;
			try {
				return str.replace(/&/g, "&amp;")
				.replace(/</g, "&lt;")
				.replace(/>/g, "&gt;")
				.replace(/"/g, "&quot;")
				.replace(/\\"/g, "\\")
				//.replace(/\n/g, "<br/>")
				.replace(/'/g, "&apos;");
			}
			catch(err) {
				console.log(err);
			}
		},

		findVariableNameById : function(id, varList) {
			if (typeof varList === 'undefined' || varList == null) varList = MenuPlus.listVariables(MP_config.root);
			var varname = id;
	
			if (Array.isArray(varList)) {
				for (var z=0; z<varList.length; z++) {
					if (varList[z].id === id) {
						varname = varList[z].name;
						break;
					}
				}
			}
			
			return varname;
		},

		findIdByVariableName : function(name, varList) {
			if (typeof varList === 'undefined' || varList == null) varList = MenuPlus.listVariables(MP_config.root);
			var id = name;
	
			for (var z=0; z<varList.length; z++) {
				if (varList[z].name === name) {
					id = varList[z].id;
					break;
				}
			}
			return id;
		},

		formatForDisplayInvalid : function(name, classname) {
			var sb = [];
			sb.push('<span title="Undefined variable" class="disp_var_invalid ');
			if (typeof classname !== 'undefined' && classname !== null) {
				sb.push(classname);
			}
			sb.push('" ');

			var offsets = '';
			if (offsets.length > 0) {
				sb.push('" style="');
				sb.push(offsets);
				sb.push('"');
			}
			sb.push('>');
			sb.push(name);
			sb.push('</span>');
			return sb.join('');
		},
	
		formatForDisplay : function(name, classname) {
			var sb = [];
			sb.push('<span class="disp_var ');
			if (typeof classname !== 'undefined' && classname !== null) {
				sb.push(classname);
			}
			sb.push('" ');

			var offsets = '';
			if (offsets.length > 0) {
				sb.push('" style="');
				sb.push(offsets);
				sb.push('"');
			}
			sb.push('>');
			sb.push(name);
			sb.push('</span>');
			return sb.join('');
		},

		getConstantForDisplay : function(id, classname) {
			var sb = [];
			sb.push('<span class="');
			if (typeof classname !== 'undefined' && classname !== null) {
				sb.push(classname);
			}
			else {
				sb.push('disp_const');
			}
			sb.push('">');
			sb.push(id);
			sb.push('</span>');
			return sb.join('');
		},

		getVariableForDisplay : function(id, classname) {
			var varList = MenuPlus.listVariables(MP_config.root);
			var varname = id;
			var found = false;
			var mpvarlist = MP_config.mpvarlist;
	
			if (varList) {
				for (var z=0; z<varList.length; z++) {
					if (varList[z].id === id) {
						varname = varList[z].name;
						found = true;
						break;
					}
				}
			}
			
			if (mpvarlist) {
				if (typeof mpvarlist !== 'undefined' && mpvarlist !== null) {
					for (var y=0; y<mpvarlist.length; y++) {
						if (mpvarlist[y].name === id) {
							varname = mpvarlist[y].name;
							found = true;
							break;
						}
					}
				}
			}
			
			varname = MenuPlus.formatForDisplay(varname, classname);
			return varname;
		},

		convertVariablesForEdit: function(text, variableList) {
			var result,regex = new RegExp(/%([\(\)\^&#*\"$\w:0-9-_\.]+)%/g);
			var replacements = {};
			if (typeof variableList === 'undefined' || variableList == null) variableList = MenuPlus.listVariables(MP_config.root);
			
			while(result = regex.exec(text)) {
				replacements[result[1]] = MenuPlus.findVariableNameById(result[1], variableList);
			}
			for (var placeholder in replacements) {
				//text = text.split(placeholder).join(replacements[placeholder]);
				text = text.split('%'+placeholder+'%').join('%'+replacements[placeholder]+'%');
			}
			//console.log(text);
			return text;
		},

		convertVariables: function(text, forDisplay, variableList) {
			var result,regex = new RegExp(/%([\(\)\^&#*\"$\w:0-9-_\.]+)%/g);
			var replacements = {};
			if (typeof forDisplay === 'undefined' || forDisplay == null) forDisplay = true;
			if (typeof variableList === 'undefined' || variableList == null) variableList = MenuPlus.listVariables(MP_config.root);
			
			while(result = regex.exec(text)) {
				if (forDisplay) {
					replacements[result[1]] = MenuPlus.findVariableNameById(result[1], variableList);
				}
				else {
					replacements[result[1]] = MenuPlus.findIdByVariableName(result[1], variableList);
				}
			}
			for (var placeholder in replacements) {
				if (forDisplay) {
					
					text = text.split('%'+placeholder+'%').join(MenuPlus.getVariableForDisplay(placeholder));
				}
				else {
					text = text.split('%'+placeholder+'%').join('%'+replacements[placeholder]+'%');
				}
			}
			//console.log(text);
			return text;
		},

        getType : function(name) {
            return treeTypeConfig.getTypeByName(name);
        },
		getNodeText : function(cfg) {
			var imgdir = path;
			var name = 0, attr = 1, content = 2;
			var attributes = cfg[attr], content = cfg[content];
			var nodeText = [];
			//console.log(cfg[name])
			switch (cfg[name]) {
				case "service":
					var i = attributes.name, langs = MenuPlus.langs;
					// initialize edit language
					var editLanguage = attributes["editLanguage"] || "ENG";
					if (MenuPlus.curLang == null)
						MenuPlus.curLang = editLanguage;

					nodeText.push('<span class="mp_desc pe_srvs" title="Service \'');
					nodeText.push(i);
					nodeText.push('\'"></span>&nbsp;');
					nodeText.push(i);
				break;

				case "goto":
					//debugger
					nodeText.push('<span class="mp_desc pe_goto_hdr" title="Goto - click to edit">&nbsp;Goto ');

					nodeText.push('<img src="');
					nodeText.push(imgdir+'property.png"');
					nodeText.push(' border=0 style="cursor:pointer;opacity:0.5" title="Edit properties" />');

					nodeText.push('<span style="color:#505050;position:relative;left:10px;font-weight:normal">');

					switch(attributes.location || 'previous') {
						case 0:
							nodeText.push("End");
						break;
						case 'previous':
							nodeText.push("Previous");
						break;
						case 2:
							nodeText.push("Root");
						break;
						case 'label':
							nodeText.push('<span class=pe_menu>' +attributes.target+ '&nbsp;&nbsp;</span>');
						break;
						default:
							nodeText.push('&lt;<i style="color:red">none</i>&gt;');
						break
					}

					nodeText.push('</span>');

					nodeText.push('</span>');

					//nodeText.push('<img src="');
					//nodeText.push(imgdir+'information-white.png"');
					//nodeText.push(' border=0 style="position:relative;left:40px;cursor:pointer;opacity:0.25" title="');
					//nodeText.push(appHelp["goto"]);
					//nodeText.push('"/>');
				break;

				case "parameters":
					nodeText.push('<span class="mp_desc pe_param" title="Parameters">Parameters</span>');
				break;

				case "call": 
					nodeText.push('<span class="mp_desc pe_call" title="Call Server \'');
					nodeText.push(attributes.type);
					nodeText.push('\'">');
					nodeText.push(i);
					nodeText.push('</span>');
				break;

				case "component":
					//debugger;
					nodeText.push('<span class="mp_desc pe_call" title="Call Component - click to edit"">Call Component ');
					nodeText.push('<span class="pe_component_title">'+cfg[1].name+'</span>');

					nodeText.push('<img ');
					nodeText.push('src="'+imgdir+'property.png"');
					nodeText.push(' border=0 style="cursor:pointer;opacity:0.5" title="Edit properties" />');

					nodeText.push('<span style="color:#505050;margin-left:10px;">');
					
					//nodeText.push(MenuPlus.getHUXInfo(cfg));
					nodeText.push('</span>');
					nodeText.push('</span>');

                break;
                
				case "menuModule":
					//debugger;
					nodeText.push('<span class="mp_desc pe_call" title="Call Module - click to edit"">Call Module ');
					nodeText.push('<span class="pe_module_title">'+cfg[1].name+'</span>');

					nodeText.push('<img ');
					nodeText.push('src="'+imgdir+'property.png"');
					nodeText.push(' border=0 style="cursor:pointer;opacity:0.5" title="Edit properties" />');

					nodeText.push('<span style="color:#505050;margin-left:10px;">');
					
					//nodeText.push(MenuPlus.getHUXInfo(cfg));
					nodeText.push('</span>');
					nodeText.push('</span>');

				break;

				case "hux":
					nodeText.push('<span class="mp_desc pe_call" title="Call a HUX service - click to edit"">HUX Call ');

					nodeText.push('<img ');
					nodeText.push('src="'+imgdir+'property.png"');
					nodeText.push(' border=0 style="cursor:pointer;opacity:0.5" title="Edit properties" />');

					nodeText.push('<span style="color:#505050;margin-left:10px;">');
					
					nodeText.push(MenuPlus.getHUXInfo(cfg));
					nodeText.push('</span>');
					nodeText.push('</span>');

					//nodeText.push('<img src="');
					//nodeText.push(imgdir+'information-white.png');
					//nodeText.push('" border=0 style="margin-left:10px;cursor:pointer;opacity:0.25" title="');
					//nodeText.push(appHelp["hux"]);
					//nodeText.push('"/>');
				break;

				case "menu":
					//debugger
					var menuName = attributes.name|| attributes.code;
					var pname = MenuPlus.curLang ? MenuPlus.curLang + "_text" : "text";	
					var menuText = attributes[pname] || "Menu";
					//var menuText = menuName?menuName:"Menu";
					//menuNumberMap.set(menuName, 1);

					if (!attributes.cnt) attributes.cnt = "000"

					nodeText.push('<span class="mp_desc pe_menu" title="');
					nodeText.push(menuText);
					nodeText.push('" with USSD request %path$#">');
					nodeText.push(MenuPlus.convertLineFeeds(menuText));
					nodeText.push('&nbsp;</span>');

				break;

				case "item":
					//debugger
					var parentNode = getMenuPlusNodeParent(cfg);
					var menuName = parentNode.confignode[1].name|| parentNode.confignode[1].code;
					var itemNumber = menuNumberMap.get(menuName);
					/*if (typeof attributes.selector === "undefined" || attributes.selector == null)
					{
						attributes.selector = (itemNumber++) + "";
					}
					menuNumberMap.set(menuName, itemNumber);*/

					var pname = MenuPlus.curLang ? MenuPlus.curLang + "_text" : "text";	
					var i = attributes[pname] || attributes["text"];
					
					nodeText.push('<span class="mp_desc pe_item" title="Menu item with USSD request %path$');
					nodeText.push(attributes.selector);
					nodeText.push('#"><b class="pe_num">');
					nodeText.push(attributes.selector);
					nodeText.push('</b> ');
					nodeText.push(MenuPlus.convertLineFeeds(i));
					nodeText.push('</span>');

				break;

				case "catchall": 
					nodeText.push('<span class="mp_desc pe_catchall" title="HUX CatchAll - click to edit">HUX Catch All ');

					nodeText.push('<img');
					nodeText.push('src="');
					nodeText.push(imgdir+'property.png');
					nodeText.push('" border=0 style="cursor:pointer;opacity:0.5" title="Edit properties" />');

					nodeText.push('<span style="color:#505050;position:relative;left:10px;font-weight:normal">');
					nodeText.push(MenuPlus.getCatchAllInfo(cfg));
					nodeText.push('</span>');

					nodeText.push('</span>');

					//nodeText.push('<img src="');
					//nodeText.push(imgdir+'information-white.png');
					//nodeText.push('" border=0 style="position:relative;left:40px;cursor:pointer;opacity:0.25" title="');
					//nodeText.push(appHelp["catchall"]);
					//nodeText.push('"/>');

				break;

				case "ifexp":
					var expInfo = MenuPlus.getIfExpressionInfo(cfg);
					nodeText.push('<span title="');
					nodeText.push(expInfo.title);
					nodeText.push('" class="mp_desc pe_expression">&nbsp;IF');
					nodeText.push(expInfo.html);
					nodeText.push('</span>');
				break;

				case "assigned":
					nodeText.push('<span title="This node will be traversed if the specified variable has any value" class="mp_desc pe_assigned">&nbsp;IF ');
					nodeText.push(MenuPlus.getVariableForDisplay(attributes.variable, 'pe_if_a'));
					nodeText.push('</span>');
				break;

				case "if":
					switch (attributes.type) {
						case "assigned":
							nodeText.push('<span title="This node will be traversed if the specified variable has any value" class="mp_desc pe_assigned">&nbsp;IF ');
							nodeText.push(MenuPlus.getVariableForDisplay(attributes.variable, 'pe_if_a'));
							nodeText.push('</span>');
						break;
						case "equals":
							nodeText.push('<span title="This node will be traversed if the specified variable equals the specified constant" class="mp_desc pe_equal">&nbsp;IF ');
							nodeText.push(MenuPlus.getVariableForDisplay(attributes.variable, 'pe_if_a'));
							nodeText.push('&nbsp;equals&nbsp;');
							nodeText.push(MenuPlus.getConstantForDisplay(attributes.constant));
						break;
						default:
							nodeText.push('<span class="mp_desc pe_depends" >&nbsp;');
							nodeText.push(MenuPlus.findVariableNameById(attributes.variable));
							nodeText.push('<span class="pe_assign_eq">&nbsp;being&nbsp;</span>"');
							nodeText.push(attributes.value);
							nodeText.push('"</span>');
						break;
					}
				break;

				case "else":
					nodeText.push('<span class="mp_desc pe_else" title="Performs action if previous IF did not match" /> Else</span>');
				break;

				case "ask":
					var pname = MenuPlus.curLang ? MenuPlus.curLang + "_text" : "text",
						i = attributes[pname] || attributes["text"], 
						s = '<span class="pe_disp">'+MenuPlus.convertLineFeeds(i)+'</span>', 
						isAsk = i.trim().length != 0;


					nodeText.push('<span class="mp_desc pe_ask_hdr">');
					nodeText.push(isAsk?'Ask':'Wait for input');;
					nodeText.push(isAsk ? s: "");
					nodeText.push('<span class="pe_ask_save_hdr">');
					nodeText.push(isAsk?'save reply':'saving it');
					nodeText.push(' as</span>');
					nodeText.push(MenuPlus.getVariableForDisplay(attributes.variable));
					nodeText.push('</span>');
					nodeText.push('');
				break;

				case "ussd":
					if (MenuPlus.duplicateUssdCode(attributes.parentid, attributes.code)) {
						nodeText.push('<span class="mp_desc pe_ussd_duplicate" title="USSD Shortcode \'');
					}
					else {
						nodeText.push('<span class="mp_desc pe_ussd" title="USSD Shortcode \'');
					}
					
					nodeText.push(attributes.code);
					nodeText.push('\'">');
					nodeText.push(attributes.code);
					nodeText.push('</span>');
				break;

				case "label":
					nodeText.push('<span class="mp_desc pe_label_hdr"');
					nodeText.push('title="Label - Use a goto node to jump to this">&nbsp;Label');
					nodeText.push('<span class="pe_label_content">');
					nodeText.push(attributes.name);
					nodeText.push('</span>');
					nodeText.push('</span>');
					nodeText.push('');
					nodeText.push('');
				break;

				case "end":
					nodeText.push('<span class="mp_desc pe_end" title="Ends the user menu session" /> End Session</span>');
				break;

				case "display":
					var pname = MenuPlus.curLang ? MenuPlus.curLang + "_text" : "text", text = attributes[pname] || attributes["text"];

					if (typeof text === 'string' && text.length > 40) {
						text = text.substr(0, 50)+' ...'
					}

					nodeText.push('<span class="mp_desc pe_disp_hdr" title="Display a message to the user">&nbsp;Display<span class="pe_disp">');
					nodeText.push(MenuPlus.convertLineFeeds(text));
					nodeText.push('</span></span>');
				break;

				case 'dynamic-menu':
					nodeText.push('<span class="mp_desc pe_disp_hdr" title="Dynamic Menu for Offers">&nbsp;Dynamic Menu Offers<span class="">');

					const { areOffersForBNumber, subscriberNumber, title, header, footer, back, next, tags, properties } = attributes;

					const bold = t => `<span class="pe_disp">${t}</span>`;
					const format = t => MenuPlus.convertLineFeeds(t);

					const nlSpace = '<br />&nbsp;&emsp;';

					const titleText = title.length > 40 ? title.substring(0, 42) + ' ...' : title;
					const headerText = header.text.length > 40 ? header.text.substring(0, 42) + ' ...' : header.text;
					const footerText = footer.text.length > 40 ? footer.text.substring(0, 42) + ' ...' : footer.text;
					const backText = back.text.length > 40 ? back.text.substring(0, 42) + ' ...' : back.text;
					const nextText = next.text.length > 40 ? next.text.substring(0, 42) + ' ...' : next.text;

					text = '';

					if (areOffersForBNumber) {
					text += nlSpace + bold("Offers for 'B' party:") + format(` ${subscriberNumber}`);
					}
					text += titleText.length != 0 ? nlSpace + bold('Title:') + format(` ${titleText}`) : '';
					text += headerText.length != 0 ? nlSpace + bold('Header:') + format(` ${headerText}`) : '';
					text += nlSpace + ' - DYNAMIC MENU will show here';
					text += footerText.length != 0 ? nlSpace + bold('Footer:') + format(` ${footerText}`) : '';
					text += backText.length != 0 ? nlSpace + bold('Back:') + format(` ${backText}`) : '';
					text += nextText.length != 0 ? nlSpace + bold('Next:') + format(` ${nextText}`) : '';

					nodeText.push(text);
					nodeText.push('</span></span>');
				break;

				case "match":
					nodeText.push('<span class="mp_desc pe_match" title="Match static requests to nodes">Match Request</span>&nbsp;&nbsp;');
					nodeText.push('<span class="pe_disp">');
					nodeText.push(attributes.request);
					nodeText.push('</span>');
					nodeText.push('');
				break;

				case "oneshot":
					nodeText.push('<span class="mp_desc pe_oneshot" title="Matches full requests to nodes">OneShot Match</span>&nbsp;&nbsp;');
					nodeText.push('<span class="pe_disp">');
					nodeText.push(MenuPlus.convertLineFeeds(attributes.match));
					nodeText.push('</span>');
					nodeText.push('');
					nodeText.push('');
				break;

				case "assign":
					nodeText.push('<span class="mp_desc pe_assign" >');
					nodeText.push(MenuPlus.getVariableForDisplay(attributes.variable, 'pe_if_a'));
					nodeText.push('=<span class=disp_const>');
					nodeText.push(attributes.value);
					nodeText.push('</span></span>');
				
				break;

				case "property":
					nodeText.push('<span class="mp_desc pe_property" >');
					nodeText.push(MenuPlus.getVariableForDisplay(attributes.variable));
					nodeText.push('= Value Of Property <span class=disp_property>');
					nodeText.push(attributes.property);
					nodeText.push('</span></span>');
				break;
				case "clear":
					nodeText.push('<span class="mp_desc pe_clear">Clear');
					nodeText.push(MenuPlus.getVariableForDisplay(attributes.variable, 'pe_if_a'));
					nodeText.push('</span>');
				break;

				case "set":
					var currentVar = attributes.variable;
					if (typeof currentVar !== 'undefined' && currentVar !== null && currentVar.indexOf('=') > 0) {
						// var contains an '=' char
						var tmpvar = currentVar.split('=');
						currentVar = tmpvar[0];
					}

					nodeText.push('<span class="mp_desc pe_set">Set');
					nodeText.push(MenuPlus.getVariableForDisplay(attributes.variable, 'pe_if_a'));
					nodeText.push('</span>');
				break;

				case "itemselection":
					/*{
						// count the number of children and make the index that
						//if (!parent[attr]["index"])
						//	parent[attr].index = '1';
						var len = parent[chil].length, i=0, inum = 0;
						for (i=0; i<len; i++)
						{
							if (parent[chil][i] == n) inum = i+1;
						}
						//var inum = n[attr].index
						return {ic:23, text:'<span class="pe_itemsel" >&nbsp;#'+inum+'</span>'};
					}*/
					// Need to obtain parent from config
					nodeText.push('TODO, Tell me if you ever see this');
				break;

				case "variableitems":
					nodeText.push('<span class="mp_desc pe_vitems" >&nbsp;Variable Items from ');
					nodeText.push(MenuPlus.getVariableForDisplay(attributes.variable));
					nodeText.push('</span>');
					nodeText.push('');
				break;

				case "process":
					nodeText.push('<span class="mp_desc pe_proc" title="Process"> Process</span>');
				break;

				case "variables":
					nodeText.push('<span class="mp_desc pe_vars">&nbsp;Variables</span>');
				break;

				case "mpvarlist":// maybe not needed
					nodeText.push('<span class="mp_desc pe_vars">&nbsp;Variables</span>');
				break;

				case "var":// maybe not needed
					nodeText.push('<span class="mp_desc pe_var">');
					nodeText.push(attributes.name);
					nodeText.push(' : <b/>');
					nodeText.push('attributes.description');
					nodeText.push('</b>');
					nodeText.push('</span>');
				break;

				case "messages":// maybe not needed
					nodeText.push('<span class="mp_desc pe_msgs">&nbsp;Messages</span>');
				break;

				case "message":// maybe not needed
					nodeText.push('<span class="mp_desc pe_msg">');
					nodeText.push(attributes.id);
					nodeText.push(' : <b/>');
					nodeText.push(attributes.description);
					nodeText.push('</b>');
					nodeText.push('</span>');
				break;

				case "sms":// maybe not needed
					nodeText.push('<span class="mp_desc pe_sms">');
					nodeText.push(attributes.text);
					nodeText.push('</span>');
				break;

				default:
					nodeText.push(attributes.text);
				break;
			}
			return nodeText.join('');
		},

		duplicateUssdCode: function(parentid, code) {
			var parentNode = getMenuPlusNode(parentid);

			var count = 0;
			for (var us=0; us<parentNode.confignode[3].length; us++) {
				var currentChild = parentNode.confignode[3][us];
				if (currentChild[1].code === code) {
					count++;
				}
			}
			return (count > 1);
		},

		hasValue: function(name) {
			return (typeof name !== 'undefined' && name !== null && name.length > 0);
		},

		getExpressionTitle : function(operator) {
			var title = '';
			switch (operator) {
				case "eq" :
				title = 'This node will be traversed if the specified variable equals the specified constant';
				break;
				case "gt" :
				title = 'This node will be traversed when the previous if condition fails to match';
				break;
				case "gte" :
				title = 'This node will be traversed when the previous if condition fails to match';
				break;
				case "lt" :
				title = 'This node will be traversed when the previous if condition fails to match';
				break;
				case "lte" :
				title = 'This node will be traversed when the previous if condition fails to match';
				break;
				case "ne" :
				title = 'This node will be traversed when the previous if condition fails to match';
				break;
				default:
				// TODO error handling
				break;
			}
			return title;
		},
	
		getExpressionHtml : function(hop) {
			var html = [], className = 'pe_if_'+hop.operator;
			html.push('<span class="disp_var pe_if_a">');
			html.push(MenuPlus.findVariableNameById(hop.variable));
			html.push('</span>');
	
			html.push('&nbsp;');
			switch (hop.operator) {
				case "eq" :
				html.push('==');
				break;
				case "gt" :
				html.push('&gt;');
				break;
				case "gte" :
				html.push('&gt;=');
				break;
				case "lt" :
				html.push('&lt;');
				break;
				case "lte" :
				html.push('&lt;=');
				break;
				case "ne" :
				html.push('!=');
				break;
				default:
				// TODO error handling
				break;
			}
			html.push('&nbsp;');
	
			html.push('<span class="disp_const ');
			html.push(className);
			html.push('">');
			html.push(hop.value);
			html.push('</span>');
			return html.join('');
		},
	
		getIfExpressionInfo : function(node)
		{
			var hop = node[attr];
	
			return {
				title: MenuPlus.getExpressionTitle(hop.operator),
				html: MenuPlus.getExpressionHtml(hop)
			};
		},

		getCatchAllInfo : function(n)
		{
			var hop = n[attr], hv = hop.variable, hc = hop.code;
			if (hv == null || hv.length == 0 || typeof hv == 'undefined') hv = "";
			var sb = [];

			sb.push("http://");
			sb.push(hop.address);
			sb.push(":");
			sb.push(hop.port);
			sb.push(hop.url);
			sb.push("&nbsp;&rArr;&nbsp;");
			if (typeof hc === 'undefined' || hc === null || hc.length === 0) {
				sb.push("[sent code]");
			}
			else {
				sb.push(hc);
			}
			sb.push(MenuPlus.convertVariables(hop.request));
			if (typeof hv === 'undefined' || hv === null || hv.length === 0) {
				sb.push("");
			}
			else {
				sb.push("&nbsp;&rarr;<span class=disp_var>");
				sb.push(MenuPlus.findVariableNameById(hv));
				sb.push("</span>");
			}
			return sb.join(' ');
		},

		getHUXInfo : function(config)
		{
			var ht =[];
			var attributes = config[attr];
			if (MenuPlus.hasValue(attributes.server) && MenuPlus.hasValue(attributes.displayname)) {
				/*var parts = attributes.server.split('-');
				parts = parts.splice(1);
				ht.push(parts.join('-'));*/
				ht.push(attributes.displayname);
			}
			else {
				ht.push("http://");
				if (MenuPlus.hasValue(attributes.address))ht.push(attributes.address);
				ht.push(":");
				if (MenuPlus.hasValue(attributes.port))ht.push(attributes.port);
				if (MenuPlus.hasValue(attributes.url))ht.push(attributes.url);
			}
			
			ht.push("&nbsp;&rArr;&nbsp;");
			if (MenuPlus.hasValue(attributes.code)) {
				ht.push(attributes.code);
			}
			else {
				ht.push("[sent code]");
			}
			if (MenuPlus.hasValue(attributes.request))ht.push(MenuPlus.convertVariables(attributes.request));

			if (MenuPlus.hasValue(attributes.variable)) {
				ht.push("&nbsp;&rarr;<span class=disp_var>");
				if (MenuPlus.hasValue(attributes.variable))ht.push(MenuPlus.findVariableNameById(attributes.variable));
				ht.push("</span>");
			}
			else {
				ht.push("");
			}

			return ht.join(' ');
		},
		
		jstreeTypes: function() {
			var path = this.imgdir;
			return treeTypeConfig;
		},

		omitKeys: function(obj) {
			var keys = [];//['children', 'parent', 'children_d'];
			var dup = {};
			for (var key in obj) {
				if (keys.indexOf(key) == -1) {
					dup[key] = obj[key];
				}
			}
			return dup;
        },

		/*
			verifyConfig just des some basic sanity checks on a node
			to ensure that the configuration has not been corrupted.
			This is a developer thing to help spot issues before they 
			get into production.
		*/
		verifyConfig: function(config, node_id) {
			var result = true;
			if (config.treenode.id !== node_id) {
				console.log('node_id mismatch');
				result = false;
			}
			return result;
		},

		editableType: function(type) {
			return treeTypeConfig[type].editable;
		},

        shouldEdit: function(data) {
            var result = false;
            if (typeof data.event === 'object') {
                result = true;
            }
            else {
                // Other conditions that can cause an edit, currently none.
            }

            return result;
		},

		jstreeId: function(id) {
			if (typeof id !== 'string') {
				return 'node_+id';
			}
			return id;
		},

		loadform: function(targetId, mode, originalConfig, cb) {
			//debugger
			var node = originalConfig.treenode, config = originalConfig.confignode[1];
			var url = 'configform/'+node.type+'/'+mode;
			//var url = 'configform/default';
			var modeText = '';
			var title = '';
      var params;
			var validationRules = null;
			var typeConfig = treeTypeConfig.getTypeByName(node.type);
			var typeName = node.type.charAt(0).toUpperCase() + node.type.slice(1);

			switch(mode) {
				case 'add':
					modeText = 'Adding';
					title = 'Adding '+typeName+' Configuration';
				break;
				case 'edit':
					modeText = 'Editing';
					title = 'Editing '+typeName+' Configuration';
				break;
				default:
					console.log('loadform:: Got unknown mode '+mode+'. ignoring');
				break;
			}
            if (typeof typeConfig.params !== 'undefined' && typeConfig.params !== null) {
				params = {};
				for (var item in typeConfig.params) { // copy attributes
					params[item] = typeConfig.params[item];
				}
				params.type = node.type;
                if (typeof params.rules !== 'undefined' && params.rules !== null) {
                    validationRules = params.rules;
				}
				params.title = params.title.replace('%MODE%', modeText);
            }
            else {
				typeName = node.type.charAt(0).toUpperCase() + node.type.slice(1);
                
                params = {
                    title: modeText+'Editing '+typeName+' Configuration',
					help: 'Configuration for '+typeName
				};
						}
						//debugger
						
						let $targetElement = targetId;
						let child = $('<div id="coalesce-edit-dialog"></div>');
						if (targetId !== '#') {
							if (typeof targetId === 'string') {
								$targetElement = $('#'+targetId);
							}
							if ($targetElement.length > 0) {
								$targetElement.html(child);
							}
							else {
								targetId = '#';
							}
						}

						if (targetId === '#') {
							$targetElement = $('#'+node.id);
							let newtargetId = node.id+'_cont';
							let container = $('<li id="coalesce-edit-dialog-parent" class="jstree-node  jstree-leaf" id="'+newtargetId+'"></li>');
							container.append(child);
							$targetElement.after(container);
							targetId = newtargetId;
						}

						// Call through to vue.js for editing.
						//debugger
						$('body').append('<div class="menuplus-backdrop fade in"></div>');
						$targetElement.trigger('inlineedit.coalesce', {
							target: child,
							id: targetId,
							//el: '#coalesce-edit-dialog',
							el: child[0],
							params: {
								type: params,
								origConfig: originalConfig,
								node: node,
								config: config,
								mode: mode
							},
							callback: cb
						})
			/*var jqxhr = $.post(url, params, function(resp) {
				var $targetElement = targetId;
				if (typeof targetId === 'string') {
					$targetElement = $('#'+targetId);
				}
				$('#menuplus-dialog').remove();
				$('.menuplus-backdrop').remove();
				var $items = $targetElement.find('ul:first');
				if ($items.length > 0) {
					$items.first().before(resp);
					//console.log('parent');
					$('#menuplus-dialog').addClass('parent');
				}
				else {
					$targetElement.after(resp);
					//console.log('child');
					$('#menuplus-dialog').addClass('child');
				}

                var $dialog = $('#menuplus-dialog');
                var form = $dialog.find('form');
                form.menuplusdeserialize(config, true);
			
				$('body').append('<div class="menuplus-backdrop fade in"></div>');

				// TODO fix this
				//$dialog[0].scrollIntoView(false);
                
                var $updateButton = $dialog.find('#submitButton');
                var $headerCloseButton = $dialog.find('#headerClose');
                var $footerCloseButton = $dialog.find('#footerClose');
				var $backdrop = $('.menuplus-backdrop');
				var $dialogBody = $dialog.find('.menuplus-body');

				if (typeof cb === 'function') {
					cb('display', $dialog, originalConfig, validationRules);
				}

                var closeAction = function(ev) {
                    $updateButton.off();
                    $dialog.off();
                    $backdrop.off();
                    $headerCloseButton.off();
					$footerCloseButton.off();
                    //MenuPlus.updateJsTree();
                    $backdrop.remove();
					$dialog.remove();
					//TODO remove old node from node list
                };

				$backdrop.on('click', closeAction);
                
                $updateButton.on('click', function() {
                    if (typeof cb === 'function') {
                        var formData = form.menuplusserialize(config, false);
                        if (cb('validate', $dialog, config, formData, config, mode)) {
							if (cb('update', $dialog, config, formData, config, mode)) {
								closeAction();
								MenuPlus.refreshJstreeNode(config);
							}
                        }
                    }
                    else closeAction();
				});

				$dialogBody.find('input[type=text]').filter(':visible:first').focus();
				
				$dialogBody.on('keypress', function(event) {
					var key = event.which || event.charCode || event.keyCode;

					switch(key) {
						case 13 : // enter
						if (typeof cb === 'function') {
							event.preventDefault();
							var formData = form.menuplusserialize(config, false);
							if (cb('validate', $dialog, config, formData, config, mode)) {
								if (cb('update', $dialog, config, formData, config, mode)) {
									closeAction();
									MenuPlus.refreshJstreeNode(config);
								}
							}
							return false;
						}
						else closeAction();
						break;

						case 8  : // backspace
            			case 37 : // left
						case 39 : // right
						case 38 : // up
						case 40 : // down
						case 27 : // esc
						break;
						default:
							//console.log(key);
						break;
					}
				});

                $footerCloseButton.on('click', function() {
                    if (typeof cb === 'function') {
                        cb('dismiss', $dialog, node);
                    }
                    closeAction();
                });

                $headerCloseButton.on('click', function() {
                    if (typeof cb === 'function') {
                        cb('dismiss', $dialog, node);
                    }
                    closeAction();
				});
				
				$('#menuplus-dialog').draggable({
					handle: ".menuplus-content"
				});
			})
			.fail(function(err) {
				bootbox.alert({
					title: "Load Form",
					message: "Unable to load form for type ||"+node.type+"||"
				});
			})
			.always(function() {
			//alert( "finished" );
			});*/
		},

		editNewNode: function(parentNode, newTreeNode, mode) {
			// debugger
			var existing = $('.menuplus-backdrop');
			if (existing.length <= 0) {
				var originalConfig = getMenuPlusNode(newTreeNode.id);
				if (MenuPlus.editableType(originalConfig.confignode[0])) {
					if (newTreeNode.id !== '#') {
						let $targetElement = $('#'+newTreeNode.id);
						if ($targetElement.length > 0) {
							$targetElement.find('.jstree-wholerow').remove();
						}
					}
					MenuPlus.loadform(newTreeNode.id, mode, originalConfig, MenuPlus.editFormCallback);
				}
			}
			else {
				console.log('Only one node at a time can be edited');
			}
		},
		
		editNode: function(node_id, mode) {
			// debugger
			MenuPlus.saved_root_config = [];
			MenuPlus.copyConfig(MP_config.root, MenuPlus.saved_root_config);
			var existing = $('.menuplus-backdrop');
			if (existing.length <= 0) {
				var originalConfig = getMenuPlusNode(node_id);
				if (MenuPlus.editableType(originalConfig.confignode[0])) {
					var $targetElement = $('#'+originalConfig.treenode.id);
					var targetId = originalConfig.treenode.id;
					if ($targetElement.length < 1) {
						// Node not in tree yet, so use parent node instead.
						$targetElement = $('#'+originalConfig.treenode.parentid);
						targetId = originalConfig.treenode.parentid;
					}
					if ($targetElement.length > 0) {
						$targetElement.find('.jstree-wholerow').remove();
	
						//loadform: function($target, node, config, cb)
						MenuPlus.loadform(targetId, mode, originalConfig, MenuPlus.editFormCallback);
					}
				}
			}
			else {
				console.log('Only one node at a time can be edited');
			}
		},

		handleSelectEvent : function(ev, data) {
			if (MenuPlus.shouldEdit(data)) {
				var originalConfig = getMenuPlusNode(data.node.id);
				try {
					if (MenuPlus.verifyConfig(originalConfig, data.node.id)) {
						if (MenuPlus.editableType(data.node.type)) {
							MenuPlus.editNode(data.node.id, 'edit');
						}
						else {
							console.log("type is not editable := "+data.node.type);
						}
					}
					else {
						var txt = JSON.stringify(originalConfig, null, 2);
						console.log (txt);
						alert("Config mismatch during edit"+txt);
					}
				}
				catch(err) {
					var txt = JSON.stringify(originalConfig, null, 2);
					console.log (txt);
					alert("Config mismatch during edit"+txt);
				}
				
			}
			else {
                // initial display, not user event
                //console.log ('missing event');
			}
		},
		
		refreshJstreeNode: function(config) {
			var originalConfig = getMenuPlusNode(config.nodeid);
			var newText = MenuPlus.getNodeText(originalConfig.confignode, path);
			var $jstree = $.jstree.reference(originalConfig.treenode.parentid);
			if ($jstree === null) {
				// Assume we need the root node
				$jstree = $.jstree.reference('#');
			}
			var originalNode = $jstree.get_node(originalConfig.treenode);
			
			$jstree.rename_node(originalNode, newText);
		},
        
    updateJsTree: function(reload) {
			var $jstree = $('#jstree-container').jstree(true);
			if (typeof reload !== 'undefined' && reload !== null && reload) {
				var jstreeConfig = MenuPlus.configForJstree(MP_config.root, '/images/');
				$jstree.settings.core.data = jstreeConfig;
				$jstree.refresh(true);
				//$jstree.open_node(jstreeConfig.id);
				$jstree.deselect_all(true);
			}
			else {
				forEachTreeNode(function(config, key, map) {
					var newText = MenuPlus.getNodeText(config.confignode, path);
					$jstree.rename_node(config.treenode, newText);
					//console.log(`m[${key}] = ${value}`);
				});
			}
			$jstree.open_all($jstree.get_node('#'));
		},
		
		/*
			data - Mixed any data supplied with the call to $.vakata.dnd.start
			element - DOM the DOM element being dragged
			helper - jQuery the helper shown next to the mouse
			event - Object the event that caused the stop
		*/
		DragAndDropScroll : function(event, context) {

		},

		DragAndDropStart : function(event, context) {

		},

		DragAndDropMove : function(event, context) {
			//console.log('DragAndDropMove');
		},

		DragAndDropStop : function(event, context) {

		},

		createNode : function(name) {
			var newNode = [];
			newNode.push(name);
			newNode.push({
				treenode: null,
				confignode: {
				}
			});
			newNode.push("");
			return newNode;
		},
		
		duplicateNode: function(node) {
			var from = (typeof node.confignode !== 'undefined')?node.confignode:node;
			var newNode = MenuPlus.createNode(from[0]); // copy name
			newNode[2] = from[2]; // copy content
			if (typeof from[1] !== 'undefined' && from[1] !== null) {
				newNode[1] = {};
				for (var item in from[1]) { // copy attributes
					newNode[1][item] = from[1][item];
				}
			}
			
			// Finally, copy children
			if (typeof from[3] !== 'undefined' && from[3] !== null) {
				newNode[3] = [];
				for (var i=0;i<from[3].length; i++) {
					newNode[3].push(MenuPlus.duplicateNode(from[3][i]));
				}
			}
			return newNode;
		},

		// var name = 0, attr = 1, cntn = 2, chil = 3;
		configForJstree : function(cfg, imgdir) {
			clearNodeLookup();
			var name = 0, attr = 1, content = 2, chil = 3;
			MenuPlus.imgdir = imgdir;
			//debugger
			var svccfg = [];
			if (cfg[0] === "root" | cfg[0] === "service") {
				svccfg = cfg
			}
			else if (typeof cfg.config !== 'undefined') {
				svccfg = cfg.config
			}
			else {
				svccfg = cfg[chil][0]; // Only process service node
			}
			var jstreecfg = MenuPlus.toJstreeConfig(svccfg, 'node_0')

			return jstreecfg;
		},

		toJstreeConfig: function(cfg, parentid, preserve) {// not yet working
			var name = 0, attr = 1, content = 2, chil = 3;
		    var name = cfg[name], children = cfg[chil], content = cfg[content];
			var treeConfig = {};
			
			cfg[attr]['parentid'] = parentid;
		    
		    treeConfig.type = name;
		    treeConfig.parentid = parentid;
		    treeConfig.text = MenuPlus.getNodeText(cfg);
		    
		    addToLookupTable(treeConfig, cfg, (typeof preserve !== 'undefined' && preserve !== null)?preserve:false);
		    
		    if (typeof children !== 'undefined') {
					treeConfig.children = [];
					for (var i=0; i<children.length; i++) {
						treeConfig.children.push(MenuPlus.toJstreeConfig(children[i], cfg[attr].nodeid, preserve));
					}
			}
			return treeConfig;
		},

		/*

			When using drag / drop with the CTRL key pressed to duplicate a subtree
			or for that matter a single node, jstree creates the visual tree for us 
			with random node ID's.  So to make the subtree work with the rest of the 
			code, we need to recurse through the tree and generate new ID's as we go
			then for each ID, add the node to our lookup table and also update the ID's
			on the jstree nodes using an API call.  Once done, refresh the tree and
			everything shoud be working again :)

		*/
		renumberJstreeNodes: function($jstree, treeNode, configNode, parentid) {
			var newKey = getLookupKey();
			var treeConfig = {};
			$jstree.set_id(treeNode, newKey);
			configNode[1].nodeid = newKey;

			treeConfig.id = newKey;
			treeConfig.type = configNode[0];
		    treeConfig.parentid = parentid;
			treeConfig.text = MenuPlus.getNodeText(configNode);
			
			configNode[1]['parentid'] = parentid;
			treeNode.type = configNode[0];

			// Now add the confignode and treenode to our node list

			addToLookupTable(treeConfig, configNode, false);

			/* sanity checks */
			if (typeof treeNode.children !== 'undefined' && 
				treeNode.children !== null && 
				configNode.length > 3 && 
				treeNode.children.length === configNode[3].length) {

				var configChildren = configNode[3];
				treeConfig.children = [];
				for (var ni=0; ni<treeNode.children.length; ni++) {
					var tree = $jstree.get_node(treeNode.children[ni]);
					var config = configChildren[ni];
					treeConfig.children.push(MenuPlus.renumberJstreeNodes($jstree, tree, config, newKey));
				}
			}
			return treeConfig;
		},

		getRealNodeData : function(data) {
			var response = {
				old_parent: data.old_parent,
				parent: data.parent,
				old_position:data.old_position,
				position:data.position
			};
			if (data.old_parent === data.parent && data.old_position >= data.position) {
				// Same parent, so things get funky.
				response.old_position = data.old_position - 1;
				response.position = data.position - 1;
			}

			// NOTE ONLY use this data on our data structures.  
			// The jstree structures will not work
			return response;
		},

		updateTreeConfig: function (obj, callback) {
			//debugger;
			var treeConfig = MenuPlus.configForJstree(MP_config.root, path);
			if (treeConfig.children !== 'undefined' && Array.isArray(treeConfig.children) && treeConfig.children.length > 0) {
				callback.call(this, treeConfig.children);
			}
			else {
				callback.call(this, treeConfig);
			}
		},
		
		initJsTree : function(config, langs, readonly) {
			MenuPlus.langs = langs;

			var imgdir = '/images/';
			MenuPlusContextMenu.imgdir = imgdir;
			var treeConfig = MenuPlus.configForJstree(config, imgdir);

			//logJson(treeConfig);
				
			MenuPlus.$container = $('#jstree-container')
			/*
			    node - Object
				parent - String the parent's ID
				position - Number the position of the node among the parent's children
				old_parent - String the old parent of the node
				old_position - Number the old position of the node
				is_multi - Boolean do the node and new parent belong to different instances
				old_instance - jsTree the instance the node came from
				new_instance - jsTree the instance of the new parent
			*/
			.on('move_node.jstree', function (event, data) {
				var $jstree = data.new_instance;
				var oldParent = getMenuPlusNode(data.old_parent);
				var newParent = getMenuPlusNode(data.parent);
				var movedNode = getMenuPlusNode(data.node.id);
				oldParent.confignode[3].splice(data.old_position, 1);
				if (newParent.confignode.length < 4) newParent.confignode.push([]);
				newParent.confignode[3].splice(data.position, 0, movedNode.confignode);
				// node, parent, position, old_parent, old_position, is_multi, old_instance, new_instance
				//console.log('Moved node');
				$jstree.open_node(newParent);
				$jstree.deselect_all(true);
				$jstree.select_node(newParent.treenode);
				MenuPlus.setChangedState(true);
			})
			.on('copy_node.jstree', function (event, original) {
				/*
					OK, jstree screws up badly with the incoming data,
					so we need to do diferent things depending on the situation.
					If the copy is to the same parent, then the data is correct.
					But for differant parents, the data is faulty.


				*/

				var $jstree = original.new_instance;
				var oldParentNode = $jstree.get_node(original.old_parent);
				var oldTreeNode = getMenuPlusNode(oldParentNode.children[original.old_position]);
				var newTreeNode = $jstree.get_node(original.node.id);
				var copiedNode = MenuPlus.duplicateNode(oldTreeNode);
				var newParent = getMenuPlusNode(original.parent);
				MenuPlus.renumberJstreeNodes($jstree, newTreeNode, copiedNode, newParent.treenode.id);

				$jstree.redraw(true);

				var completedNode = getMenuPlusNode(newTreeNode.id);
				/*
				 At this point, the data structures have been made consistent
				 with the nly discrepency being the anchor node id on the page 
				 has the jstree j number (which I believe is never used).
				 Also, the visual part of the tree is complete, so we just need 
				 to splice the copy into the parent confignode
				 */
				if (newParent.confignode.length < 4) newParent.confignode.push([]);
				newParent.confignode[3].splice(original.position, 0, completedNode.confignode);

				if (newTreeNode.type === 'ussd') MenuPlus.updateJsTree();

				$jstree.open_node(newParent.treenode);
				$jstree.deselect_all(true);
				$jstree.select_node(newParent.treenode);
				MenuPlus.setChangedState(true);
			})
			.on('changed.jstree', function (e, data) {
				//var node = $('.menuplusli');
				var node = $('.menuplusli');
				
					//node.html(html.join(''));
				//console.log(e);
				/*var i, j, r = [];
				for(i = 0, j = data.selected.length; i < j; i++) {
					r.push(data.instance.get_node(data.selected[i]).text);
				}*/
				//$('#event_result').html('Selected: ' + r.join(', '));
			})
			.on('ready.jstree', function (e, data) {
				var node = $('.menuplusli');
				//node.html(html.join(''));
				//console.log(e);
				//$('#event_result').html('Selected: ' + r.join(', '));
			})
			.on("loaded.jstree", function (event, data) {
				$(this).jstree("open_all");
			})
				//.on('contextmenu', '.jstree-anchor', function (e) {
				//	return MenuPlus.menuplusContextMenu(e);
				//  })
			.on("select_node.jstree", MenuPlus.handleSelectEvent)
			//.mpJstree({
			.jstree({
				readonly: readonly,
				context_menu: MenuPlusContextMenu.showContextMenu,
				plugins : ["noclose", "wholerow", "state", "inlinehtml", "types", "contextmenu", "dnd"],
				state : {
					key : "menuplus.edit"
				},

				types : MenuPlus.jstreeTypes(),
				core : {
					data : MenuPlus.updateTreeConfig,
					multiple: false, // Do not allow multiple nodes to be selected.
					//force_text : true,
					check_callback : treeTypeConfig.childAllowed,// Usxed to decide if drop of drag drop or paste is allowed.
					//check_callback : true
					themes: {
						dots: false,
						icons: false
					}
				},
				dnd : {
					is_draggable : function(node) {
						//debugger;
						return treeTypeConfig.canDrag(node[0].type);
					}
				},
				contextmenu: {
					select_node: false, // Do not allow multiple nodes to be selected.
					show_at_node: false, // Make menu appear at mouse co-ordinates rather than node
					// NB arrow function used so that this is set correctly.  Won't
					items: MenuPlusContextMenu.showContextMenu
				}
			});

			$(document)
			.on('dnd_stop.vakata', MenuPlus.DragAndDropScroll)
			.on('dnd_stop.vakata', MenuPlus.DragAndDropStart)
			.on('dnd_stop.vakata', MenuPlus.DragAndDropMove)
			.on('dnd_stop.vakata', MenuPlus.DragAndDropStop);
        },

        /*
            takes a typename and type definition
            then uses the template from the type definition
            to create a new node in the menuplus format.
        */
        createXmlNodeFromType: function(type, typeInfo) {
			var newItem = [];
			var typeName = (typeof typeInfo === 'string')?typeInfo:typeInfo.type;
            newItem.push(typeName); // name of xml element
            var attributes = {};
            if (typeof type.template !== 'undefined' && type.template !== null) {
                for (var attrName in type.template) {
                    attributes[attrName] = type.template[attrName];
                }
            }
            newItem.push(attributes); // end up as element attributes in XML
            newItem.push(""); // xml text content
            //newItem.push([]); // child elements
            return newItem;
        },

		deleteNode : function(ctxt) {
			//debugger
			var $jstree = $.jstree.reference(ctxt.reference);
			var treeNode = $jstree.get_node(ctxt.reference);
			var node = getMenuPlusNode(treeNode.id);
			if (typeof node.confignode[1].parentid !== 'undefined') {
				var parent = getMenuPlusNode(node.confignode[1].parentid);
				for (var i=0; i<parent.confignode[3].length; i++) {
					var child = parent.confignode[3][i];
					if (child[1].nodeid === node.confignode[1].nodeid) {
						parent.confignode[3].splice(i, 1);
						break;
					}
				}
			}
			$jstree.delete_node($jstree.get_node(ctxt.reference));
			MenuPlus.setChangedState(true);

		},
		
    contextClick : function(ctxt) {
      var typeName = (typeof ctxt.item.type === 'object')?ctxt.item.type.type:ctxt.item.type;
			//debugger;
			if (typeName === 'edit') {
				var $jstree = $.jstree.reference(ctxt.reference);
				var treeNode = $jstree.get_node(ctxt.reference);
				var editNode = getMenuPlusNode(treeNode.id);
				MenuPlus.editNode(editNode.confignode[1].nodeid, 'edit');
			}
			else if (typeName === 'delete') {
				MenuPlus.deleteNode(ctxt);
			}
			else {
				var type = MenuPlus.getType(typeName);
				if (typeof type === 'object') {
						MenuPlus.createTreeNode(ctxt, type, ctxt.item.type);
				}
				//console.log(JSON.stringify(type, null, 2));
			}
		},

		removeNode: function(parent, node) {
			for (var i=0; i<parent.confignode[3].length; i++) {
				var child = parent.confignode[3][i];
				if (child[1].nodeid === node.confignode[1].nodeid) {
					parent.confignode[3].splice(i, 1);
					break;
				}
			}
			delete node.treenode;
			return node.confignode;
		},
		
		cutNode : function(data)
    {
			var $jstree = $.jstree.reference(data.reference);
			var cutNode = $jstree.get_node(data.reference);

			//data.reference.css('opacity', '0.50');
			copyBuffer.data = MenuPlus.removeNode(getMenuPlusNode(cutNode.parent), getMenuPlusNode(cutNode.id));
			copyBuffer.originalParentId = cutNode.parent;
			$jstree.delete_node(data.reference);
			MenuPlus.setChangedState(true);
		},
        
    copyNode : function(data)
    {
			var $jstree = $.jstree.reference(data.reference);
			var treeNode = $jstree.get_node(data.reference);
			copyBuffer.data = MenuPlus.duplicateNode(getMenuPlusNode(treeNode.id));
			copyBuffer.originalParentId = -1;
		},

		/* TODO add missing checks.  Also do same for drag copy and drag move :-

		if destination is an if and grandparent is a menu
			check new item for invalid statements
			If contains invalid, don't allow paste
			else allow paste
		else
			allow paste
	
		
		Alternatively, consider making a menuif and menuelse statement
		*/
		pasteNodeDisabled : function(data) {
			var state = true;
			if (typeof copyBuffer.data !== 'undefined' && copyBuffer.data !== null) {
				var $jstree = $.jstree.reference(data.reference);
				var parentNode = $jstree.get_node(data.reference);
				var childNode = $jstree.get_node(copyBuffer.data[1].nodeid);
				if (childNode === false) {
					state = !treeTypeConfig.childTypeAllowedByName(copyBuffer.data[0], parentNode.type);
				}
				else {
					state = !treeTypeConfig.childTypeAllowedByNode(childNode, parentNode);
				}
				if (!state) {
					if (typeof parentNode.children !== 'undefined' && parentNode.children !== null) {
						for (var ch=0; ch<parentNode.children.length; ch++) {
							var currentChild = getMenuPlusNode(parentNode.children[ch]);
							if (currentChild.treenode.type === 'else') {
								state = true;
								break;
							}
						}

					}
				}
			}
			return state;
		},
		
		pasteNode : function(data)
    {
			if (typeof copyBuffer.data !== 'undefined' && copyBuffer.data !== null) {
				var $jstree = $.jstree.reference(data.reference);
				var parentNode = $jstree.get_node(data.reference);
				var pasteConfig = MenuPlus.duplicateNode(copyBuffer.data);

				var positionFirst = true;
				if (pasteConfig[0] === 'else') {
					positionFirst = false;
				}

				var jstreecfg = MenuPlus.toJstreeConfig(pasteConfig, parentNode.id);
				var pasteToNode = getMenuPlusNode(parentNode.id);

				if (pasteToNode.confignode.length < 4) pasteToNode.confignode.push([]);

				if (positionFirst)
					pasteToNode.confignode[3].unshift(pasteConfig);
				else
					pasteToNode.confignode[3].push(pasteConfig);

				//pasteToNode.confignode[3].splice(0, 0, pasteConfig);
				
				$jstree.create_node(parentNode, jstreecfg, (positionFirst)?'first':'last');
				if (pasteConfig[0] === 'ussd') MenuPlus.updateJsTree();
				$jstree.open_node(parentNode);
				$jstree.deselect_all(true);
				$jstree.select_node(jstreecfg);
			}
			else {
				console.log('Nothing to paste');
			}
			MenuPlus.setChangedState(true);
		},

		expandAll: function(data) {
			var $jstree = $.jstree.reference(data.reference);
			var parentNode = $jstree.get_node(data.reference);
			$jstree.open_all(parentNode);
		},

		contractAll: function(data) {
			var $jstree = $.jstree.reference(data.reference);
			var parentNode = $jstree.get_node(data.reference);
			$jstree.close_all(parentNode);
		},

		createTreeNode: function(ctxt, type, typeDetail) {
			//debugger
			var typeName = (typeof typeDetail === 'object')?typeDetail.type:typeDetail;
			var $jstree = $.jstree.reference(ctxt.reference);
			var parentNode = $jstree.get_node(ctxt.reference);
			var itemPosition = 0;
			if (typeof typeDetail === 'object' && typeDetail.mode === 'after') {
				var grandParentNode = $jstree.get_node(parentNode.parent);
				for (var index=0; index<grandParentNode.children.length; index++) {
					if (parentNode.id === grandParentNode.children[index]) {
						itemPosition = index+1;
						break;
					}
				}
				parentNode = grandParentNode;
			}
			if (typeName === 'else') {
				itemPosition = parentNode.children.length;
			}
			
      // add item
			var newItem = MenuPlus.createXmlNodeFromType(type, typeName);
			var node = getMenuPlusNode(parentNode.id);
			var newTreeNode;

			if (node.confignode.length < 4) node.confignode[3] = [];
				node.confignode[3].splice(itemPosition, 0, newItem);

			newTreeNode = MenuPlus.toJstreeConfig(newItem, parentNode.id);
			newTreeNode.text = '&nbsp;'
			$jstree.create_node(parentNode, newTreeNode, itemPosition);
			$jstree.open_node (parentNode);
			$jstree.deselect_all(true);
			$jstree.select_node(newTreeNode);
			var $container = $('#jstree-container');
			var $jstree = $container.jstree(true);
			$jstree.refresh();

			if (!type.editable) {
				MenuPlus.setChangedState(true);
			}
			else {
				let timerid = setTimeout( () => {
					//debugger
					newTreeNode.mode = typeDetail.mode;
					newTreeNode.position = itemPosition;
					MenuPlus.editNewNode(parentNode, newTreeNode, 'add');
				}, 300 )



				//$jstree.refresh(true);
				//$jstree.open_node(jstreeConfig.id);
				//$jstree.deselect_all(true);
				
			}
		},

		editCreatedTreeNode: function() {
			debugger
			newTreeNode.mode = typeDetail.mode;
			newTreeNode.position = itemPosition;
			//MenuPlus.editNewNode(parentNode, newTreeNode, 'add');
		},

		setChangedState: function(changed, isImport) {
			$("#menutree").trigger('inlinesave.coalesce', {
				data: window.MP_config.root
			})

			MenuPlus.updateJsTree(false);
		},

		cannotMoveNodeUp : function(data) {
			var $jstree = $.jstree.reference(data.reference);
			var treeNode = $jstree.get_node(data.reference);
			var moveNode = getMenuPlusNode(treeNode.id);
			var moveNodeId = moveNode.confignode[1].nodeid;
			var parent = getMenuPlusNode(moveNode.treenode.parentid);
			var currentOffset = -1;
			if (moveNode.treenode.type === 'else') {
				return true;
			}
			else if (typeof parent !== 'undefined') {
				for (var k=0; k<parent.confignode[3].length; k++) {
					if (parent.confignode[3][k][1].nodeid === moveNodeId) {
						currentOffset = k;
						break;
					}
				};
			}
			
			return (currentOffset === 0);
		},

		cannotMoveNodeDown : function(data) {
			var $jstree = $.jstree.reference(data.reference);
			var treeNode = $jstree.get_node(data.reference);
			var moveNode = getMenuPlusNode(treeNode.id);
			var moveNodeId = moveNode.confignode[1].nodeid;
			var parent = getMenuPlusNode(moveNode.treenode.parentid);
			var currentOffset = -1;
			if (typeof parent !== 'undefined') {
				for (var k=0; k<parent.confignode[3].length; k++) {
					if (parent.confignode[3][k][1].nodeid === moveNodeId) {
						currentOffset = k;
						break;
					}
				}
				return (currentOffset === (parent.confignode[3].length - 1));
			}
			return false;
		},

		moveUp : function(data)
        {
			try {
				var $jstree = $.jstree.reference(data.reference);
				var treeNode = $jstree.get_node(data.reference);
				var moveNode = getMenuPlusNode(treeNode.id);
				var parent = getMenuPlusNode(moveNode.treenode.parentid);
				var moveNodeId = moveNode.confignode[1].nodeid;
				var treeParent = $jstree.get_node(parent.treenode.id);
				if (parent.confignode.length > 3) {
					var currentOffset = -1;
					for (var k=0; k<parent.confignode[3].length; k++) {
						if (parent.confignode[3][k][1].nodeid === moveNodeId) {
							currentOffset = k;
							break;
						}
					}
					if (currentOffset > 0) { // Cannot move up the first node
						$jstree.move_node(treeNode, treeParent, currentOffset-1);
						var tmp = parent.confignode[3][currentOffset];
						//parent.confignode[3].splice(currentOffset, 1);
						//parent.confignode[3].splice(currentOffset-1, 0, tmp);
						MenuPlus.setChangedState(true);
					}
				}
			}
			catch(err) {
				console.log(err);
			}
		},

		moveDown : function(data)
        {
			try {
				var $jstree = $.jstree.reference(data.reference);
				var treeNode = $jstree.get_node(data.reference);
				var moveNode = getMenuPlusNode(treeNode.id);
				var parent = getMenuPlusNode(moveNode.treenode.parentid);
				var moveNodeId = moveNode.confignode[1].nodeid;
				var treeParent = $jstree.get_node(parent.treenode.id);
				if (parent.confignode.length > 3) {
					var children = parent.confignode[3];
					var currentOffset = -1;
					for (var k=0; k<children.length; k++) {
						if (children[k][1].nodeid === moveNodeId) {
							currentOffset = k;
							break;
						}
					}
					if (currentOffset < (children.length - 1)) { // Cannot move down the last node

						// There seems to be a bug here, currentOffset+1 should work but doesn't.  +2 does work, at least for now.
						$jstree.move_node(treeNode, treeParent, currentOffset+2);
						//children.splice(currentOffset+1, 0, tmp);
						//children.splice(currentOffset, 1);
						MenuPlus.setChangedState(true);
					}
				}
			}
			catch(err) {
				console.log(err);
			}
		},

		editFormCallback: function(action, $dialog, node, formData, config, mode) {
			// 				var $jstree = $.jstree.reference(node_id);
			//debugger
			var result = false;
			var $form = $( "#menuplus-dialog-form" );
			switch (action) {
				case 'menudata':
					//debugger
					//if (mode === 'add') MenuPlus.addNewnodeToTree(formData);
					MenuPlus.updateJsTree(false);
					$('.menuplus-backdrop').remove();
					$('#coalesce-edit-dialog-parent').remove();
					result = true;
					//MenuPlus.savetreeConfig();
					MenuPlus.setChangedState(true);
					break;
				case 'update':
					if (node.nodeid === config.nodeid) {
						var $typeList = $dialog.find('[data-mp-type]');
						$typeList.each(function(index, elem) {
							var $element = $(elem);
							var data = $(elem).data();
							// processing of attributes after making changes, 
							// such as converting variables in text fields back to v1, v2 etc.
							MenuPlus.processDataForUpdate($dialog, $element, data, formData, node);
						});
						if (MenuPlus.updateDataChanged(config, formData, mode)) {
							for (var itemName in formData) { 
								node[itemName] = formData[itemName];
								config[itemName] = formData[itemName];
							}
							if (mode === 'add') MenuPlus.addNewnodeToTree(config);
						}
						MenuPlus.updateJsTree(false);
						result = true;
					}
				break;
				case 'display': // after form is displayed

					// Find all fields with a type id and configure them.
					// Used for populating dropdowns etc.
					var configAfter = [];
					$dialog.find('[data-mp-type]').each(function(index, elem) {
						var $element = $(elem);
						var elementData = $element.data();
						var elementName = null;
						var data;
						var configure = true;
						if (typeof elementData.mpType === 'string' && elementData.mpType === 'radio') {
							var $radios = $element.find(':radio');
							$radios.each(function(index, radio) {
								var radioName = $(radio).attr('name');
								if (elementName !== null && elementName !== radioName) console.log('Invalid radio configuration, all radio buttons in the same div must have the same name.');
								data = node.confignode[1][radioName];
							});
							
						}
						else if (typeof elementData.mpType === 'string' && elementData.mpType === 'enable') {
							data = node.confignode[1];
							configure = false;
							// Needs to be done last. see below
							configAfter.push({p1:$dialog, p2:$element, p3:elementData, p4:data});
						}
						else {
							elementName = $element.attr('name');
							data = node.confignode[1][elementName];
						}
						// NB these are HTML5 attributes, so data-mp-type will become mpType etc.

						if (configure) MenuPlus.configureFieldForEdit($dialog, $element, elementData, data);
					});

					// Loop through and configure fields with attributes that only work after the other field is populated
					// If you move these back into the main loop, the goto radio buttons will not be set and the 
					// associated text field will not get enabled.
					for (var y=0; y<configAfter.length; y++) {
						var params = configAfter[y];
						MenuPlus.configureFieldForEdit(params.p1, params.p2, params.p3, params.p4);
					}
					
					$form.validate({rules:formData});
					MenuPlus.configureValidation($form, node.confignode);
					// cb('display', $dialog, originalConfig, validationRules);
					// TODO configure input validation
					result = true;
				break;
				case 'validate': // After data submitted
					if (node.nodeid === config.nodeid) {
						result = $form.valid();
					}
					//cb('validate', $dialog, config, formData, config)
				break;
				default:
					result = true;
				break;
			}

			return result;// TODO any validation goes here
		},

		listLabels : function(config, labelList) {
			var name = 0, attr = 1, content = 2, children = 3;
			if (typeof labelList === 'undefined' || labelList == null) labelList = [];

			if (config[name] === 'menu' || config[name] === 'label') {

				if (typeof config[attr]['name'] !== 'undefined' 
					&& config[attr]['name'] !== null
					&& config[attr]['name'].length > 0) {
						
					labelList.push(config[attr]['name']);
				}
			}

			if (config[name] === 'variables') {
				return;
			}
			else if (config[name] === 'messages') {
				return;
			}
			else if (config.length > chil) {
				for (var i=0; i<config[chil].length; i++) {
					MenuPlus.listLabels(config[chil][i], labelList);
				}
			}
								
			labelList.sort(function (a, b) {
				return a.toLowerCase().localeCompare(b.toLowerCase());
			});
			var uniquelabels = [];
			$.each(labelList, function(i, el){
				if($.inArray(el, uniquelabels) === -1) uniquelabels.push(el);
			});

			return uniquelabels;
		},

		// var name = 0, attr = 1, cntn = 2, chil = 3;
		listVariables : function(config, variableList) {
			//console.log(JSON.stringify(config, null, 3));
			//console.log(typeof config);
			if (typeof variableList === 'undefined' || variableList == null) variableList = [];

			if (config[name] === 'variables') {
				var varData = config[chil];
				if (typeof varData !== 'undefined' && varData != null) {
					for (var i=0; i<varData.length; i++) {
						if (varData[i][name] === 'var') variableList.push(varData[i][attr]);
					}
				}
			}
			else if (config[name] === 'service') {
				return;
			}
			else if (config[name] === 'messages') {
				return;
			}
			else if (config.length > chil) {
				for (var i=0; i<config[chil].length; i++) {
					MenuPlus.listVariables(config[chil][i], variableList);
				}
			}
			//else {
				//console.log("Ignoring unexpected type "+typeof config+((typeof config === 'object')?JSON.stringify(config):''));
			//}

			return variableList;
		},

		populateVariables: function($elem, config, dataAttributes, value) {
			if (typeof config !== 'undefined') {
				var currentVarList = MenuPlus.listVariables(config.root);
				if (typeof config.varlist !== 'undefined') {
					for (var i = 0; i < config.varlist.length; i++) {
						currentVarList.push(config.varlist[i]);
					}
				}

				var uniqueVars = [];
				currentVarList.sort(function (a, b) {
					return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
				});
	
				var uniqueVars = [];
				$.each(currentVarList, function(i, el){
					if($.inArray(el, uniqueVars) === -1) uniqueVars.push(el);
				});

				if (uniqueVars.length > 0) {
					/*if (withBlank) {
						elem.append('<option class="edvar" id="edvaroption0" value=""></option>');
					}*/
					for (var i = 0; i < uniqueVars.length; i++) {
						var ht=[];
						ht.push('<option value="'+uniqueVars[i].id+'"');
						if (value === uniqueVars[i].id) {
							ht.push(' selected="selected" ');
						}
						ht.push('>'+uniqueVars[i].name+'</option>');
						$elem.append(ht.join(''));
					}
				}
				else {
					$elem.append('<option value="">No variables defined</option>');
				}
			}
		},

		populateLabels: function($elem, config, dataAttributes, value) {
			if (typeof config !== 'undefined') {
				var currentLabelList = MenuPlus.listLabels(config.root);
			}
			if (currentLabelList.length > 0) {
				/*if (withBlank) {
					elem.append('<option class="edvar" id="edvaroption0" value=""></option>');
				}*/
				for (var i = 0; i < currentLabelList.length; i++) {
					var ht=[];
					ht.push('<option value="'+currentLabelList[i]+'"');
					if (value === currentLabelList[i]) {
						ht.push(' selected="selected" ');
					}
					ht.push('>'+currentLabelList[i]+'</option>');
					$elem.append(ht.join(''));
				}
			}
			else {
				$elem.append('<option value="">No labels defined</option>');
			}
		},

		populateProperties: function($element, config, dataAttributes, value) {
			if (typeof MP_config != 'undefined' &&
				typeof MP_config.server != 'undefined' &&
				typeof MP_config.server.propertylist != 'undefined') {
				
				MP_config.server.propertylist.sort();
				for (var i = 0; i < MP_config.server.propertylist.length; i++) {
					var ht=[];
					var currentProperty = MP_config.server.propertylist[i];
					ht.push('<option value="'+currentProperty+'"');
					if (value === currentProperty) {
						ht.push(' selected="selected" ');
					}
					ht.push('>');
					ht.push(currentProperty);
					ht.push('</option>');
					$element.append(ht.join(''));
				}
			}
			else {
				$element.append('<option>Unavailable</option>');
				$element.prop('disabled', true);
			}

		},

		configureRadioOptions: function($element, config, dataAttributes, value) {
			var $radios = $element.find(':radio');
			$radios.each(function(index, radio) {
				var $current = $(radio);
				var radioValue = $current.val();
				if (radioValue === value) {
					$current.prop("checked", true);
					var data = $current.data();
					if (typeof data.mpDisable === 'string') {
						$('#'+data.mpDisable).prop('disabled', true);
					}
					if (typeof data.mpEnable === 'string') {
						$('#'+data.mpEnable).prop('disabled', false);
					}
				}
			});
			$element.on('click', function(clicked) {
				var data = $(clicked.target).data();
				if (typeof data.mpDisable === 'string') {
					$('#'+data.mpDisable).prop('disabled', true);
				}
				if (typeof data.mpEnable === 'string') {
					$('#'+data.mpEnable).prop('disabled', false);
				}
			});
		},

		configureEnableCheckbox: function($elem, MP_config, dataAttributes, config) {
			var elementname = $elem.val();
			if (typeof elementname !== 'undefined' && elementname !== null) {
				var checkValue = config[elementname];
				if (typeof checkValue !== 'undefined' && checkValue !== null && checkValue.length > 0) {
					$('#'+elementname).prop('disabled', false);
					$elem.prop('checked', true);
				}
				else {
					$('#'+elementname).val('');
					$('#'+elementname).prop('disabled', true);
				}
				$elem.on('click', function(clicked) {
					if ($elem.prop('checked')) {
						$('#'+elementname).prop('disabled', false);
					}
					else {
						$('#'+elementname).val('');
						$('#'+elementname).prop('disabled', true);
						config[elementname] = '';
					}
				});
			}
			else {
				console.log('The value of the checkbox needs to be set for it to work');
			}
		},

		addPopupVarlist: function($elem, MP_config, dataAttributes, value) {
			'onkeydown="return text_keydown(event)" onkeypress="return text_keypress(event)"'

			
			$elem.on('keydown', text_keydown);
			$elem.on('keypress', text_keypress);
			/*var currentVarList = MenuPlus.listVariables(MP_config.root);
			console.log('addPopupVarlist');
			var ht = [];
			ht.push('<div id="menuplusPopup">');
				 ht.push('<div id="pop_ctrl"><i class="fa fa-bars"></i></div>');
				ht.push('<ul id="demo_ul">');
				for (var i=0; i<currentVarList.length; i++) {
					ht.push('<li class="demo_li">');
					ht.push('<div><i class="fa fa-home"></i></div>');
					ht.push('<div>');
					ht.push(currentVarList[i]);
					ht.push('</div>');
					ht.push('</li>');
				}
				ht.push('</ul>');
			ht.push('</div>');


			$body.append(ht.join(''));
			$('#menuplusPopup').popmenu({
				'controller': 'true',
				'width': '100%',
				'background': '#34495e',
				'focusColor': '#1abc9c',
				'borderRadius': '10px',
				'top': '0',
				'left': '0',
				'iconSize': '100px'
				});*/
		},

		valueHasChanged: function(inputName, convertedText, configtext) {
			var changed = false;
			var convertedTextValid = false;
			var configTextValid = false;
			if (typeof convertedText !== 'undefined' && convertedText !== null ) convertedTextValid = true;
			if (typeof configtext !== 'undefined' && configtext !== null ) configTextValid = true;
			if (convertedTextValid !== configTextValid) changed == true;

			if (!changed && convertedTextValid && configTextValid && convertedText !== configtext) {
				changed = true;
			}

			return changed;
		},

		addNewnodeToTree: function(config) {
			var originalConfig = getMenuPlusNode(config.nodeid);
			var treenode = originalConfig.treenode;
			var $jstree = $.jstree.reference(treenode.parentid);
			var parentNode = $jstree.get_node(treenode.parentid);
			var parentConfig = getMenuPlusNode(treenode.parentid);
			var itemPosition = 0;

			if (typeof treenode.mode !== 'undefined' && treenode.mode === 'after') {
				itemPosition = treenode.position;
				delete(treenode.mode);
				delete(treenode.position);
			}

			if (treenode.type === 'else') {
				itemPosition = parentNode.children.length;
			}
			treenode.text = MenuPlus.getNodeText(originalConfig.confignode);
			if (parentConfig.confignode.length < 4) parentConfig.confignode.push([]);
			parentConfig.confignode[3].splice(itemPosition, 0, originalConfig.confignode);
			MenuPlus.setChangedState(true);

			//newTreeNode = MenuPlus.toJstreeConfig(newItem, parentNode.id);


			var result = $jstree.create_node(parentNode, treenode, itemPosition);
			if (!result) {
				console.log($jstree.last_error());
				console.log('Parent :-');
				logJson(parentNode);
				console.log('Child :-');
				logJson(treenode);
			}
			$jstree.open_node (parentNode);
			$jstree.deselect_all(true);
			$jstree.select_node(treenode);
		},

		updateDataChanged: function(config, formData, mode) {
			var changed = false;
			if (mode === 'add') {
				changed = true;
			}
			else {
				for (var itemName in formData) {
					if (config[itemName] !== formData[itemName]) {
						MenuPlus.setChangedState(true);
						changed = true;
						break;
					}
				}
			}
			return changed;
		},

		/*
			Perform processing after a field is changed, such as
			flagging that the application contains changes and
			converting the values of variables in text fields 
			back to their internal format
		*/
		// 
		processDataForUpdate: function($dialog, $elem, dataAttributes, formData, config) {
			var inputName = $elem.attr('name');
			if (typeof inputName !== 'undefined' && inputName !== null && inputName.length > 0) {
				switch(dataAttributes.mpType) {
					case 'varlist':
					break;
					case 'labellist':
					break;
					case 'radio':
					break;
					case 'enable':
					break;
					case "properties" :
					break;
					case "popupvarlist" :
						var convertedText = MenuPlus.convertVariables($elem.val(), false);
						var configtext = config[inputName];
						formData[inputName] = convertedText;
						if (MenuPlus.valueHasChanged(inputName, convertedText, configtext)) {
							
							//$('body').data('unsaved-changes', "1");
							MenuPlus.setChangedState(true);
						}
					break;
					default:
						console.log('Unrecognised type '+dataAttributes.mpType);
					break;
				}
			}
		},

		preventAttachToNode: function(data) {
			var result = true;

			var $jstree = $.jstree.reference(data.reference);
			var parentNode = $jstree.get_node(data.reference);
			var grandParentNode;
			var nodeInfo = data.item.type;
			var nodeType = (typeof nodeInfo === 'string')?nodeInfo:nodeInfo.type;
			var nodeMode = (typeof nodeInfo === 'string')?'add':nodeInfo.mode;
			switch(nodeType) {
				case 'else':
					if (nodeMode === 'add') {
						result = (parentNode.type === 'if' || parentNode.type === 'ifexp' || parentNode.type === 'assigned' || parentNode.type === 'equals')?false:true;

						if (!result && typeof parentNode.children !== 'undefined' && parentNode.children !== null) {
							for (var ch=0; ch<parentNode.children.length; ch++) {
								var currentChild = getMenuPlusNode(parentNode.children[ch]);
								if (currentChild.treenode.type === 'else') {
									result = true;
									break;
								}
							}
						}
					}
					else if (nodeMode === 'after') {
						result = (parentNode.type === 'item' || parentNode.type === 'variableitems' || parentNode.type === 'display')?false:true;
						if (!result) {
							grandParentNode = $jstree.get_node(parentNode.parent);
							if (typeof grandParentNode !== 'undefined' && grandParentNode !== null) {
								result = (grandParentNode.type === 'if' || grandParentNode.type === 'ifexp' || grandParentNode.type === 'assigned' || grandParentNode.type === 'equals')?false:true;
								if (!result && typeof grandParentNode.children !== 'undefined' && grandParentNode.children !== null) {
									for (var ch=0; ch<grandParentNode.children.length; ch++) {
										var currentChild = getMenuPlusNode(grandParentNode.children[ch]);
										if (currentChild.treenode.type === 'else') {
											result = true;
											break;
										}
									}
								}
							}
							
						}
					}
				break;
				default:
					result = true;
				break;
			}

			return result;
		},

		configureFieldForEdit: function($dialog, $elem, dataAttributes, value) {
			switch(dataAttributes.mpType) {
				case 'varlist':
					MenuPlus.populateVariables($elem, MP_config, dataAttributes, value);
				break;
				case 'labellist':
					MenuPlus.populateLabels($elem, MP_config, dataAttributes, value);
				break;
				case 'radio':
					MenuPlus.configureRadioOptions($elem, MP_config, dataAttributes, value);
				break;
				case 'enable':
					MenuPlus.configureEnableCheckbox($elem, MP_config, dataAttributes, value);
				break;
				case "properties" :
					MenuPlus.populateProperties($elem, MP_config, dataAttributes, value);
				break;
				case "popupvarlist" :
					MenuPlus.addPopupVarlist($elem, MP_config, dataAttributes, value);
					$elem.val(MenuPlus.convertVariablesForEdit($elem.val()));
				break;
				default:
					console.log('Unrecognised type '+dataAttributes.mpType);
				break;
			}
		},

		validateLanguageField: function(value, element, params, existingConfig) {
			var valid = true;
			return valid;
		},

		configureValidation: function($form, config) {
			var existingConfig = config;
			var $fields = $form.find('.ussdcode');
			$fields.off().on('keydown', function(e) {
				var isModifierkeyPressed = (e.metaKey || e.ctrlKey || e.shiftKey);
				var isCursorMoveOrDeleteAction = ([46,8,37,38,39,40].indexOf(e.keyCode) != -1);
				var isNumKeyPressed = (e.keyCode >= 48 && e.keyCode <= 58) || (e.keyCode >=96 && e.keyCode <= 105);
				var isEnterKeyPressed = ([13].indexOf(e.keyCode) != -1);
				var isEscapeKeyPressed = ([27].indexOf(e.keyCode) != -1);
				var isCommaKeyPressed = ([188].indexOf(e.keyCode) != -1);
				var vKey = 86, cKey = 67,aKey = 65;
				switch(true){
					case isCursorMoveOrDeleteAction:
					case isModifierkeyPressed == false && isNumKeyPressed:
					case (e.metaKey || e.ctrlKey) && ([vKey,cKey,aKey].indexOf(e.keyCode) != -1):
					case isEnterKeyPressed:
					case isEscapeKeyPressed:
					case isCommaKeyPressed:
						break;
					default:
						e.preventDefault();
				}
				/*var key = event.which || event.charCode || event.keyCode;
				switch (key) {
					case 8  : // backspace
					case 27 : // esc
					case 13 : // enter
						return true;
					default:
						if ((key > 47 && key < 58)|| (key === 188)) return true;
				}
				
				return false;*/
			});

			$.validator.addMethod("mpValidateLanguage", function(value, element, params) {
				return MenuPlus.validateLanguageField(value, element, params, existingConfig);
			}, "* At least one of the language fields must contain a text desciprion.");
		},

		sanitizeConfiguration: function(config) {
			//console.log('processing '+config[0]);
			//debugger
			config[0] = $.trim(config[0]);

			const recusiveTrim = item => {
				if (Array.isArray(item) || typeof item === 'object') {
					for (var key in item) {
					recusiveTrim(item[key]);
					}
				} else if (typeof item === 'string') {
					item = item.trim();
				}
			};
		
			for (var attr in config[1]) {
				if (typeof attr === 'undefined' || attr === null) continue;
				if (typeof config[1][attr] !== 'undefined' && config[1][attr] !== null) {
					// config[1][attr]=$.trim(config[1][attr]);
					recusiveTrim(config[1][attr]);
				}
			}

			if (config.length > 3) {
				for (var i=0; i<config[3].length; i++) {
					MenuPlus.sanitizeConfiguration(config[3][i]);
				}
			}
			return config;
		},

		copyConfig : function(s, t)
		{
			var name = 0, attr = 1, cntn = 2, chil = 3;
			t[name] = s[name];
			t[cntn] = s[cntn];
			var c = s[chil], i;
			if (c)
			{
				var tc = t[chil] = [];
				for (i=0; i<c.length; i++)
				{
					tc[i] = [];
					this.copyConfig(c[i], tc[i])
				}
			}
			if (s[attr])
			{
				t[attr] = {};
				for (i in s[attr])
					t[attr][i] = s[attr][i];
			}
		},

		updateConfig: function(config) {
			MP_config.root = eval ("(" + s + ")");
			MenuPlus.save_config = [];
			MenuPlus.copyConfig(MP_config.root, MenuPlus.save_config);
		},

		updateFromConfig: function(config, langs) {
			var langList = (typeof langs !== 'object')?MP_config.langs:langs;
			MenuPlus.save_config = [];
			MenuPlus.copyConfig(config, MenuPlus.save_config);
			MP_config.root = MenuPlus.sanitizeConfiguration(config);
		},

		getConfigNodeByOffset: function(index) {
			return MP_config.root[3][index];
		},

		loadVariablesAndMessages: function() {
			try {
//debugger
				if (typeof MP_config.mpvarlist !== 'undefined' && MP_config.mpvarlist !== null) {
					MP_config.root[chil].push(['mpvarlist',
					{},
					"",
					MP_config.mpvarlist
					]);
				}
				if (MP_config.root.length > 3) {
					var sections = MP_config.root[3];
					for (let i=0; i<sections.length; i++) {
						// Ignore service section
						if (sections[i][0] === 'service') continue;
						if (sections[i][0] === 'variables') {
							//console.log(JSON.stringify(sections[i], null, 2));
						}
						//var tableConfig = tableConfiguration[sections[i][0]];
						//var tableContent = MPTables.getTable(sections[i], tableConfig, i);
						//$(tableConfig.targetDiv).html(tableContent);
					}
				}
			}
			catch(err) {
				console.log(err);
			}
			
		},

		redrawWithNewConfig: function(callback) {
			var $container = $('#jstree-container');
			var $jstree = $container.jstree(true);
			$jstree.refresh();
			//MenuPlus.$container.jstree(true).refresh();
			//MenuPlus.$container.jstree(true).redraw();
			MenuPlus.loadVariablesAndMessages();
			if (typeof callback == 'function') callback();
			$('.menuplus-backdrop').remove();
		},

		discardChanges: function(data, mode) {
			// debugger
			if (MenuPlus.saved_root_config) {
				MP_config.root = MenuPlus.saved_root_config;
				delete MenuPlus.saved_root_config;
			}
			if (mode === 'add') {
				MenuPlus.deleteNode({
					reference: data.nodeid
				});
			}
			MenuPlus.redrawWithNewConfig();
		},

		init: function(readonly, config, langs) {
			//debugger
			MenuPlus.updateFromConfig(config, langs);
			MenuPlus.loadVariablesAndMessages();
			MenuPlus.initJsTree(config, langs, readonly);
		},

		loadConfig : function(callback) {
			debugger
			// Application Configuration
			var configUrl = '/menuplus/config';
			var config = MP_config;
			var jqxhr = $.get( configUrl, function(resp) {
				config.langs = {
					ENG: 'English',
					FRA: 'French',
					EWE: 'Ewe'
				}
				config.root = resp
				if (typeof callback === 'function') callback(config.root, config.langs);
				// First load server config, IE. info that comes from a config file
				/*if (typeof resp.server !== 'undefined') config.server = resp.server;
				if (typeof resp.sce !== 'undefined') config.sce = resp.sce;
				if (typeof resp.langs !== 'undefined') config.langs = resp.langs;
				if (typeof resp.itemlist !== 'undefined') {
					var itemlist = resp.itemlist;
					if (typeof itemlist.properties !== 'undefined') config.server.propertylist = itemlist.properties;
					if (typeof itemlist.varlist !== 'undefined') config.mpvarlist = itemlist.varlist;
				}*/
			})
			.fail(function(err) {
				bootbox.alert({
					title: "Load Configuration",
					message: "Unable to load menuplus configuration from "+configUrl
				});
			});
		},

		getMenuPlusNode: function(node_id) {
			return getMenuPlusNode(node_id);
		},
		savetreeConfig: function() {
			//delete(MP_config.root.mpvarlist);
			// debugger
			var config = MenuPlus.sanitizeConfiguration(MP_config.root);
			
			$('#menutree').trigger('inlinesave.coalesce', {data:config})
		}
    } // end of object
