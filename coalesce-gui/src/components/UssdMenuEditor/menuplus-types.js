/* eslint-disable */
window.name = 0, window.attr = 1, window.cntn = 2, window.chil = 3; // want to remove these, but too many references in existing code.

/*
        type configuration used via the type plugin for jstree.
        editable, params and template are specific to menuplus

        editable => Used to mark this element as having values that can be edited
        params => used for the header of the edit dialog
        params.rules => rules object to be used by j<PERSON>y validate for validation rules.
        template => json object that will br added to the model when adding a new type from the context menu.
    */

   window.treeTypeSet = {
    statementSet: [
        'dynamic-menu', 'display','goto','call','hux','component', 'menuModule','menu','catchall','ifexp','if','assigned',
        'equals','ask','label','end','match','assign','property','clear','set','variableitems'
    ],

    statementSetIf: [
        'dynamic-menu', 'display','goto','call','hux','component', 'menuModule','menu','catchall','ifexp','if','assigned',
        'equals','else','ask','label','end','match','assign','property','clear',
        'set','item','variableitems'
    ],

    statementSetUssd: [
        'dynamic-menu', 'display','goto','call','hux','component', 'menuModule', 'menu','catchall','ifexp','if','assigned',
        'equals','ask','label','end','match','oneshot','assign','property',
        'clear','set'
    ],
    menuSet: [
        'dynamic-menu', 'display','ifexp','if','oneshot','assigned','equals','end','item','variableitems','label', 'call'
    ],
    rootSet: [
        'dynamic-menu', 'display','call','assign','component','property','ifexp','if','oneshot','assigned','equals','end','item','variableitems','label'
    ]
};

window.treeTypeConfig = {



    service: {
        icon : "menuplus-service",
        editable: false,
				//valid_children: treeTypeSet.statementSetUssd,
				valid_children: treeTypeSet.rootSet,
        mps: {
            draggable: false
        },
        context: {
						//edit: "Change Service Name",
						edit: false,
            del: false
        },
        params:{
            title: '%MODE% Service Configuration',
            help: 'The service name is for internal purposes and should be a name that relates to the use of the menu.',
            rules: {
                text: {
                    required: true
                }
            }
        }, 
        template: {
            text:"your text here"
        }
    },
    //service: serviceType, // bodge for jstree root always # issue
    '#': {
			icon : "menuplus-root",
			editable: false,
			//valid_children: treeTypeSet.statementSetUssd,
			valid_children: treeTypeSet.rootSet,
			mps: {
					draggable: false
			},
			context: {
					//edit: "Change Service Name",
					edit: false,
					del: false
			},
			params:{
					title: '%MODE% Service Configuration',
					help: 'The service name is for internal purposes and should be a name that relates to the use of the menu.',
					rules: {
							text: {
									required: true
							}
					}
			}, 
			template: {
					text:"your text here"
			}
    }, // bodge for jstree root always # issue

    ['dynamic-menu']: {
        //icon : path+"server--arrow.png",
        icon : 'menuplus-dynamic-menu',
        valid_children: [],
        editable: true,
        context: {
            edit: "Edit Dynamic Menu",
            del: "Delete Dynamic Menu",
        },
        params:{
            title: '%MODE% Dynamic Menu Configuration',
            help: 'Please type the new Menu Name and press enter.',
            rules: {
                text: {
                    required: true,
                    mpValidateLanguage: "text"
                }
            }
        },
        template: {
            areOffersForBNumber: false,
            subscriberNumber: "",
            title: "",
            header: {
                text: "",
            },
            footer: {
                text: "",
            },
            next: {
                selector: "",
                text: "",
            },
            back: {
                selector: "",
                text: "",
            },
            menuStyle: "default",
            tags: [],
            properties: {}
        }
    },

    display: {
        //icon : path+"server--arrow.png",
        icon : 'menuplus-display',
        valid_children: [],
        editable: true,
        context: {
            edit: "Edit Display",
            del: "Delete Display",

        },
        params:{
            title: '%MODE% Display Configuration',
            help: 'Please type the new Menu Name and press enter.',
            rules: {
                text: {
                    required: true,
                    mpValidateLanguage: "text"
                }
            }
        }, 
        template: {
            text:"your text here"
        }
    },
    
    goto: {
        icon : "menuplus-goto",
        editable: true,
        context: {
            edit: "Change Goto",
            del: "Delete Goto"
        },
        valid_children: [],
        params:{
            title: '%MODE% Goto Configuration',
            help: 'Please choose a destination.',
            rules: {

            }
        }, 
        template: {
            location:"1"
        }
    },
    call: {
        icon : "menuplus-call",
        editable: false,
        context: {
            edit: "",
            del: ""
        },
        valid_children: [],
        params:{
            title: '%MODE% Call Configuration',
            help: 'TODO',
            rules: {

            }
        }, 
        template: {
            text:"your text here"
        }
		},
		
		component: {
			icon : "menuplus-component",
			editable: true,
			context: {
				edit: "Change Component",
				del: "Delete Component"
			},
			valid_children: [],
			params:{
					title: 'Component Configuration',
					help: 'TODO',
					rules: {

					}
			}, 
			template: {
					text:"your text here"
			}
		}, 
		menuModule: {
			icon : "menuplus-component",
			editable: true,
			context: {
				edit: "Change Module",
				del: "Delete Module"
			},
			valid_children: [],
			params:{
					title: 'Module Configuration',
					help: 'TODO',
					rules: {

					}
			}, 
			template: {
					text:"your text here"
			}
		}, 
    
    menu: {
        icon : "menuplus-menu",
        editable: true,
        context: {
            edit: "Change Menu Name",
            del: "Delete Menu"
        },
        valid_children: treeTypeSet.menuSet,
        params:{
            title: '%MODE% Menu Configuration',
            help: 'Please provide a name for the menu',
            rules: {
                name: {
                    required: true
                }
            }
        }, 
        template: {
            name:""
        }
    },
    
    if: {
        icon : "menuplus-if",
        valid_children: treeTypeSet.statementSetIf,
        context: {
            edit: "Edit If",
            del: "Delete If"
        },
        editable: true,
        params:{
            title: '%MODE% If Configuration',
            help: 'TODO',
            rules: {
                constant: {
                    required: true
                }
            }
        }, 
        template: {
            variable:"none",
            operator:"eq",
            constant:"0"
        }
    },
    
    equals: {// unused ??
        icon : "menuplus-equals",
        valid_children: treeTypeSet.statementSetIf,
        editable: "",
        params:{
            title: '%MODE% If Equals Configuration',
            help: 'Please enter the details of the comparison',
            rules: {
                constant: {
                    required: true
                }
            }
        }, 
        template: {
            type:"equals",
            variable:"none",
            constant:""
        }
    },
    
    ask: {
        icon : "menuplus-ask",
        valid_children: [],
        editable: true,
        context: {
            edit: "Change Ask Settings",
            del: "Delete Ask"
        },
        params:{
            title: '%MODE% Ask Configuration',
            help: 'Please enter the prompt messaghe for the end user and select a variable that will be populated with the users response.',
            rules: {
                text: {
                    required: true,
                    mpValidateLanguage: "text"
                },
                variable: {
                    required: true
                }
            }
        }, 
        template: {
            text:"some text here",
            variable:""
        }
    },
    
    ussd: {
        icon : "menuplus-ussd",
        editable: true,
        context: {
            edit: "Change USSD Code",
            del: "Delete USSD Code"
        },
        valid_children: treeTypeSet.statementSetUssd,
        params:{
            title: '%MODE% USSD Configuration',
            help: 'Please enter the required USSD code.  The code must contain no more than 4 digits.  You can also define multiple codes by seperating them with a comma.',
            rules: {
                code: {
                    required: true,
                    mpValidateUssdCode: "code"
                }
            }
        }, 
        template: {
            code:""
        }
    },

    label: {
        icon : "menuplus-label",
        valid_children: [],
        editable: true,
        context: {
            edit: "Edit Label",
            del: "Delete Label"
        },
        params:{
            title: '%MODE% Label Configuration',
            help: 'Please enter a name for the label.  The label can be used as a target location for the goto statement',
            rules: {
                name: {
                    required: true
                }
            }
        }, 
        template: {
            name:""
        }
    },
    
    match: {
        icon : "menuplus-match",
        valid_children: treeTypeSet.statementSet,
        editable: true,
        context: {
            edit: "Edit Match",
            del: "Delete Match"
        },
        params:{
            title: '%MODE% Match Configuration',
            help: 'Please enter the USSD request string that you wish to match',
            rules: {
                request: {
                    required: true
                }
            }
        }, 
        template: {
            request:"#"
        }
    },

    oneshot: {
        icon : "menuplus-oneshot",
        valid_children: treeTypeSet.statementSet,
        editable: true,
        context: {
            edit: "Edit Oneshot",
            del: "Delete Oneshot"
        },
        params:{
            title: '%MODE% One Shot Configuration',
            help: 'Please enter the neshot request string.',
            rules: {
                request: {
                    required: true
                }
            }
        }, 
        template: {
            match:"#"
        }
    },

    set: {
        icon : "menuplus-set",
        valid_children: [],
        editable: true,
        context: {
            edit: "Edit Set",
            del: "Delete Set"
        },
        params:{
            title: '%MODE% Set Configuration',
            help: 'Please select the variable that will be set.',
            rules: {

            }
        }, 
        template: {
            variable:""
        }
    },

    clear: {
        icon : "menuplus-clear",
        valid_children: [],
        editable: true,
        context: {
            edit: "Edit Clear",
            del: "Delete Clear"
        },
        params:{
            title: '%MODE% Clear Configuration',
            help: 'Please select the variable that will be cleared',
            rules: {

            }
        }, 
        template: {
            variable:""
        }
    },

    assign: {
        icon : "menuplus-assign",
        valid_children: [],
        editable: true,
        context: {
            edit: "Edit Assign",
            del: "Delete Assign"
        },
        params:{
            title: '%MODE% Assign Configuration',
            help: 'Please select a variable and enter the value that will be assigned to the selected variable',
            rules: {
                value: {
                    required: true
                }
            }
        }, 
        template: {
            variable:"any",
            value:"any"
        }
    },
   
    property: {
        icon : "menuplus-property",
        valid_children: [],
        editable: true,
        context: {
            edit: "Change Property",
            del: "Delete Property"
        },
        params:{
            title: '%MODE% Property Configuration',
            help: 'Please select a variable and property.  At runtime, the variable will be populated with the property value from the SCE system.',
            rules: {

            }
        }, 
        template: {
            variable:"",
            property:""
        }
    },

    assigned: {
        icon : "menuplus-assigned",
        valid_children: treeTypeSet.statementSetIf,
        editable: true,
        context: {
            edit: "Change Assigned",
            del: "Delete Assigned"
        },
        params:{
            title: '%MODE% If Assigned Configuration',
            help: 'Please select the variable which will be checked to see if it contains any value',
            rules: {

            }
        }, 
        template: {
            type:"assigned",
            variable:""
        }
    },

    ifexp: {
        icon : "menuplus-ifexp",
        valid_children: treeTypeSet.statementSetIf,
        editable: true,
        context: {
            edit: "Edit Expression",
            del: "Delete If"
        },
        params:{
            title: '%MODE% If Expression Configuration',
            help: 'Please select the details of the comparison',
            rules: {
                constant: {
                    required: true
                }
            }
        }, 
        template: {
            variable:"",
            operator:"gt",
            value:""
        }
    },

    else: {
        icon : "menuplus-else",
        context: {
            edit: false,
            del: "Delete Else"
        },
        valid_children: treeTypeSet.statementSet,
        editable: false,
    },

    hux: {
        icon : "menuplus-hux",
        editable: true,
        context: {
            edit: "Change HuX Settings",
            del: "Delete HuX Call"
        },
        valid_children: [],
        params:{
            title: '%MODE% HuX Configuration',
            help: 'Please enter the required values for the external system.',
            rules: {
                address: {
                    required: true
                },
                port: {
                    required: true,
                    range: [0, 65535]
                }
            }
        }, 
        template: {
            address:"",
            port:"4021",
            url:"/ash",
            code:"",
            request:"#",
            variable:""
        }
    },

    catchall: {
        icon : "menuplus-catchall",
        editable: true,
        context: {
            edit: "Change Catch All Settings",
            del: "Delete Catch All"
        },
        valid_children: [],
        params:{
            title: '%MODE% Catch All Configuration',
            help: 'Please complete the details for the remote system that the catchall will send the request to.',
            rules: {
                address: {
                    required: true
                },
                port: {
                    required: true,
                    range: [0, 65535]
                }
            }
        }, 
        template: {
            address:"",
            port:"4021",
            url:"/ash",
            code:"",
            request:"#",
            variable:""
        }
    }, 
    
    itemselection: {// TODO, not sure what this is
        icon : "menuplus-itemselection",
        valid_children: [],
        editable: true,
        params:{
            title: '%MODE% Item Selection Configuration',
            help: 'Please select a variable',
            rules: {

            }
        }, 
        template: {
            variable:"any"
        }
    },
    variableitems: {
        icon : "menuplus-variableitems",
        valid_children: [],
        editable: true,
        context: {
            edit: "Edit Variable Items",
            del: "Delete Variable Items"
        },
        params:{
            title: '%MODE% Variable Items Configuration',
            help: 'Please select a variable that must be populated with the required variable items',
            rules: {

            }
        }, 
        template: {
            variable:"",
            index:1
        }
    },
    item: {
        icon : "menuplus-item",
        valid_children: treeTypeSet.statementSet,
        editable: true,
        context: {
            edit: "Edit Item",
            del: "Delete Item"
        },
        params:{
            title: '%MODE% Menu Item Configuration',
            help: 'Please enter a number and description for the menu item',
            rules: {
                text: {
                    required: true,
                    mpValidateLanguage: "text"
                }
            }
        }, 
        template: {
            number:"",
            text:"New Menu Item Text"
        }
    },

    end: {// Not needed
        icon : "menuplus-end",
        valid_children: [],
        editable: false,
        context: {
            edit: false,
            del: "Delete End"
        },
    },

    default : {
        icon : "menuplus-default",
        valid_children: [],
        editable: true,
        context: {
            displayName: "Default",
            edit: "Edit Me",
            del: "Delete Me"
        },
        params:{
            title: '%MODE% Default Configuration',
            help: 'Please enter some text',
            rules: {

            }
        }, 
        template: {
            text:"your text here"
        }
    }
};

/*
    these messages are loaded on a blank configuration when no messages exist.
*/
window.defaultMessages = [
    {
        description: "Message displayed when the user chooses a incorrect option at a menu.",
        id: "MENU_INVALID_OPTION",
        ENG: "Sorry, you have chosen a invalid option. Please try again.",
        FRA: "Désolé, vous avez choisi une option non valide. Veuillez réessayer.",
    },
    {
        description: "Message displayed when a menu item is chosen but the item is empty.",
        id: "MENU_EMPTY_ITEM",
        ENG: "You have chosen a menu item which contains no further functionality.",
        FRA: "Vous avez choisi une option de menu qui ne contient pas d'autres fonctionnalités.",
    },
    {
        description: "Message displayed when the end of the menu hierarchy is reached.",
        id: "MENU_END",
        ENG: "End of menu hierarchy reached, ending session.",
        FRA: "Fin de la hiérarchie du menu atteint, la session se termine.",
    },
    {
        description: "Message displayed when a request comes in that is missing a '*'.",
        id: "INVALID_REQUEST_STAR",
        ENG: "Invalid request, please start the request with a '*'.",
        FRA: "Demande non valide, Veuillez commencer la demande avec un '*'.",
    },
    {
        description: "Message displayed when a request comes in that is missing a '#'.",
        id: "INVALID_REQUEST_HASH",
        ENG: "Invalid request, please end the request with a '#'.",
        FRA: "Demande non valide, Veuillez terminer la demande avec un '#'.",
    },
    {
        description: "Message displayed when a request comes in which is too short.",
        id: "INVALID_REQUEST_TOO_SHORT",
        ENG: "Request too short.",
        FRA: "Demande trop courte.",
    },
    {
        description: "Message displayed when a request comes in where the USSD service code cannot be matched to the service codes in the menu.",
        id: "INVALID_SERVICE_CODE",
        ENG: "Invalid Service Code.",
        FRA: "Code de service non valid.",
    },
    {
        description: "Message displayed when a request needs to communicate a remote server but cannot.",
        id: "REMOTE_SERVER_ERROR",
        ENG: "Remote service unavailable",
        FRA: "Remote service disponible",
    },
    /*{
        description: "Message displayed when a request needs variable items from a remote server but finds none.",
        id: "NO_VARIABLE_ITEMS",
        ENG: "Sorry, you do not qualify for any of the available bundles",
        FRA: "Sorry, you do not qualify for any of the available bundles",
    },*/
    {
        description: "Menu Session Timeout experienced",
        id: "SESSION_TIMEOUT",
        ENG: "Remote service unavailable",
        FRA: "Remote service disponible",
    },
];

window.tableConfiguration = {
    variables :   {
        type:'table',
        modes: 'aed', // a for add, e for edit, d for delete
        layout: 'V',
        heading: 'User Defined Variables',
        targetDiv: '#cs-user-variables',
        caption:'Variable',
        fields: [
            'h:id:ID:50',
            'a:name:Name:100',
            'a:type:Data Type:100',
            'a:description:Description:400',
            'a:defaultvalue:Default Value:300'
        ],
        add: '[`var`,{id:`newid`,type:`type`,name:`name`,description:`description`,defaultvalue:`defaultvalue`},``,[]]'
    },
    mpvarlist :   {
        type:'table',
        modes: '', // a for add, e for edit, d for delete
        layout: 'V',
        heading: 'System Defined Variables',
        targetDiv: '#cs-system-variables',
        caption:'Variable',
        postfix: ' from request',
        fields: [
            'p:name:Name:100',
            'q:param:Description:400'
        ]
    },

    messages  :  {
        type:'table',
        modes:'e', // a for add, e for edit, d for delete
        layout:'V',
        targetDiv:'#cs-config-messages',
        caption:'Message',
        fields:[
            'a!:id:Identifier:200',
            'a!:description:Description:400',
            't:language(id!;50,content;700):Languages (ISO Language Code and Message Text):800'
        ],
        add:'[`message`,{id:`n`,description:`description`},``,[[`language`,{id:`EN`},`This is message a`,[]], [`language`,{id:`AR`},`This is message 2`,[]] ]]'
    },
};

window.treeTypeConfig.canDrag = function(type) {
	var result = true;
	try {
		if (this[type].hasOwnProperty('mps') && this[type].mps.hasOwnProperty('draggable'))
			result = this[type].mps.draggable;
	}
	catch(err) {
		console.log("menuplus::canDrag - Invalid type "+type);
		console.log(err);
		result =  false;
	}
	return result;
}

window.treeTypeConfig.getTypeByName = function(name) {
	return (treeTypeConfig.hasOwnProperty(name))?treeTypeConfig[name]:null;
};

window.treeTypeConfig.childTypeAllowedByName = function(childType, parentName) {
	var parentType = treeTypeConfig.getTypeByName(parentName);
	var result = true;
	if (Array.isArray(parentType.valid_children)) {
		result = ($.inArray(childType, parentType.valid_children) != -1);
	}
	else {
		result = (parentType.valid_children !== -1);
	}
	return result;
};

window.treeTypeConfig.childTypeAllowedByNode = function(node, node_parent) {
	var result = true;

	var $jstree = $.jstree.reference(node_parent);
	var parentType = $jstree.get_rules(node_parent);
	var childType = $jstree.get_rules(node);
	if (Array.isArray(parentType.valid_children)) {
		result = ($.inArray(childType.type, parentType.valid_children) != -1);
	}
	else {
		result = (parentType.valid_children !== -1);
	}
	return result;
};

window.treeTypeConfig.childAllowed = function(operation, node, node_parent, node_position, more) {
	// operation can be 'create_node', 'rename_node', 'delete_node', 'move_node', 'copy_node' or 'edit'
	// in case of 'rename_node' node_position is filled with the new node name
	//return operation === 'rename_node' ? true : false;

	var result = true;
	switch(operation) {
		case 'move_node':
			result = treeTypeConfig.childTypeAllowedByNode(node, node_parent);
		case 'copy_node': // Intentional fallthrough, this check is for both move and copy.
			break;
		default:
			result = true;
	}
	return result;
};

/*
	given the config in internal form, it will add any missing default messages.
*/
window.treeTypeConfig.findMessageById = function(config, message_id) {
	var foundMessage = null;
	for (var j=0; j<config.length; j++) {
		var currentMessage = config[j];
		if (currentMessage[1].id === message_id) {
			foundMessage = currentMessage;
		}
	}
	return foundMessage;
},

window.treeTypeConfig.addMissingMessages = function(config, langs) {
	for (var i=0; i<defaultMessages.length; i++) {
		var currentMessage = defaultMessages[i];
		var existingMessage = treeTypeConfig.findMessageById(config[3], currentMessage.id);
		if (existingMessage === null) {
			existingMessage = [
				"message",
				{
					id: currentMessage.id,
					description: currentMessage.description
				},
				"",
				[]
			];
			config[3].push(existingMessage);
		}
		for (var lang in langs) {
			var foundLang = null;
			for (var m=0; m<existingMessage[3].length; m++) {
				var currentLang = existingMessage[3][m];
				if (currentLang[1].id === lang) {
					foundLang = currentLang;
					break;
				}
			}
			if (foundLang === null) {
				foundLang = [
					"language",
					{
						id: lang
					},
					""
				]
				existingMessage[3].push(foundLang);
			}
			if (foundLang[2].length <= 0)foundLang[2] = currentMessage[lang];
		}
	}
};
