<template>
  <div>
    <dialog-heading
      v-show="showHeading"
      title="Details to display on subscribers handset"
      icon="fa-mobile-alt"
      :value="value"
      @dialogClosed="dialogClosed"
    >
    </dialog-heading>

    <div class="row">
      <div class="col-12">
        <language-text ref="langs" :value="value" :is-dialog-open="isDialogOpen" @input="updateText"> </language-text>
      </div>
    </div>
  </div>
</template>

<script>
// slide 70
/* eslint-disable import/no-unresolved */
import LanguageText from '@/components/menuitems/LanguageText.vue';
import DialogHeading from '@/components/menuitems/DialogHeading.vue';

export default {
  name: 'DisplayItem',
  components: {
    LanguageText,
    DialogHeading,
  },
  props: {
    value: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      isDialogOpen: true,
    };
  },
  computed: {
    showHeading() {
      return true;
    },
  },
  mounted() {
    // ---- Set the 'old' value - only happens on item load (once), is overwritten on each load (intentionally)
    this.oldValue = JSON.parse(JSON.stringify(this.value));
  },

  methods: {
    dialogClosed() {
      this.isDialogOpen = false;
    },
    updateText(data) {
      // debugger
      let valid = true;
      let changed = false;

      // ---- This is a necessary failsafe, if we CREATE a label, it DOES NOT have the 'name' value set :/
      // So one DEFAULT EMPTY must be created in order for the validation to fail - so that something must be entered first before we validate
      this.value.ENG_text = this.value.ENG_text || '';

      for (const key in data) {
        if (key !== 'valid' && key !== 'changed') {
          // ---- Only <LANGUAGE_KEY>_text should be matched -- e.g. FR_text or EN_text
          if (key.match(/_text$/)) {
            if (typeof data[key] !== 'string' || data[key].length <= 0) valid = false;

            // eslint-disable-next-line eqeqeq
            if (this.oldValue[key] != data[key]) changed = true;
          }
        }
      }

      this.$emit('update:value', data);
      this.$emit('valid', valid);
      this.$emit('changed', changed);
    },
  },
};
</script>

<style scoped></style>
