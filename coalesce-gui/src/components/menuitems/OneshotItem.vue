<template>
  <div>
    <dialog-heading v-show="showHeading" title="Match a USSD request" icon="fa-tag"> </dialog-heading>

    <div class="form-group row">
      <div class="col-6 column-title">
        <p>Request To Match</p>
      </div>
    </div>

    <div class="form-group row">
      <div class="col-6">
        <input type="text" ref="match" class="form-control" v-model="value.match" @input="doValidate" />
      </div>
    </div>
  </div>
</template>

<script>
// eslint-disable-next-line import/no-unresolved
import DialogHeading from '@/components/menuitems/DialogHeading.vue';

// slide 91
export default {
  name: 'OneshotItem',
  components: {
    DialogHeading,
  },
  props: {
    value: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    showHeading() {
      return true;
    },
  },
  mounted() {
    // ---- Set the 'old' value - only happens on item load (once), is overwritten on each load (intentionally)
    this.oldValue = JSON.parse(JSON.stringify(this.value));
    if (!this.oldValue.match) this.oldValue.match = '';
  },

  methods: {
    doValidate() {
      // debugger
      let valid = true;
      let changed = false;

      // ---- This is a necessary failsafe, if we CREATE a label, it DOES NOT have the 'name' value set :/
      // So one DEFAULT EMPTY must be created in order for the validation to fail - so that something must be entered first before we validate
      if (!this.value.match) this.value.match = '';

      if (typeof this.value.match !== 'string' || this.value.match.length <= 0) valid = false;

      // eslint-disable-next-line eqeqeq
      if (this.value.match != this.oldValue.match) changed = true;

      this.$emit('valid', valid);
      this.$emit('changed', changed);
    },
  },
};
</script>

<style scoped>
.column-title {
  font-weight: bold;
  text-align: center;
}
</style>
