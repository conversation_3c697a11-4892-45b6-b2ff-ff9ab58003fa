<template>
  <div>
    <dialog-heading v-show="showHeading" title="Call Module" icon="fa-sitemap"> </dialog-heading>

    <div class="form-group row">
      <div class="col-6" v-if="menuModules.length > 0">
        <select class="form-control" ref="name" v-model="value.name" @input="doValidate">
          <option v-for="(mod, index) in menuModules" :value="mod.name" :key="index">{{ mod.name }}</option>
        </select>
      </div>
      <div class="col-6" v-else>
        No Module found
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

// eslint-disable-next-line import/no-unresolved
import DialogHeading from '@/components/menuitems/DialogHeading.vue';

// slide 91
export default {
  name: 'CallModuleItem',
  components: {
    DialogHeading,
  },
  props: {
    value: {
      type: Object,
      default() {
        return {};
      },
    },
    // eslint-disable-next-line vue/require-default-prop
    moduleId: Number,
  },
  data() {
    return {
      compList: Array,
      menuModules: [],
    };
  },
  computed: {
    ...mapGetters(['components', 'modules']),
    showHeading() {
      return true;
    },
  },
  async mounted() {
    await this.$store.dispatch('modules/list');
    // debugger;
    // ---- Set the 'old' value - only happens on item load (once), is overwritten on each load (intentionally)
    this.oldValue = JSON.parse(JSON.stringify(this.value));
    this.menuModules = this.modules.modules.filter(item => item.id !== this.moduleId);
  },

  methods: {
    // eslint-disable-next-line no-unused-vars
    doValidate(event) {
      // debugger
      let valid = true;
      let changed = false;

      // ---- This is a necessary failsafe, if we CREATE a label, it DOES NOT have the 'name' value set :/
      // So one DEFAULT EMPTY must be created in order for the validation to fail - so that something must be entered first before we validate

      if (typeof this.$refs.name.value !== 'string' || this.$refs.name.value.length <= 0) valid = false;

      // eslint-disable-next-line eqeqeq
      if (this.$refs.name.value != this.oldValue.name) {
        this.value.name = this.$refs.name.value;
        for (let i = 0; i < this.modules.modules.length; ++i) {
          if (this.modules.modules[i].name === this.value.name) {
            this.value.moduletype = this.modules.modules[i].type;
            break;
          }
        }
        changed = true;
      }

      this.$emit('valid', valid);
      this.$emit('changed', changed);
    },
  },
};
</script>

<style></style>
