<template>
  <div class="form-horizontal">
    <dialog-heading v-show="showHeading" title="Dynamic Menu" icon="fa-bars" @dialogClosed="dialogClosed"></dialog-heading>
    <form class="px-4 pb-4">
      <div class="form-group row ">
        <div class="form-check form-check-inline">
          <label for="bNumberCheck" class="text-left pt-2 col-form-label col">Offers are for 'B' number?</label>
          <input
            class="form-check-input mx-3"
            type="checkbox"
            id="bNumberCheck"
            aria-label="useIncomingSubscriberNumber"
            v-model="value.areOffersForBNumber"
            :checked="value.areOffersForBNumber"
            @change="emitUpdate()"
          />
        </div>
        <div class="col align-items-center">
          <input
            v-if="value.areOffersForBNumber"
            type="text"
            placeholder="'B' number ..."
            name="subscriberNumber"
            v-model="value.subscriberNumber"
            class="form-control"
            :class="{ 'is-invalid': !subscriberNumberIsValid }"
            aria-label="subscriberNumber"
            @change="emitUpdate()"
            :required="value.areOffersForBNumber"
            @blur="emitUpdate()"
          />
          <div class="invalid-feedback">
            Required, can only contain numbers, 5 or more digits
          </div>
        </div>
      </div>
      <div class="form-group row ">
        <label for="title" class="col-form-label col-sm-2">
          Title:
          <font-awesome-icon title="(only visible on first page)" icon="info-circle" class="ml-2 text-info"></font-awesome-icon>
        </label>
        <div class="col">
          <input name="title" class="form-control" placeholder="Enter USSD title here" type="text" v-model="value.title" @change="emitUpdate()" />
        </div>
      </div>

      <!-- eslint-disable-next-line vue/require-v-for-key -->
      <div class="form-group row " v-for="(item, key) in { header: 'Header', footer: 'Footer' }">
        <label :for="key" class="col-form-label col-sm-2 text-left">
          {{ item }}:
          <font-awesome-icon title="(visible on all pages)" icon="info-circle" class="ml-2 text-info"></font-awesome-icon>
        </label>
        <div class="col">
          <input :name="key" :placeholder="`Enter USSD ${key}`" v-model="value[key].text" class="form-control" type="text" @change="emitUpdate()" />
        </div>
      </div>

      <!-- eslint-disable-next-line vue/require-v-for-key -->
      <div
        class="form-group row"
        v-for="(item, key) in {
          next: { textLabel: 'Next', selectorLabel: 'Selector', help: 'Not visible on LAST page' },
          back: { textLabel: 'Back', selectorLabel: 'Selector', help: 'Not visible on FIRST page' },
        }"
      >
        <label :for="key" class="col-form-label col-sm-2 text-left">
          {{ item.textLabel }}:
          <font-awesome-icon :title="item.help" icon="info-circle" class="ml-2 text-info"></font-awesome-icon>
        </label>
        <div class="col form-inline">
          <label :for="key + '-selector'" class="px-2">Selector:</label>
          <input
            style="max-width: 4em"
            :name="key + '-selector'"
            v-model="value[key].selector"
            :class="{ 'is-invalid': value[key].selector.length === 0 }"
            class="form-control text-left py-1 pr-2"
            type="text"
            max="2"
            @change="emitUpdate()"
            required
          />
          <label :for="key + '-text'" class="px-2">Display Text:</label>
          <input
            :class="{ 'is-invalid': value[key].text.length === 0 }"
            :name="key + '-text'"
            v-model="value[key].text"
            class="form-control"
            type="text"
            @change="emitUpdate()"
            required
          />
          <div class="invalid-feedback">
            Selector and Display Text are required.
          </div>
        </div>
      </div>

      <div class="form-group row d-flex align-items-center">
        <label for="menu-style" class="col-form-label col-sm-2">Menu Style:</label>
        <div class="col">
          <b-dropdown variant="dark" id="menuStyleDropdown" :text="chosenMenuStyle" size="sm" class="m-md-2">
            <b-dropdown-item
              v-for="item in menuStyles"
              :key="item.id"
              :value="item.name"
              @click="
                value.menuStyle = item.id;
                emitUpdate();
              "
            >
              {{ item.name }}
            </b-dropdown-item>
            <!--b-dropdown-divider></b-dropdown-divider>
            <b-dropdown-item active>Active action</b-dropdown-item>
            <b-dropdown-item disabled>Disabled action</b-dropdown-item-->
          </b-dropdown>
        </div>
      </div>

      <label for="tags" class="col-form-label col-sm-2 mb-2">Offer Filtering:</label>

      <div class="form-group row d-flex align-items-center">
        <label for="tags" class="col-form-label col-sm-2">Tags:</label>
        <div class="col">
          <b-form-tags input-id="tags" v-model="value.tags" @input="emitUpdate()"></b-form-tags>
        </div>
      </div>

      <div class="form-group row d-flex align-items-center">
        <label for="tags" class="col-form-label col-sm-2">Properties:</label>
        <div class="col">
          <input v-if="!hasProperties" name="dummy" title="" disableAddButton placeholder="None" disabled class="form-control" type="text" />
          <div v-else class="form-inline">
            <div v-for="(tag, key) in value.properties" class="bg-info text-light px-3 mx-1 py-1 border rounded-pill" :key="key">
              {{ key }}: {{ tag }}
              <font-awesome-icon
                icon="times"
                class="ml-2 text-light cursor-pointer"
                @click="
                  removeProperty(key);
                  emitUpdate();
                "
              ></font-awesome-icon>
            </div>
          </div>
        </div>
      </div>
    </form>
    <form>
      <div class="form-group row d-flex align-items-center">
        <label for="tags" class="col-form-label col-sm-2">New Property:</label>
        <div class="col-4">
          <input
            name="newKey"
            placeholder="Enter a property key"
            class="form-control"
            :class="{ 'is-invalid': !!value.properties[newProperty.key] }"
            type="text"
            v-model="newProperty.key"
          />
          <div class="invalid-feedback">
            This key already exists
          </div>
        </div>
        <div class="col-4">
          <input name="newValue" placeholder="Enter a property value" class="form-control" type="text" v-model="newProperty.value" />
        </div>
        <div class="col-1">
          <b-button
            :title="!newPropertyIsValid ? 'Enter a property key and value' : ''"
            :disabled="!newPropertyIsValid"
            variant="success"
            @click="
              addProperty();
              emitUpdate();
            "
          >
            Add
          </b-button>
        </div>
      </div>
      <!-- -->
    </form>
  </div>
</template>

<script>
// slide 70
/* eslint-disable import/no-unresolved */
import DialogHeading from '@/components/menuitems/DialogHeading.vue';

export default {
  name: 'DynamicMenuItem',
  components: {
    DialogHeading,
  },
  props: {
    value: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      wasValidated: false,
      isDialogOpen: true,
      menuStyles: [
        //
        { id: 'default', name: 'Default' },
      ],
      newProperty: {
        key: null,
        value: null,
      },
    };
  },
  computed: {
    subscriberNumberIsValid() {
      return /^\d{5,}$/.test(String(this.value.subscriberNumber));
    },
    hasProperties() {
      const { properties } = this.value || {};
      const has = Object.values(properties).length > 0;
      return has;
    },
    newPropertyIsValid() {
      const { key, value } = this.newProperty;
      return key && value && !this.value.properties[key];
    },
    showHeading() {
      return true;
    },
    chosenMenuStyle() {
      return this.menuStyles.find(item => item.id === this.value.menuStyle).name;
    },
  },
  mounted() {
    // ---- Set the 'old' value - only happens on item load (once), is overwritten on each load (intentionally)
    this.oldValue = JSON.parse(JSON.stringify(this.value));
  },
  methods: {
    _clearSubscriberNumberIfBNumberNotChecked() {
      if (!this.value.areOffersForBNumber) {
        this.value.subscriberNumber = '';
      }
    },
    removeProperty(key) {
      delete this.value.properties[key];
      // for VUE to be reactive... we update the full properties value
      this.value.properties = { ...(this.value || {}).properties };
    },
    addProperty() {
      // for VUE to be reactive... we update the full properties value
      this.value.properties = {
        ...(this.value || {}).properties,
        [this.newProperty.key]: this.newProperty.value,
      };
      this.newProperty.key = null;
      this.newProperty.value = null;
    },
    dialogClosed() {
      this.isDialogOpen = false;
    },
    emitUpdate() {
      this.wasValidated = true;
      const { oldValue, value } = this;

      let valid = true;
      let changed = false;

      this._clearSubscriberNumberIfBNumberNotChecked();

      if (
        oldValue.areOffersForBNumber !== value.areOffersForBNumber ||
        oldValue.subscriberNumber !== value.subscriberNumber ||
        oldValue.title !== value.title ||
        oldValue.header.text !== value.header.text ||
        oldValue.footer.text !== value.footer.text ||
        oldValue.next.selector !== value.next.selector ||
        oldValue.back.selector !== value.back.selector ||
        oldValue.next.text !== value.next.text ||
        oldValue.back.text !== value.back.text ||
        oldValue.menuStyle !== value.menuStyle ||
        JSON.stringify(oldValue.tags.sort()) !== JSON.stringify(value.tags.sort()) ||
        JSON.stringify(Object.entries(oldValue.properties).sort()) !== JSON.stringify(Object.entries(value.properties).sort())
      ) {
        changed = true;
      }

      if (value.areOffersForBNumber && value.subscriberNumber.length === 0) {
        valid = false;
      }

      if (
        this.value.back.selector.length === 0 ||
        this.value.back.text.length === 0 ||
        this.value.next.selector.length === 0 ||
        this.value.next.text.length === 0
      ) {
        valid = false;
      }

      if (value.areOffersForBNumber && !this.subscriberNumberIsValid) {
        valid = false;
      }

      this.$emit('update:value', value);
      this.$emit('valid', valid);
      this.$emit('changed', changed);
    },
  },
};
</script>

<style scoped></style>
