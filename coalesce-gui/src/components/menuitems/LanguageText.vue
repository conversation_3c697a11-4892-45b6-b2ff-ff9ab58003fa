<template>
  <div>
    <div class="form-group row" v-for="{ name, code, title, text, id } in langs" :key="id">
      <label for="lang" class="col-sm-2 col-form-label lang-label">{{ title }}</label>
      <div class="col-sm-10">
        <!-- <textarea type="text" v-autosize ref="langs" class="form-control" :name="name" :value="text" @input="updateText"/> -->
        <input type="text" ref="langs" class="form-control" :name="name" :value="text" @input="updateText" />
      </div>
    </div>
  </div>
</template>

<script>
// slide 70
// import ResizableTextArea from "@/components/ResizableTextArea.vue";
import { mapGetters } from 'vuex';

export default {
  name: 'LanguageText',
  props: {
    value: {
      type: Object,
      default() {
        return {};
      },
    },
    isDialogOpen: {
      type: <PERSON>olean,
      // default: null
    },
  },
  data() {
    return {};
  },
  computed: {
    ...mapGetters(['langs']),
  },
  watch: {
    isDialogOpen(/* newVal, oldVal */) {
      // watch it
      //   console.log('Prop changed: ', newVal, ' | was: ', oldVal)
      if (!this.isDialogOpen) {
        this.addNewlinesToLanguages();
      }
    },
  },
  mounted() {
    this.stripNewlinesFromLanguages();

    this.$refs.langs.forEach((lang, i) => {
      if (this.value && typeof this.value[lang.name] !== 'undefined') {
        this.$refs.langs[i].value = this.value[lang.name];
      } else {
        this.$refs.langs[i].value = '';
      }
    });
  },
  methods: {
    addNewlinesToLanguages() {
      Object.keys(this.value).forEach(item => {
        if (item.match(/_text$/) && !this.value[item].endsWith('\\n')) {
          this.value[item] = `${this.value[item]}\\n`;
        }
      });
    },
    stripNewlinesFromLanguages() {
      Object.keys(this.value).forEach(item => {
        if (item.match(/_text$/) && this.value[item].endsWith('\\n')) {
          this.value[item] = this.value[item].slice(0, -2);
        }
      });
    },
    updateText() {
      // debugger;
      const data = this.value;

      for (let i = 0; i < this.$refs.langs.length; i++) {
        const lang = this.$refs.langs[i];
        // if(lang.value.endsWith("\\n")) lang.value = lang.value.slice(0, -2)
        data[lang.name] = `${lang.value}\\n`;
      }
      // alert(JSON.stringify(data, null, 3));
      this.$emit('input', data);
      this.$emit('change', data);
    },
  },
};
</script>

<style scoped>
.lang-label {
  text-align: right;
}
</style>
