<template>
  <div>
    <dialog-heading title="Add menu item to display on subscriber handset" icon="fa-align-justify" @dialogClosed="dialogClosed"></dialog-heading>

    <div class="row">
      <div class="col-12">
        <div class="form-group row">
          <label for="selector" class="col-sm-2 col-form-label item-label">Item ID</label>
          <div class="col-sm-4">
            <input ref="selector" type="text" class="form-control" v-model="value.selector" @input="doValidate" />
          </div>
        </div>
        <div class="form-group row lang-title mb-3">
          <p class="col-sm-2 text-alignment-h1 text-right pr-4">Lang.</p>
          <p class="offset-sm-5 col-sm-4 text-alignment-h2">Menu item text to display</p>
        </div>

        <language-text :is-dialog-open="isDialogOpen" v-model="value" @input="doValidate"></language-text>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved */

import { mapGetters } from 'vuex';
// slide 70
import LanguageText from '@/components/menuitems/LanguageText.vue';
import DialogHeading from '@/components/menuitems/DialogHeading.vue';

export default {
  name: 'MenuEntryItem',
  components: {
    LanguageText,
    DialogHeading,
  },
  props: {
    value: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      isDialogOpen: true,
    };
  },
  computed: {
    ...mapGetters(['langs']),
  },
  mounted() {
    // ---- Set the 'old' value - only happens on item load (once), is overwritten on each load (intentionally)
    this.oldValue = JSON.parse(JSON.stringify(this.value));
    this.oldValue.selector = this.value.selector;
  },

  methods: {
    dialogClosed() {
      this.isDialogOpen = false;
    },
    doValidate() {
      // debugger
      let valid = true;
      let changed = false;

      // ---- This is a necessary failsafe, if we CREATE a menuEntryItem, it DOES NOT have any text values :/
      // So one DEFAULT EMPTY must be created in order for the validation to fail - so that something must be entered first before we validate
      this.value.ENG_text = this.value.ENG_text || '';

      for (const key in this.value) {
        if (key !== 'valid' && key !== 'changed') {
          // ---- Only <LANGUAGE_KEY>_text should be matched -- e.g. FR_text or EN_text
          if (key.match(/_text$/) || key === 'selector') {
            if (typeof this.value[key] !== 'string' || this.value[key].length <= 0) valid = false;

            // eslint-disable-next-line eqeqeq
            if (this.oldValue[key] != this.value[key]) changed = true;
          }
        }
      }
      this.$emit('valid', valid);
      this.$emit('changed', changed);
    },
  },
};
</script>

<style scoped>
.item-label {
  text-align: right;
}

.lang-title {
  margin: 10px 0 0 0;
  padding: 0;
  font-weight: bold;
}

.text-alignment-h1 {
  margin: 0;
  padding: 0;
  text-align: center;
}

.text-alignment-h2 {
  margin: 0 0 0 0;
}
</style>
