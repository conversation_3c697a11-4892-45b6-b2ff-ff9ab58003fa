<template>
  <div>
    <dialog-heading title="Select Goto destination" icon="fa-flag"> </dialog-heading>

    <div class="form-group row">
      <div class="col-12">
        <div class="form-check form-check-inline">
          <input class="form-check-input" type="radio" ref="location" v-model="value.location" :value="'previous'" @change="doValidate" />
          <label class="form-check-label">Previous Menu</label>
        </div>
        <div class="form-check form-check-inline">
          <input class="form-check-input" type="radio" ref="location" v-model="value.location" :value="'label'" @change="doValidate" />
          <label class="form-check-label">Label</label>
        </div>
      </div>
    </div>
    <div class="form-group row">
      <div class="col-12">
        <select class="form-control" ref="target" v-model="value.target" @change="doValidate" :disabled="value.location !== 'label'">
          <option v-for="(label, index) in treelabels" :key="index">{{ label }}</option>
        </select>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

// eslint-disable-next-line import/no-unresolved
import DialogHeading from '@/components/menuitems/DialogHeading.vue';

// slide
export default {
  name: 'GotoItem',
  components: {
    DialogHeading,
  },
  props: {
    value: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    ...mapGetters(['treelabels']),
  },
  mounted() {
    // debugger
  },
  methods: {
    // eslint-disable-next-line no-unused-vars
    doValidate(event) {
      let valid = false;
      const changed = true;
      // debugger
      if ((this.value.location === 'label' && typeof this.value.target === 'string') || this.value.location === 'previous') {
        valid = true;
      }
      // debugger
      this.$emit('valid', valid);
      this.$emit('changed', changed);
    },
  },
};
</script>

<style></style>
