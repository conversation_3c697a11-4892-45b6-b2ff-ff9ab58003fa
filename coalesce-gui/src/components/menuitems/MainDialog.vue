<template>
  <div class="menuplus-content container-fluid" ref="containerdialog">
    <form @submit.prevent="handleSubmit">
      <dynamic-menu-item
        ref="editdialog"
        v-if="type === 'dynamic-menu'"
        :value.sync="value"
        @valid="isValid"
        @changed="isChanged"
      ></dynamic-menu-item>
      <display-item ref="editdialog" v-if="type === 'display'" :value.sync="value" @valid="isValid" @changed="isChanged"></display-item>
      <menu-name-item ref="editdialog" v-if="type === 'menu'" :value.sync="value" @valid="isValid" @changed="isChanged"></menu-name-item>
      <menu-entry-item ref="editdialog" v-if="type === 'item'" :value.sync="value" @valid="isValid" @changed="isChanged"></menu-entry-item>

      <label-item ref="editdialog" v-if="type === 'label'" :value.sync="value" @valid="isValid" @changed="isChanged"></label-item>
      <goto-item ref="editdialog" v-if="type === 'goto'" :value.sync="value" @valid="isValid" @changed="isChanged"></goto-item>
      <ask-item ref="editdialog" v-if="type === 'ask'" :value.sync="value" @valid="isValid" @changed="isChanged"></ask-item>
      <call-component-item
        ref="editdialog"
        v-if="type === 'component'"
        :value.sync="value"
        @valid="isValid"
        @changed="isChanged"
      ></call-component-item>
      <call-module-item
        ref="editdialog"
        v-if="type === 'menuModule'"
        :module-id="moduleId"
        :value.sync="value"
        @valid="isValid"
        @changed="isChanged"
      ></call-module-item>
      <assign-item ref="editdialog" v-if="type === 'assign'" :value.sync="value" @valid="isValid" @changed="isChanged"></assign-item>
      <oneshot-item ref="editdialog" v-if="type === 'oneshot'" :value.sync="value" @valid="isValid" @changed="isChanged"></oneshot-item>
      <ifexp-item ref="editdialog" v-if="type === 'ifexp'" :value.sync="value" @valid="isValid" @changed="isChanged"></ifexp-item>

      <div class="row">
        <div class="col-12">
          <div class="form-group row">
            <div class="col-sm-10 submit-button">
              <button
                type="submit"
                class="btn"
                :disabled="!valid || !hasChanged"
                :class="{ 'btn-outline-dark disabled': !valid || !hasChanged, 'btn-success': valid && hasChanged }"
              >
                Submit
              </button>
              <!-- WIP
                    <div v-show="warning_msg != ''" style="position:absolute;" class="alert alert-warning">
                        {{ warning_msg }}
                    </div>
-->
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved */
// http://astronautweb.co/snippet/font-awesome/

import { mapGetters } from 'vuex';
import DynamicMenuItem from '@/components/menuitems/DynamicMenuItem.vue';
import DisplayItem from '@/components/menuitems/DisplayItem.vue';
import MenuNameItem from '@/components/menuitems/MenuNameItem.vue';
import MenuEntryItem from '@/components/menuitems/MenuEntryItem.vue';
import LabelItem from '@/components/menuitems/LabelItem.vue';
import GotoItem from '@/components/menuitems/GotoItem.vue';
import AskItem from '@/components/menuitems/AskItem.vue';
import CallComponentItem from '@/components/menuitems/CallComponentItem.vue';
import AssignItem from '@/components/menuitems/AssignItem.vue';
import OneshotItem from '@/components/menuitems/OneshotItem.vue';
import IfexpItem from '@/components/menuitems/IfexpItem.vue';
import CallModuleItem from '@/components/menuitems/callModuleItem.vue';

export default {
  name: 'MainDialog',
  components: {
    DynamicMenuItem,
    DisplayItem,
    MenuNameItem,
    MenuEntryItem,
    LabelItem,
    GotoItem,
    AskItem,
    CallComponentItem,
    AssignItem,
    OneshotItem,
    IfexpItem,
    CallModuleItem,
  },
  props: {
    // type: {
    //      type: String,
    //      default: 'display'
    //   }
    type: {
      type: String,
      default: () => '',
    },
    value: {
      type: Object,
      default: () => {
        return {};
      },
    },
    mode: {
      type: String,
      default: () => '',
    },
  },
  data() {
    return {
      hasChanged: false,
      itemId: -1,
      valid: true,
      changed: false,
      moduleId: null,
      // warning_msg: ""
    };
  },
  computed: {
    ...mapGetters(['treecontext', 'modules']),
  },

  mounted() {
    // eslint-disable-next-line radix
    // debugger
    this.moduleId = parseInt(this.$route.params.moduleId, 10);
    this.$nextTick(() => {
      this.$refs.editdialog.$el.scrollIntoView({
        behavior: 'auto', // smooth or auto
        block: 'center', // start center end or nearest
        inline: 'nearest', // start center end or nearest
      });
    });
    // debugger
  },
  methods: {
    handleSubmit() {
      if (this.type === 'item') {
        this.addNewlinesToLanguages();
      }
      // debugger
      // alert('Submitting '+JSON.stringify(this.value, null, 3));
      if (this.treecontext.mode === 'add') {
        this.$emit('menuadd', this.type);
      } else if (this.treecontext.mode === 'edit') {
        this.$emit('menuedit', this.item, this.type, this.itemId);
      } else {
        this.$emit('menudata', this.value);
      }
      this.$store.dispatch('menutree/closedialog');
    },
    addNewlinesToLanguages() {
      Object.keys(this.value).forEach(key => {
        const value = this.value[key];
        if (key.endsWith('_text') && !value.endsWith('\\n')) {
          this.value[key] = `${value}\\n`;
        }
      });
    },
    isChanged(status) {
      this.changed = status;
      if (status === true) this.hasChanged = true;
    },
    isValid(status) {
      this.valid = status;
    },
  },
};
</script>

<style></style>
