<template>
  <div>
    <dialog-heading v-show="showHeading" title="Assign a value to a variable" icon="fa-tag"> </dialog-heading>

    <div class="form-group row">
      <div class="col-6 column-title">
        <p>Variable Name</p>
      </div>

      <div class="col-6 column-title">
        <p>Value</p>
      </div>
    </div>

    <div class="form-group row">
      <div class="col-6">
        <input type="text" ref="variable" class="form-control" v-model="value.variable" @input="doValidate" />
      </div>

      <div class="col-6">
        <input type="text" ref="value" class="form-control" v-model="value.value" @input="doValidate" />
      </div>
    </div>
  </div>
</template>

<script>
// eslint-disable-next-line import/no-unresolved
import DialogHeading from '@/components/menuitems/DialogHeading.vue';

// slide 91
export default {
  name: 'AssignItem',
  components: {
    DialogHeading,
  },
  props: {
    value: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    showHeading() {
      return true;
    },
  },
  mounted() {
    // ---- Set the 'old' value - only happens on item load (once), is overwritten on each load (intentionally)
    this.oldValue = JSON.parse(JSON.stringify(this.value));
    if (!this.oldValue.variable) this.oldValue.variable = '';
    if (!this.oldValue.value) this.oldValue.value = '';
  },

  methods: {
    // eslint-disable-next-line no-unused-vars
    doValidate(event) {
      // debugger
      let valid = true;
      let changed = false;

      // ---- This is a necessary failsafe, if we CREATE a label, it DOES NOT have the 'name' value set :/
      // So one DEFAULT EMPTY must be created in order for the validation to fail - so that something must be entered first before we validate
      if (!this.value.variable) this.value.variable = '';
      if (!this.value.value) this.value.value = '';

      if (typeof this.value.variable !== 'string' || this.value.variable.length <= 0) valid = false;

      if (typeof this.value.value !== 'string' || this.value.value.length <= 0) valid = false;

      // eslint-disable-next-line eqeqeq
      if (this.value.variable != this.oldValue.variable || this.value.value != this.oldValue.value) changed = true;

      this.$emit('valid', valid);
      this.$emit('changed', changed);
    },
  },
};
</script>

<style scoped>
.column-title {
  font-weight: bold;
  text-align: center;
}
</style>
