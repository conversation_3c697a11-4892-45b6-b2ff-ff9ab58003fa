<template>
  <div>
    <dialog-heading title="If configuration" icon="fa-flag"> </dialog-heading>

    <div class="form-group row">
      <div class="col-4 center">
        Variable
      </div>
      <div class="col-2 center">
        Operator
      </div>
      <div class="col-6 center">
        Value
      </div>
    </div>

    <div class="form-group row">
      <div class="col-5">
        <input type="text" ref="variable" class="form-control" v-model="value.variable" @input="doValidate" />
      </div>
      <div class="col-2">
        <select class="form-control" ref="target" v-model="value.operator" @change="doValidate">
          <option value="gt">&gt;</option>
          <option value="ge">&gt;=</option>
          <option value="lt">&lt;</option>
          <option value="le">&lt;=</option>
          <option value="eq">==</option>
          <option value="ne">!=</option>
        </select>
      </div>
      <div class="col-5">
        <input type="text" ref="constant" class="form-control" v-model="value.value" @input="doValidate" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

// eslint-disable-next-line import/no-unresolved
import DialogHeading from '@/components/menuitems/DialogHeading.vue';

// slide
export default {
  name: 'IfexpItem',
  components: {
    DialogHeading,
  },
  props: {
    value: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    ...mapGetters(['treelabels']),
  },
  mounted() {
    // debugger;
    // ---- Set the 'old' value - only happens on item load (once), is overwritten on each load (intentionally)
    this.oldValue = JSON.parse(JSON.stringify(this.value));
    if (!this.oldValue.variable) this.oldValue.variable = '';
    if (!this.oldValue.value) this.oldValue.value = '';
    if (!this.oldValue.operator) this.oldValue.operator = 'gt';
  },
  methods: {
    doValidate() {
      let valid = false;
      let changed = false;
      // debugger
      if (this.value.variable !== this.oldValue.variable) changed = true;
      if (this.value.value !== this.oldValue.value) changed = true;
      if (this.value.operator !== this.oldValue.operator) changed = true;

      if (typeof this.value.variable === 'string' && typeof this.value.operator === 'string') {
        valid = true;
      }
      // debugger
      this.$emit('valid', valid);
      this.$emit('changed', changed);
    },
  },
};
</script>

<style scoped>
.center {
  text-align: center;
}
</style>
