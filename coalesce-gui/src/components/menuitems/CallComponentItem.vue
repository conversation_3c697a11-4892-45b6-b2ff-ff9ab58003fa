<template>
  <div>
    <dialog-heading v-show="showHeading" title="Call Component" icon="fa-laptop-code"> </dialog-heading>

    <div class="form-group row">
      <div class="col-6">
        <select class="form-control" ref="name" v-model="value.name" @input="doValidate">
          <option v-for="(comp, index) in components" :value="comp.name" :key="index">{{ comp.name }}</option>
        </select>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

// eslint-disable-next-line import/no-unresolved
import DialogHeading from '@/components/menuitems/DialogHeading.vue';

// slide 91
export default {
  name: 'CallComponentItem',
  components: {
    DialogHeading,
  },
  props: {
    value: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      compList: Array,
    };
  },
  computed: {
    ...mapGetters(['components']),
    showHeading() {
      return true;
    },
  },
  async mounted() {
    await this.$store.dispatch('component/list');
    // debugger;
    // ---- Set the 'old' value - only happens on item load (once), is overwritten on each load (intentionally)
    this.oldValue = JSON.parse(JSON.stringify(this.value));
  },

  methods: {
    // eslint-disable-next-line no-unused-vars
    doValidate(event) {
      // debugger
      let valid = true;
      let changed = false;

      // ---- This is a necessary failsafe, if we CREATE a label, it DOES NOT have the 'name' value set :/
      // So one DEFAULT EMPTY must be created in order for the validation to fail - so that something must be entered first before we validate

      if (typeof this.$refs.name.value !== 'string' || this.$refs.name.value.length <= 0) valid = false;

      // eslint-disable-next-line eqeqeq
      if (this.$refs.name.value != this.oldValue.name) {
        this.value.name = this.$refs.name.value;
        for (let i = 0; i < this.components.length; ++i) {
          if (this.components[i].name === this.value.name) {
            this.value.comptype = this.components[i].type;
            break;
          }
        }
        changed = true;
      }

      this.$emit('valid', valid);
      this.$emit('changed', changed);
    },
  },
};
</script>

<style></style>
