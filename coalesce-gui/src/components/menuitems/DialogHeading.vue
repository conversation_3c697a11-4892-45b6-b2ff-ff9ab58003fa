<template>
  <div class="row">
    <div class="d-flex w-100 justify-content-between m-3 mb-4 pb-3">
      <div class="d-flex align-items-center">
        <font-awesome-icon class="mx-3" size="2x" :icon="String(icon).replace(/fa-/, '')"></font-awesome-icon>
        <span class="dialog-title">{{ title }}</span>
      </div>
      <span class="mx-3 cursor-pointer">
        <!-- Do not change this from a button.  It is a workaround to fix a framework issue.
              If you change it, the ability to close dialogs without change when adding new items
              to the menu tree will be impaired for large menus only.
        -->
        <b-button @click="handleClose" variant="outline-light">
          <font-awesome-icon size="2x" icon="times"></font-awesome-icon>
        </b-button>
      </span>
    </div>
  </div>
</template>

<script>
/* eslint-disable vue/require-default-prop */

export default {
  name: 'DialogHeading',
  props: {
    title: String,
    icon: String,
    value: Object,
    // closeDialog is a function defined in the parent components of this comp (1- DisplayItem 2-MenuEntryItem)
    // and it will just set a variable there to false (indicating modal is closed)
    // Removed this as it is bad coding.  If we want to call a parent function, we raise an event.
    // closeDialog: Function,
  },
  data() {
    return {
      scrollYPosition: 0,
    };
  },
  methods: {
    handleClose() {
      this.scrollYPosition = window.scrollY;

      this.$emit('dialogClosed');
      this.$store.dispatch('menutree/closedialog');
      window.MenuPlus.discardChanges(this.$parent.$parent.value, this.$parent.$parent.mode);

      setTimeout(() => {
        window.scrollTo(this.scrollYPosition, this.scrollYPosition);
      }, 100);
    },
  },
};
</script>

<style scoped>
.dialog-heading {
  display: table;
  margin: 10px 0 30px 30px;
}

.dialog-title {
  font-size: 1.5rem;
  font-size: 2.2rem;
  display: table-cell;
  vertical-align: middle;
}

.dialog-close {
  font-size: 2.2rem;
}

.btn-outline-light {
  color: #2d3135;
}
</style>
