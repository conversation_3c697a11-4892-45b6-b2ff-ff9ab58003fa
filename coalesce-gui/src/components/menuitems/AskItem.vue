<template>
  <div>
    <dialog-heading v-show="showHeading" title="Details to display on subscribers handset" icon="fa-mobile-alt" @dialogClosed="dialogClosed">
    </dialog-heading>

    <div class="form-group row">
      <label for="variable" class="col-sm-1 col-form-label lang-label">Variable</label>
      <div class="col-sm-4">
        <input type="text" ref="variable" class="form-control" v-model="value.variable" />
      </div>
    </div>

    <div class="form-group row">
      <label for="question" class="offset-sm-1 col-sm-1 col-form-label lang-label">Question</label>
    </div>
    <div class="row">
      <div class="col-12">
        <language-text :is-dialog-open="isDialogOpen" ref="langs" :value="value" @input="updateText"> </language-text>
      </div>
    </div>
  </div>
</template>

<script>
// slide 70
/* eslint-disable import/no-unresolved */
import LanguageText from '@/components/menuitems/LanguageText.vue';
import DialogHeading from '@/components/menuitems/DialogHeading.vue';

export default {
  name: 'AskItem',
  components: {
    LanguageText,
    DialogHeading,
  },
  props: {
    value: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      isDialogOpen: true,
    };
  },
  computed: {
    showHeading() {
      return true;
    },
  },

  mounted() {
    // ---- Set the 'old' value - only happens on item load (once), is overwritten on each load (intentionally)
    this.oldValue = JSON.parse(JSON.stringify(this.value));
  },

  methods: {
    dialogClosed() {
      this.isDialogOpen = false;
    },
    updateText(event) {
      // debugger
      let valid = true;
      let changed = false;

      // ---- This is a necessary failsafe, if we CREATE a label, it DOES NOT have the 'name' value set :/
      // So one DEFAULT EMPTY must be created in order for the validation to fail - so that something must be entered first before we validate
      this.value.ENG_text = this.value.ENG_text || '';

      for (const key in event) {
        if (key !== 'valid' && key !== 'changed') {
          // ---- Only <LANGUAGE_KEY>_text should be matched -- e.g. FR_text or EN_text
          if (key.match(/_text$/)) {
            if (typeof event[key] !== 'string' || event[key].length <= 0) valid = false;

            // eslint-disable-next-line eqeqeq
            if (this.oldValue[key] != event[key]) changed = true;
          }
        }
      }

      this.$emit('update:value', event);
      this.$emit('valid', valid);
      this.$emit('changed', changed);
    },
  },
};
</script>

<style scoped></style>
