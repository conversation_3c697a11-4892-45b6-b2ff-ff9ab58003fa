<template>
  <div>
    <dialog-heading title="Enter menu name" icon="fa-sitemap"> </dialog-heading>

    <div class="row">
      <div class="col-12">
        <language-text :value="value" @input="updateText"> </language-text>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved */
import LanguageText from '@/components/menuitems/LanguageText.vue';
import DialogHeading from '@/components/menuitems/DialogHeading.vue';

export default {
  name: 'MenuNameItem',
  components: {
    LanguageText,
    DialogHeading,
  },
  props: {
    value: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  mounted() {
    // ---- Set the 'old' value - only happens on item load (once), is overwritten on each load (intentionally)
    this.oldValue = JSON.parse(JSON.stringify(this.value));
  },
  methods: {
    updateText(event) {
      // debugger

      let valid = true;
      let changed = false;

      // ---- This is a necessary failsafe, if we CREATE a label, it DOES NOT have the 'name' value set :/
      // So one DEFAULT EMPTY must be created in order for the validation to fail - so that something must be entered first before we validate
      this.value.ENG_text = this.value.ENG_text || '';

      for (const key in event) {
        if (key !== 'valid' && key !== 'changed') {
          // ---- Only <LANGUAGE_KEY>_text should be matched -- e.g. FR_text or EN_text
          if (key.match(/_text$/)) {
            if (typeof event[key] !== 'string' || event[key].length <= 0) valid = false;

            // eslint-disable-next-line eqeqeq
            if (this.oldValue[key] != event[key]) changed = true;
          }
        }
      }

      this.$emit('update:value', event);
      this.$emit('valid', valid);
      this.$emit('changed', changed);
    },
  },
};
</script>

<style></style>
