<template>
  <div id="app">
    <first-time-settings-modal v-if="openFirstTimeSettings"></first-time-settings-modal>
    <lockout-manager :is-idle="isAppIdle" :idle-in-seconds="idleSeconds"></lockout-manager>
    <router-view />
  </div>
</template>

<script>
/* eslint-disable import/no-unresolved */
import { mapGetters } from 'vuex';
import lockoutManager from '@/components/LockoutManager'; // get token from cookie
import PermissionManager from '@/utils/PermissionManager';
import firstTimeSettingsModal from './views/globalSettings/firstTimeSettingsModal.vue';

export default {
  name: 'App',
  components: {
    lockoutManager,
    firstTimeSettingsModal,
  },
  // eslint-disable-next-line vue/require-prop-types
  props: ['idleSeconds'],
  data() {
    return {
      openFirstTimeSettings: false,
    };
  },
  computed: {
    ...mapGetters(['user', 'token', 'tokenExpiry', 'userPermissions', 'globalSettings']),
  },
  watch: {
    /*
    In order to check if the modal changes ... BOTH user and globalSettings need to change
    */
    user: {
      deep: true,
      handler() {
        this.checkFirstTimeSettings();
      },
    },
    globalSettings: {
      deep: true,
      handler() {
        this.checkFirstTimeSettings();
      },
    },
  },
  mounted() {
    console.debug('Mounted App');
    this.$store.dispatch('user/getInfo').then(() => {
      this.checkFirstTimeSettings();
    });
  },
  methods: {
    checkFirstTimeSettings() {
      if (this.user.email) {
        const pm = new PermissionManager(this.userPermissions);
        if (
          pm.hasAll([
            { can: 'edit-general-settings', forGroup: 'globalsettings' },
            { can: 'list-general-settings', forGroup: 'globalsettings' },
          ])
        ) {
          if (this.globalSettings.generalSettings) {
            this.globalSettings.generalSettings.forEach(setting => {
              if (setting.name.match(/web.*hostname/i) && String(setting.value).length === 0) {
                this.openFirstTimeSettings = true;
              }
            });
          }
        }
      }
    },
  },
};
</script>

<style>
*[class*='-container'],
*[class*='-container'] * {
  transition-duration: 0.2s;
  transition-property: all;
}

.switch {
  position: relative;
  display: inline-block;
  width: 100px;
  height: 34px;
}

/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: '';
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: #39b546;
}

input:focus + .slider {
  box-shadow: 0 0 1px #39b546;
}

input:checked + .slider:before {
  -webkit-transform: translateX(66px);
  -ms-transform: translateX(66px);
  transform: translateX(66px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

h5.cs-heading {
  color: #39b546;
}
.btn-primary.cs-btn {
  background-color: #39b546;
  border-color: #39b546;
  background: #39b546 linear-gradient(180deg, #39b546, #39b546) repeat-x;
}

.btn-primary.cs-btn:disabled {
  background-color: #a0a0a0;
  border-color: #a0a0a0;
  background: #a0a0a0 linear-gradient(180deg, #a0a0a0, #a0a0a0) repeat-x;
  box-shadow: none;
}

.btn-primary.cs-btn:not(:disabled):focus,
.btn-primary.cs-btn:not(:disabled):active:focus,
.btn-primary.cs-btn:not(:disabled):hover {
  background-color: #33a23f;
  border-color: #33a23f;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0.2rem rgba(51, 162, 63, 0.5);
}

.btn-primary.cs-btn:not(:disabled):not(.disabled):active,
.btn-primary.cs-btn:not(:disabled):not(.disabled).active,
.show > .btn-primary.cs-btn.dropdown-toggle {
  color: #fff;
  background-color: #39b546;
  background-image: none;
  border-color: #39b546;
}

.form-group .form-control:focus {
  border-color: #a0a0a0;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(160, 160, 160, 0.6);
}

.form-group .form-control.dirty-valid:focus {
  border-color: #39b546;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(57, 181, 74, 0.6);
}

.form-group--error .form-control:focus {
  border-color: #ff0000;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(255, 0, 0, 0.6);
}

.form-group--error .form-group__message,
.form-group--error .error {
  display: block;
  color: #ff0000;
}

.form-group__message,
.error {
  font-size: 0.85rem;
  line-height: 1;
  display: none;
  margin-left: 14px;
  margin-top: 6px;
}

.form-group input.dirty-valid,
.form-group textarea.dirty-valid,
.form-group input.dirty-valid:focus,
.form-group input.dirty-valid:hover {
  border-color: #39b546;
  color: #39b546;
}

.form-group--error input,
.form-group--error textarea,
.form-group--error input:focus,
.form-group--error input:hover {
  border-color: #ff0000;
  color: #ff0000;
}

.form-group--alert,
.form-group--error {
  animation-name: shakeError;
  animation-fill-mode: forward;
  animation-duration: 0.6s;
  animation-timing-function: ease-in-out;
}

.col-form-label {
  font-weight: bold;
}

.badge.cs-msg-label {
  font-size: 0.9em;
  padding: 8px 20px;
}
</style>
