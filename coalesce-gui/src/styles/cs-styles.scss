$coalesce_green: #39b54a;
$cs_color_light: #e8e9ea;
$cs_color_medium: #b8b9ba;
$cs_color_default: #787f84;
$cs_color_default_dark: #4c5154;

$cs_color_primary: $coalesce_green;
$cs_color_info: #3d80c8;
$cs_color_danger: #dc3545;
$cs_color_warning: #a4320c;
$cs_color_warning_bg: #ffe785;

// Overrides
* {
  box-sizing: border-box;
}
html,
body,
#app {
  height: 100%;
  min-height: 100%;
  width: 100%;
}

// Default Modifiers
.cursor-help {
  cursor: help;
}
.cursor-default {
  cursor: default;
}
.cursor-pointer {
  cursor: pointer;
}
.bold {
  font-weight: bold;
}
.nowrap {
  white-space: nowrap;
}
// Prevents color of real text in 'input' elements from appearing too 'light' when compared with it's placeholder
//  Without this fix, feel like the user has entered text already in the field, when in fact they have not - it's the placeholder
input.form-control {
  color: #293037;
}

// Default Colours
.cs-text-primary,
.cs-text-coalesce,
.cs-text-success {
  color: $cs_color_primary;
  &-important {
    color: $cs_color_primary !important;
  }
  &-on-hover:hover {
    color: $cs_color_primary !important;
  }
}
.cs-text-info {
  color: $cs_color_info;
  &-important {
    color: $cs_color_info !important;
  }
  &-on-hover:hover {
    color: $cs_color_info !important;
  }
}
.cs-text-danger {
  color: $cs_color_danger;
  &-important {
    color: $cs_color_danger !important;
  }
  &-on-hover:hover {
    color: inherit !important;
  }
}
.cs-text-warning {
  color: $cs_color_warning;
  &-important {
    color: $cs_color_warning !important;
  }
  &-on-hover:hover {
    color: inherit !important;
  }
}

// used to highlight form fields when they are 'active'
.cs-field-active-border {
  border-color: $cs_color_primary !important;
}

.nav-tabs {
  border-width: 2px;
  .nav-link {
    border-width: 2px;
    color: $cs_color_default;
    margin-bottom: -2px;
    &.active {
      font-weight: bold;
      color: $coalesce_green;
      border-radius: 7px 7px 0 0;
    }
  }
}

// WARNING: It is expected that we use the default bootstrap `.btn` as a "base" class, i.e. '.btn' is required BEFORE these
.cs-btn {
  cursor: pointer;
  border-width: 2px;
  border-color: transparent;
  border-radius: 0.4rem;
  &:hover {
    box-shadow: 0px 0px 6px #666;
    cursor: pointer;
  }
  &:disabled {
    opacity: 1 !important;
    cursor: default;
  }
  &:focus:disabled,
  &:hover:disabled {
    box-shadow: none !important;
  }
  &.cs-btn-secondary {
    background-color: $cs_color_medium !important;
    border-color: $cs_color_medium !important;
    color: white !important;
    &:hover {
      background-color: $cs_color_light;
      color: $cs_color_default_dark !important;
    }
  }
  &.cs-btn-info {
    background-color: $cs_color_info !important;
    border-color: $cs_color_info !important;
    color: white !important;
    &:hover {
      background-color: $cs_color_info;
      color: white;
    }
  }
  &.cs-btn-submit {
    background-color: $cs_color_primary !important;
    border-color: $cs_color_primary !important;
    color: white !important;
    &:hover {
      background-color: $cs_color_primary;
      color: white;
    }
  }
  &.cs-btn-danger {
    color: white;
    background-color: $cs_color_danger;
    border-color: $cs_color_danger !important;
    &:hover {
      background-color: $cs_color_danger;
      color: white;
    }
  }
  &.cs-btn-warning {
    color: $cs_color_warning;
    background-color: $cs_color_warning_bg;
    border-color: $cs_color_warning_bg !important;
    &:hover {
      background-color: $cs_color_warning_bg;
      color: $cs_color_warning;
    }
  }
  &.cs-btn-inactive {
    cursor: default;
    &:focus,
    &:disabled {
      box-shadow: none !important;
    }
    &:disabled {
      opacity: 1 !important;
    }
    color: white !important;
    background-color: $cs_color_default !important;
    border-color: $cs_color_default !important;
    &:hover {
      box-shadow: none;
      cursor: default !important;
      color: white !important;
    }
  }
  /* Button Outline Styles */
  &.cs-btn-outline-success {
    &:focus,
    &:disabled {
      box-shadow: none !important;
    }
    border-radius: 0.4rem !important;
    color: $cs_color_primary;
    background-color: white;
    border-color: $cs_color_primary !important;
    &:hover {
      background-color: $cs_color_primary;
      color: white;
    }
  }
  &.cs-btn-outline-danger {
    &:disabled {
      background-color: #ccc !important;
    }
    &:focus,
    &:disabled {
      box-shadow: none !important;
    }
    border-radius: 0.4rem !important;
    font-weight: bold !important;
    color: $cs_color_danger !important;
    background-color: white !important;
    border-color: $cs_color_danger !important;
    border-width: 2px !important;
    &:hover {
      background-color: $cs_color_danger;
      color: white;
    }
  }
  &.cs-btn-outline-warning {
    &:disabled {
      background-color: #ccc !important;
    }
    &:focus,
    &:disabled {
      box-shadow: none !important;
    }
    border-radius: 0.4rem !important;
    font-weight: bold !important;
    color: $cs_color_warning !important;
    background-color: white !important;
    border-color: $cs_color_warning_bg !important;
    border-width: 2px !important;
    &:hover {
      background-color: $cs_color_warning_bg;
      color: white;
    }
  }
  &.cs-btn-outline-info {
    &:focus,
    &:disabled {
      box-shadow: none !important;
    }
    border-radius: 0.4rem !important;
    color: $cs_color_info;
    background-color: white;
    border-color: $cs_color_info !important;
    border-width: 1px !important;
    &:hover {
      background-color: $cs_color_info !important;
      color: white !important;
    }
  }
  &.cs-btn-outline-inactive {
    &:hover {
      box-shadow: none;
      color: #444 !important;
    }
    &:focus,
    &:disabled {
      box-shadow: none !important;
    }
    /* removed opacity override - using bootstrap default which causes opacity for a 'disabled' button */
    /*	&:disabled {
			opacity: 1 !important;
		}*/
    opacity: 0.5 !important;
    border-radius: 0.4rem !important;
    font-weight: bold !important;
    color: #444 !important;
    background-color: white !important;
    border-color: $cs_color_default_dark !important;
    border-width: 1px !important;
  }
}
