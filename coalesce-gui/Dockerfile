FROM node:14.17.1-alpine3.11

RUN mkdir /app
# Change working directory
WORKDIR "/app"
ENV PATH /app/node_modules/.bin:$PATH

RUN apk add --update --no-cache git \
	nodejs-npm \
	python \
	make \
	g++

# Install npm production packages
COPY package*.json /app/
RUN cd /app; npm install --production && npm install rimraf;

RUN npm install -g @vue/cli

COPY . /app
RUN cd /app; if [ -f .env.ci ]; then cat .env.ci >> .env; fi;

RUN cd /app; npm rebuild && npm run build

ENV NODE_PATH=/usr/lib/node_modules/npm/node_modules
ENV NODE_ENV production

ENV PORT 5000
EXPOSE 5000

USER node

CMD ["npm", "start"]
