/* eslint-disable import/no-unresolved */
import { mount } from '@vue/test-utils';
import csInput from '@/utils/components/cs-input';

import localVue from '../../../helpers/localVue';

describe('views / csInput / index', () => {
  const wrapper = mount(csInput, {
    propsData: {
      value: 'test',
    },
    localVue,
  });

  it('Is called CSInput', () => {
    expect(csInput.name).toEqual('CSInput');
  });

  it('has computed and variables', () => {
    expect(typeof csInput.computed).toBe('object');
  });

  it('sets the correct default data', () => {
    expect(typeof csInput.data).toBe('function');

    const defaultData = csInput.data();

    expect(defaultData.defaults.slotPrependClasses).toEqual('border border-left-0');
    expect(defaultData.defaults.slotAppendClasses).toEqual('border border-right-0');
    expect(defaultData.defaults.maxWidth).toEqual('20em');
    expect(defaultData.defaults.tooltip).toEqual('');
    expect(defaultData.defaults.placeholder).toEqual('');
    expect(defaultData.defaults.labelClassesPrefix).toEqual(
      'd-flex align-items-center justify-content-center justify-content-sm-end col-sm-4 pr-sm-3',
    );
    expect(defaultData.defaults.inputGroupClassesPrefix).toEqual('input-fields-height col-sm-12');
    expect(defaultData.defaults.inputGroupClassesPrefixWithLabel).toEqual('input-fields-height col-sm-8');
  });

  const getInput = () => {
    return wrapper.find('input').element;
  };

  it('Default HTML displayed', () => {
    // WITHOUT Label ... class full-width
    expect(
      wrapper
        .find('#inputParent')
        .classes()
        .includes('col-sm-12'),
    ).toBe(true);
    expect(getInput().value).toBe('test');
    expect(getInput().placeholder).toBe('');
    expect(getInput().type).toBe('text');
    expect(wrapper.find('#label').exists()).toBe(false);
    expect(wrapper.find('#messages').exists()).toBe(false);
    expect(wrapper.find('#required').exists()).toBe(false);

    expect(wrapper.find('#inputParent').exists()).toBe(true);
  });

  it('Modified HTML displayed', () => {
    wrapper.setProps({
      value: 'newValue',
      label: 'Test Label',
      options: { placeholder: 'place text', showRequired: 'testing required' },
      required: true,
    });
    wrapper.vm.$nextTick(() => {
      expect(wrapper.find('#label').exists()).toBe(true);
      expect(wrapper.find('#messages').exists()).toBe(true);
      expect(wrapper.find('#required').exists()).toBe(true);

      expect(wrapper.find('#inputParent').exists()).toBe(true);

      expect(wrapper.find('#label').text()).toContain('Test Label');
      expect(wrapper.find('#messages').text()).toBe('testing required');
      expect(getInput().value).toEqual('newValue');
      expect(getInput().placeholder).toEqual('place text');
      // WITH Label ... classes for partial width on each
      expect(
        wrapper
          .find('#label')
          .classes()
          .includes('col-sm-4'),
      ).toBe(true);
      expect(
        wrapper
          .find('#inputParent')
          .classes()
          .includes('col-sm-8'),
      ).toBe(true);
    });
  });
});
