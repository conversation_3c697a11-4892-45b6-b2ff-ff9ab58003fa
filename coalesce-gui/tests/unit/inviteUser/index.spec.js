/* eslint-disable import/no-unresolved */
import { shallowMount } from '@vue/test-utils';
import inviteUser from '@/views/inviteuser/index';

import Vuex from 'vuex';
import localVue from '../helpers/localVue';

describe('views / inviteUser / index', () => {
  const store = new Vuex.Store({
    state: {
      app: {
        sidebar: {
          opened: false,
          withoutAnimation: false,
          config: {},
          location: '',
        },
        device: 'desktop',
      },
    },
    actions: {
      'user/listRoles': jest.fn(),
    },
    getters: {
      sidebar: state => state.app.sidebar,
    },
  });
  shallowMount(inviteUser, {
    store,
    localVue,
  });

  it('Is called InviteUser', () => {
    expect(true).toBe(true);
  });
});
