// test.js

// Import the `mount()` method from the test utils
// and the component you want to test
import { mount } from "@vue/test-utils";
import Hamburger from "@/components/Hamburger";

describe("Hamburger", () => {
  it("renders with is-active class", () => {
    const wrapper = mount(Hamburger, {
      propsData: { isActive: true }
    });
    expect(wrapper.html()).toContain('class="hamburger is-active"');
  });

  it("renders without is-active class", () => {
    const wrapper = mount(Hamburger, {
      propsData: { isActive: false }
    });
    expect(wrapper.html()).toContain('class="hamburger"');
  });

  it("toggleClick event is emited after click", () => {
    const wrapper = mount(Hamburger);

    wrapper.find("div").trigger("click");
    expect(wrapper.emitted("toggleClick")).toHaveLength(1);
  });
});
