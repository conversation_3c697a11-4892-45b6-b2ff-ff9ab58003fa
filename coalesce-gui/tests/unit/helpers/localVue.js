import BootstrapVue, { IconsPlugin } from 'bootstrap-vue';
import Vuelidate from 'vuelidate';

import VTooltip from 'v-tooltip';

import { createLocalVue } from '@vue/test-utils';
import Vuex from 'vuex';

import infoTooltip from '../../../src/utils/components/info-tooltip.vue';
import required from '../../../src/utils/components/required.vue';

import fa from '../../../src/font-awesome-icons';

const { FontAwesomeIcon } = fa;

const localVue = createLocalVue();
localVue.component('font-awesome-icon', FontAwesomeIcon);
localVue.use(Vuex);
localVue.use(BootstrapVue);
localVue.use(IconsPlugin);

localVue.component('info-tooltip', infoTooltip);
localVue.use('required', required);

localVue.use(Vuelidate);
localVue.use(VTooltip);
localVue.component('svg-icon', { template: '<span>SI</span>' });

export default localVue;
