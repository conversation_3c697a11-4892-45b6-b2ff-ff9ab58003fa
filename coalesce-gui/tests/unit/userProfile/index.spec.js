// eslint-disable-next-line import/no-unresolved
import userprofile from '@/views/userprofile/index';

import { shallowMount } from '@vue/test-utils';

import Vuex from 'vuex';
import localVue from '../helpers/localVue';

describe('views / userprofile / index', () => {
  const store = new Vuex.Store({
    state: {
      user: {
        picture: '',
        user: {
          firstname: 'admin',
          lastname: 'coalesce',
          email: '<EMAIL>',
        },
      },
    },

    getters: {
      user: state => state.user.user,
    },
  });
  const wrapper = shallowMount(userprofile, {
    store,
    localVue,
    stubs: ['svg-icon'],
  });

  it('Is called userProfile', () => {
    expect(userprofile.name).toEqual('UserProfile');
  });

  it('displays user-profile section', () => {
    see('User Profile');
    see('admin coalesce', 'div.user-profile-info > h5');
    see('<EMAIL>', 'div.user-profile-info > div');
  });

  it('displays Profile-Details section', () => {
    see('Profile Details');
    see('Email');
    see('<EMAIL>', 'form.profile-info-form');

    see('Name');
    expect(wrapper.find('input#firstname').exists()).toBe(true);
    expect(wrapper.find('input#firstname').element.value).toBe('admin');
    expect(wrapper.find('input#lastname').exists()).toBe(true);
    expect(wrapper.find('input#lastname').element.value).toBe('coalesce');

    see('Position');
    expect(wrapper.find('input#position').exists()).toBe(true);
    expect(wrapper.find('input#position').element.value).toBe('');

    see('Mobile');
    expect(wrapper.find('input#mobile_code').exists()).toBe(true);
    expect(wrapper.find('input#mobile_code').element.value).toBe('');
    expect(wrapper.find('input#mobile_number').exists()).toBe(true);
    expect(wrapper.find('input#mobile_number').element.value).toBe('');
  });

  it('displays Change-Password section', () => {
    see('Change Password');

    see('Current Password');
    expect(wrapper.find('input#current_password').exists()).toBe(true);
    expect(wrapper.find('input#current_password').element.value).toBe('');

    see('New Password');
    expect(wrapper.find('input#password').exists()).toBe(true);
    expect(wrapper.find('input#password').element.value).toBe('');
  });

  // ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
  //                             helper functions                          ||
  // ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||

  let see = (text, selector) => {
    const wrap = selector ? wrapper.find(selector) : wrapper;

    expect(wrap.html()).toContain(text);
  };

  // let type = (selector, text) => {
  //   const node = wrapper.find(selector);

  //   node.element.value = text;
  //   node.trigger('input');
  //   node.trigger('change');
  // };

  // let click = selector => {
  //   wrapper.find(selector).trigger('click');
  // };

  // let submitForm = selector => {
  //   wrapper.find(selector).trigger('submit');
  // };
});
