/* eslint-disable import/no-unresolved */
// test.js

// Import the `mount()` method from the test utils
// and the component you want to test
import { mount } from '@vue/test-utils';
import permissionGroup from '@/views/usermanagement/permissions';
import checkboxRow from '@/views/usermanagement/checkbox';

import localVue from '../helpers/localVue';

describe('views / usermanagement / permissions / index', () => {
  let wrapper;
  const permissionsData = null;
  const selected = ['preprodservice_codeedit', 'preprodservice_launch'];
  const data = {
    group: 'preprodservice',
    permissions: [
      { name: 'codeedit', description: 'Can create/edit/delete a ussd code for a Pre Production modules' },
      { name: 'launch', description: 'Can push/launch a module to Pre Production' },
    ],
  };
  const role = {
    name: 'operator',
    permission: [{ group: 'preprodservice', permissions: ['launch', 'codeedit'] }],
    description: 'Can push to staging, and launch to production. Can manage all short-codes for menus.',
  };
  const index = 5;

  const propsData = {
    permissionsData,
    selected,
    data,
    index,
    role,
  };

  beforeEach(() => {
    wrapper = mount(permissionGroup, { propsData, localVue });
  });

  it('Is called permission-group', () => {
    expect(permissionGroup.name).toEqual('PermissionGroup');
  });

  it('renders without errors', () => {
    see(data.group);
  });

  it('has a mounted hook', () => {
    expect(typeof permissionGroup.mounted).toBe('function');
  });
  it('sets the correct default data', () => {
    expect(typeof permissionGroup.data).toBe('function');

    expect(wrapper.vm.allSelected).toBe(true);
    expect(wrapper.vm.indeterminate).toBe(false);
    expect(wrapper.vm.selectedPermissions).toEqual(['preprodservice_launch', 'preprodservice_codeedit']);
    expect(wrapper.vm.shown).toBe(false);
  });
  it('renders the Child component (checkbox.vue)', () => {
    expect(wrapper.findComponent(checkboxRow).exists()).toBe(true);
    expect(wrapper.findAllComponents(checkboxRow).length).toBe(2); // see propsData we're sending to the child comp
  });
  it('emits events correctly when child component emits the corresponding event', () => {
    const childComponent = wrapper.findComponent(checkboxRow);

    // wrapper.vm.onCheckboxStateChange('checked', permission.name) // call directly from parent
    childComponent.vm.onCheckboxStateChange('checked', data.permissions[0].name); // calling from child
    expect(wrapper.emitted().addPermission.length).toBe(1);

    // wrapper.vm.onCheckboxStateChange( null, permission.name) // call directly from parent
    childComponent.vm.onCheckboxStateChange(undefined, data.permissions[0].name); // calling from child
    expect(wrapper.emitted('removePermission')).toHaveLength(1);
  });

  // ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
  //                             helper functions                          ||
  // ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||

  let see = (text, selector) => {
    const wrap = selector ? wrapper.find(selector) : wrapper;

    expect(wrap.html()).toContain(text);
  };

  /*
  let type = (selector, text) => {
    const node = wrapper.find(selector);

    node.element.value = text;
    node.trigger('input');
    node.trigger('change');
  };

  let click = selector => {
    wrapper.find(selector).trigger('click');
  };

  let submitForm = selector => {
    wrapper.find(selector).trigger('submit');
  };
  */
});
