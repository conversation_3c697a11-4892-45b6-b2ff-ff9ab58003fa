/* eslint-disable import/no-unresolved */
// test.js

// Import the `mount()` method from the test utils
// and the component you want to test
import { mount } from '@vue/test-utils';
// eslint-disable-next-line import/extensions
import checkboxRow from '@/views/usermanagement/checkbox';
import localVue from '../helpers/localVue';

describe('views / usermanagement / checkboxRow / index', () => {
  let wrapper;

  const permission = { name: 'test', description: 'test description' };
  const data = { group: 'component' };

  const propsData = { permission, data };

  beforeEach(() => {
    wrapper = mount(checkboxRow, {
      propsData,
      localVue,
    });
  });

  it('Is called checkboxRow', () => {
    expect(checkboxRow.name).toEqual('CheckboxRow');
  });
  it('renders without errors', () => {
    see(permission.description, 'div.col-8');
    see(permission.name, 'div.col-4');
  });
  it('emits onCheckboxStateChange event when user clicks a checkbox', () => {
    type('.custom-checkbox > input', permission.name);
    wrapper.emitted('onCheckboxStateChange');
    expect(wrapper.emitted('onCheckboxStateChange')).not.toBe(undefined);
  });
  // ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
  //                             helper functions                          ||
  // ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||

  let see = (text, selector) => {
    const wrap = selector ? wrapper.find(selector) : wrapper;

    expect(wrap.text()).toBe(text);
  };

  let type = (selector, text) => {
    const node = wrapper.find(selector);

    node.element.value = text;

    node.trigger('input');
    node.trigger('change');
  };

  /*
  const click = selector => {
    wrapper.find(selector).trigger('click');
  };

  const submitForm = selector => {
    wrapper.find(selector).trigger('submit');
  };
  */
});
