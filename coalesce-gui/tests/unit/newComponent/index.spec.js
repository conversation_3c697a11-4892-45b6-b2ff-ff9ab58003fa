/* eslint-disable import/no-unresolved */
// test.js

// Import the `mount()` method from the test utils
// and the component you want to test
/*
import { shallowMount } from '@vue/test-utils';
import newcomponent from '@/views/newcomponent/index';
*/

describe('views / newcomponent / index', () => {
  it('Dummy test', () => {
    expect(true).toBeTruthy();
  });
});
/*
describe('views / newcomponent / index', () => {
  const wrapper = shallowMount(newcomponent, {
    // methods : {onSubmit: () => {} }
  });

  it('Is called newcomponent', () => {
    expect(wrapper.name()).toEqual('Newcomponent');
  });
  it('renders without errors', () => {
    expect(wrapper.isVueInstance()).toBeTruthy();
  });
  it('has a mounted hook', () => {
    expect(typeof newcomponent.mounted).toBe('function');
  });

  it('sets the correct default data', () => {
    expect(typeof newcomponent.data).toBe('function');

    const defaultData = newcomponent.data();

    expect(defaultData.component.displayName).toBe('');
    expect(defaultData.component.name).toBe('');
    expect(defaultData.component.description).toBe('');
    expect(defaultData.valid).toBe(false);
    expect(defaultData.isDisabled).toBe(false);
    expect(defaultData.submitError).toBe('');
  });

  it('if component Name/Description is empty, submit button should be disabled', () => {
    const defaultData = newcomponent.data();

    expect(defaultData.component.displayName).toBe('');
    expect(defaultData.component.name).toBe('');
    expect(defaultData.component.description).toBe('');

    // const button = wrapper.find("button").props().disabled;

    // expect(button).toBe(true)
  });
});
*/
