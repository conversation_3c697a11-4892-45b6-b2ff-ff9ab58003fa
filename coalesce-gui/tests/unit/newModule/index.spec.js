/* eslint-disable import/no-unresolved */
// test.js

// Import the `mount()` method from the test utils
// and the component you want to test
/*
import { shallowMount } from '@vue/test-utils';
import newModule from '@/views/newmodule/index';
import sinon from 'sinon';
*/

describe('views / newmodule / index', () => {
  it('Dummy test', () => {
    expect(true).toBeTruthy();
  });
});

/*
describe('views / newModule / index', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = shallowMount(newModule, {
      // methods : {onSubmit: () => {} }
    });
  });

  it('Is called newModule', () => {
    expect(wrapper.name()).toEqual('Newmodule');
  });
  it('renders without errors', () => {
    expect(wrapper.isVueInstance()).toBeTruthy();

    // text that user would see in this page
    see('Provide Module Details', '.module-text');
    see('<strong>Note:</strong> numbers are not allowed as a <strong>"prefix"</strong>');
    see('Module Name');
    see('Description');

    //  form must have 2 inputs field and a submit button
    expect(wrapper.contains('input#modulename')).toBe(true);
    expect(wrapper.contains('input#description')).toBe(true);
    expect(wrapper.contains('button.submit-button')).toBe(true);
  });
  it('has a mounted and computed hooks', () => {
    expect(typeof newModule.mounted).toBe('function');
    expect(typeof newModule.computed).toBe('object');
  });

  it('sets the correct default data', () => {
    expect(typeof newModule.data).toBe('function');

    expect(wrapper.vm.module.displayName).toBe('');
    expect(wrapper.vm.module.name).toBe('');
    expect(wrapper.vm.module.description).toBe('');
    expect(wrapper.vm.valid).toBe(false);
    expect(wrapper.vm.isDisabled).toBe(false);
    expect(wrapper.vm.submitError).toBe('');
  });

  it('submit button should be disabled if module Name/Description is empty', async () => {
    // const clickMethodStub = sinon.stub();
    // wrapper.setMethods({ onSubmit: clickMethodStub });

    expect(wrapper.vm.module.displayName).toBe('');
    expect(wrapper.vm.module.name).toBe('');
    expect(wrapper.vm.module.description).toBe('');

    // submit button should have a class "cs-btn-inactive" if its disabled
    expect(wrapper.html()).toContain('class="submit-button btn cs-btn cs-btn-inactive"');
    // expect(wrapper.find(".cs-btn-inactive").exists()).toBe(true);
  });

  it('test name/description input fields', async () => {
    //
    // 1) before setting module Name and Description
    //
    expect(wrapper.vm.module.displayName).toBe('');
    expect(wrapper.vm.module.description).toBe('');

    //
    // 2) setting module Name and Description
    //

    const moduleName = 'test-jest displayName';
    const moduleDescription = 'test-jest description';

    type('#modulename', moduleName);
    type('#description', moduleDescription);

    //
    // 3) after setting module Name and Description
    //

    expect(wrapper.vm.module.displayName).toBe(moduleName);
    expect(wrapper.vm.module.description).toBe(moduleDescription);
  });

  it('creates a new module upon form-submit if user enters correct name/description', async () => {
    const clickMethodStub = sinon.stub();
    wrapper.setMethods({ onSubmit: clickMethodStub });

    //
    // set default data to test just the submit action
    //
    wrapper.setData({
      module: {
        displayName: 'test-jest displayName',
        name: 'test-jest name',
        description: 'test-jest description',
      },
      classes: {
        submitButton: 'cs-btn-submit',
      },
      valid: true,
      isDisabled: false,
    });
    //
    // make sure default data sets correctly
    //

    expect(wrapper.vm.module.displayName).toBe('test-jest displayName');
    expect(wrapper.vm.module.name).toBe('test-jest name');
    expect(wrapper.vm.module.description).toBe('test-jest description');

    //
    // trigger form submit action
    //
    submitForm('form');
    await wrapper.vm.$nextTick();

    expect(clickMethodStub.called).toBe(true);
    wrapper.destroy();
  });

  // ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
  //                             helper functions                          ||
  // ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||

  let see = (text, selector) => {
    const wrap = selector ? wrapper.find(selector) : wrapper;

    expect(wrap.html()).toContain(text);
  };

  let type = (selector, text) => {
    const node = wrapper.find(selector);

    node.element.value = text;
    node.trigger('input');
    node.trigger('change');
  };

  // let click = selector => {
  //   wrapper.find(selector).trigger('click');
  // };

  let submitForm = selector => {
    wrapper.find(selector).trigger('submit');
  };
});
*/
