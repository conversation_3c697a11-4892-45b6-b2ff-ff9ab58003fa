/* eslint-disable import/no-unresolved */
import { shallowMount } from '@vue/test-utils';
import SMPPEndpoint from '@/components/ConnectorEndpoints/smpp/index.vue';

import localVue from '../../../helpers/localVue';

describe('views / SMPP Endpoint / index', () => {
  const isProd = false;

  const wrapper = shallowMount(SMPPEndpoint, {
    propsData: {
      isProd,
      endpointIndex: 0,
      editMode: true,
      endpointData: {
        hostname: 'HOST-NAME',
        protocol: 'SMPP',
        port: 2775,
        timeout: 1000,
        // encodings: ['LATIN1', 'UCS2'],  //// FIXME / TODO  --- This is auto assigned in the vue component due to disabling of user editing of encodings
        auth: {
          system_id: 'id',
          password: 'pw',
        },
      },
    },
    localVue,
  });

  it('Is called SMPPEndpoint', () => {
    expect(SMPPEndpoint.name).toEqual('SMPPEndpoint');
  });

  it('Has computed and variables', () => {
    expect(typeof SMPPEndpoint.computed).toBe('object');
  });

  it('Has the correct Titles, Number of inputs, and respective Labels', () => {
    expect(wrapper.find('#username-preprod').exists()).toBe(true);
    expect(wrapper.find('#hostname-preprod').exists()).toBe(true);
    expect(wrapper.find('#protocol-preprod').exists()).toBe(true);
    expect(wrapper.find('#port-preprod').exists()).toBe(true);
    expect(wrapper.find('#timeout-preprod').exists()).toBe(true);
    expect(wrapper.find('#system_id-preprod').exists()).toBe(true);
    // expect(wrapper.find('#encodings-group-preprod').exists()).toBe(true);
  });

  it('Correctly populates vm "endpoint" data from endpointData prop', () => {
    const { endpoint } = wrapper.vm;
    expect(endpoint.hostname).toBe('HOST-NAME');
    expect(endpoint.protocol).toBe('SMPP');
    expect(endpoint.port).toBe(2775);
    expect(endpoint.timeout).toBe(1000);
    expect(endpoint.auth.system_id).toBe('id');
    expect(endpoint.auth.password).toBe('pw');
    expect(Array.isArray(endpoint.encodings)).toBe(true);
    expect(endpoint.encodings.includes('LATIN1')).toBe(true);
    expect(endpoint.encodings.includes('UCS2')).toBe(true);
    expect(endpoint.encodings.includes('ASCII')).toBe(true);
    // expect(endpoint.encodings.includes('ASCII')).toBe(false);   //// FIXME / TODO  --- This is auto assigned in the vue component due to disabling of user editing of encodings
  });

  const endpointValidator = endpointData => {
    const mountData = {
      propsData: {
        isProd: false,
        endpointIndex: 0,
        editMode: true,
        endpointData,
      },
      localVue,
    };
    /**
     * NEW _WRAPPER_ CREATED AS WORKAROUND
     *  There is a problem using setProps on existing wrapper -- see https://github.com/vuejs/vue-test-utils/issues/738
     */
    let validationWrapper = shallowMount(SMPPEndpoint, mountData);

    return {
      resetEndpointData: newEndpointData => {
        mountData.propsData.endpointData = newEndpointData;
        validationWrapper = shallowMount(SMPPEndpoint, mountData);
      },
      getWrapper: () => {
        return validationWrapper;
      },
      getValueOf: key => {
        const v = validationWrapper.vm.endpoint[key];
        // console.debug(`${key} .not.toBe(undefined)`);
        expect(typeof v).not.toBe('undefined');
        return v;
      },
      expect: key => {
        const v = validationWrapper.vm.$v.endpoint[key];
        // console.debug(`${key} .not.toBe(undefined)`);
        expect(typeof v).not.toBe('undefined');
        // console.debug(`validating key '${key}' - it's value:`, v.$model);
        return {
          toBe: value => {
            // console.debug(`expect ${key}.value .toBe ...`);
            expect(v.$model).toBe(value);
          },
          isValid: bool => {
            // console.debug(`${key} .isValid ...`);
            expect(!v.$invalid).toBe(bool);
          },
          child: child => {
            const c = v[child];
            // console.debug(`${child} (as KEY - not value) .not.toBe(undefined)`);
            // console.debug(`validating child '${child}' - it's value:`, c.$model);
            expect(typeof c).not.toBe('undefined');
            return {
              isValid: bool => {
                // console.debug(`${child} .isValid ...`);
                expect(!c.$invalid).toBe(bool);
              },
              toBe: value => {
                // console.debug(`${child} .toBe ...`);
                expect(c.$model).toBe(value);
              },
            };
          },
        };
      },
    };
  };

  it('Validates obvious vm endpoint data (after populating - see previous test)', () => {
    const firstWrapperIsValid = !wrapper.vm.$v.endpoint.$invalid;
    expect(firstWrapperIsValid).toBe(true);

    const INVALID_SYS_ID_LENGTH = 129;
    const INVALID_PW_LENGTH = 65;

    // ALL INVALID ...
    const validator = endpointValidator({
      hostname: '123',
      protocol: 'not-SMPP', // cannot use internal vualidate
      port: 'abcde',
      timeout: 100,
      auth: {
        system_id: 'a'.repeat(INVALID_SYS_ID_LENGTH),
        password: 'b'.repeat(INVALID_PW_LENGTH),
      },
    });

    const encodings = validator.getValueOf('encodings');
    expect(encodings.length).toBe(3);
    encodings.forEach(encoding => {
      expect(['ASCII', 'UCS2', 'LATIN1'].includes(encoding)).toBe(true);
    });

    validator.expect('hostname').toBe('123');
    validator.expect('hostname').isValid(false);

    validator.expect('protocol').toBe('not-SMPP');
    validator.expect('protocol').isValid(false);

    // conversion to EMPTY is normal, but still counts as invalid (i.e. this is a required field)
    //  remains invalid, as it's a required field
    validator.expect('port').toBe('');
    validator.expect('port').isValid(false); // required

    // timeout defaults to a VALID value of 1000 if you specify any _invalid_ value
    validator.expect('timeout').toBe(1000);
    validator.expect('timeout').isValid(true);
  });

  it('Validates _conditional_ vm endpoint data port and timeout', () => {
    // STRING that can be converted to INT is VALID
    let validator = endpointValidator({ port: '500', timeout: '1500', auth: { system_id: '' } });
    validator.expect('port').toBe(500); // forced to null (default) if invalid
    validator.expect('port').isValid(true);
    validator.expect('timeout').toBe(1500);
    validator.expect('timeout').isValid(true);

    // Invalid number converted to empty
    //   remains invalid as it's a required field
    validator = endpointValidator({ port: -500, timeout: -100 });
    validator.expect('port').toBe('');
    validator.expect('port').isValid(false);
    validator.expect('timeout').toBe(1000); // converted to default valid timeout
    validator.expect('timeout').isValid(true);
  });
});
