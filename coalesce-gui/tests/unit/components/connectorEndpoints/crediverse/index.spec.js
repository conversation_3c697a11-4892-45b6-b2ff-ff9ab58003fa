/* eslint-disable import/no-unresolved */
import { shallowMount } from '@vue/test-utils';
import CrediverseEndpoint from '@/components/ConnectorEndpoints/crediverse/index.vue';

import localVue from '../../../helpers/localVue';

describe('views / Crediverse Endpoint / index', () => {
  const prodWrapper = shallowMount(CrediverseEndpoint, {
    propsData: {
      isProd: true,
      endpointIndex: 0,
      editMode: true,
      endpointData: {},
    },
    localVue,
  });

  const wrapper = shallowMount(CrediverseEndpoint, {
    propsData: {
      isProd: false,
      endpointIndex: 0,
      editMode: true,
      endpointData: {
        hostname: 'crediverse.endpoint.hostname',
        url: '/url',
        port: 8995,
        timeout: 2000,
        auth: {
          client_id: 'cID',
          client_secret: 'cSecret',
          username: 'cUname',
          password: 'cPword',
        },
      },
    },
    localVue,
  });

  it('Is called CrediverseEndpoint', () => {
    expect(CrediverseEndpoint.name).toEqual('CrediverseEndpoint');
  });

  it('Has computed and variables', () => {
    expect(typeof CrediverseEndpoint.computed).toBe('object');
  });

  it('Has the correct Titles, Number of inputs, and respective Labels (Preprod)', () => {
    expect(wrapper.find('#username-preprod').exists()).toBe(true);
    expect(wrapper.find('#url-preprod').exists()).toBe(true);
    expect(wrapper.find('#port-preprod').exists()).toBe(true);
    expect(wrapper.find('#timeout-preprod').exists()).toBe(true);
    expect(wrapper.find('#client_id-preprod').exists()).toBe(true);
    expect(wrapper.find('#client_secret-preprod').exists()).toBe(true);
    expect(wrapper.find('#client_username-preprod').exists()).toBe(true);
    expect(wrapper.find('#client_password-preprod').exists()).toBe(true);
  });

  it('Has the correct Titles, Number of inputs, and respective Labels (Prod)', () => {
    expect(prodWrapper.find('#username-prod').exists()).toBe(true);
    expect(prodWrapper.find('#url-prod').exists()).toBe(true);
    expect(prodWrapper.find('#port-prod').exists()).toBe(true);
    expect(prodWrapper.find('#timeout-prod').exists()).toBe(true);
    expect(prodWrapper.find('#client_id-prod').exists()).toBe(true);
    expect(prodWrapper.find('#client_secret-prod').exists()).toBe(true);
    expect(prodWrapper.find('#client_username-prod').exists()).toBe(true);
    expect(prodWrapper.find('#client_password-prod').exists()).toBe(true);
  });

  it('Correctly populates vm "endpoint" data from endpointData prop', () => {
    const { endpoint } = wrapper.vm;
    expect(endpoint.hostname).toBe('crediverse.endpoint.hostname');
    expect(endpoint.port).toBe(8995);
    expect(endpoint.timeout).toBe(2000);
    expect(endpoint.auth.client_id).toBe('cID');
    expect(endpoint.auth.client_secret).toBe('cSecret');
    expect(endpoint.auth.username).toBe('cUname');
    expect(endpoint.auth.password).toBe('cPword');
  });

  const endpointValidator = endpointData => {
    const mountData = {
      propsData: {
        isProd: false,
        endpointIndex: 0,
        editMode: true,
        endpointData,
      },
      localVue,
    };
    /**
     * NEW _WRAPPER_ CREATED AS WORKAROUND
     *  There is a problem using setProps on existing wrapper -- see https://github.com/vuejs/vue-test-utils/issues/738
     */
    let validationWrapper = shallowMount(CrediverseEndpoint, mountData);

    return {
      resetEndpointData: newEndpointData => {
        mountData.propsData.endpointData = newEndpointData;
        validationWrapper = shallowMount(CrediverseEndpoint, mountData);
      },
      getWrapper: () => {
        return validationWrapper;
      },
      expect: key => {
        const v = validationWrapper.vm.$v.endpoint[key];
        // console.debug(`${key} .not.toBe(undefined)`);
        expect(typeof v).not.toBe('undefined');
        // console.debug(`validating key '${key}' - it's value:`, v.$model);
        return {
          toBe: value => {
            // console.debug(`expect ${key}.value .toBe ...`);
            expect(v.$model).toBe(value);
          },
          isValid: bool => {
            // console.debug(`${key} .isValid ...`);
            expect(!v.$invalid).toBe(bool);
          },
          child: child => {
            const c = v[child];
            // console.debug(`${child} (as KEY - not value) .not.toBe(undefined)`);
            // console.debug(`validating child '${child}' - it's value:`, c.$model);
            expect(typeof c).not.toBe('undefined');
            return {
              isValid: bool => {
                // console.debug(`${child} .isValid ...`);
                expect(!c.$invalid).toBe(bool);
              },
              toBe: value => {
                // console.debug(`${child} .toBe ...`);
                expect(c.$model).toBe(value);
              },
            };
          },
        };
      },
    };
  };

  it('Validates obvious vm endpoint data (after populating - see previous test)', () => {
    const firstWrapperIsValid = !wrapper.vm.$v.endpoint.$invalid;
    expect(firstWrapperIsValid).toBe(true);

    const CLIENT_ID_INVALID_LENGTH = 129;
    const CLIENT_SECRET_INVALID_LENGTH = 1025;
    const CLIENT_UN_INVALID_LENGTH = 65;
    const CLIENT_PW_INVALID_LENGTH = 65;

    // eslint-disable-next-line camelcase
    const client_id = 'a'.repeat(CLIENT_ID_INVALID_LENGTH);
    // eslint-disable-next-line camelcase
    const client_secret = 'b'.repeat(CLIENT_SECRET_INVALID_LENGTH);
    const username = 'a'.repeat(CLIENT_UN_INVALID_LENGTH);
    const password = 'b'.repeat(CLIENT_PW_INVALID_LENGTH);

    // ALL INVALID ...
    const validator = endpointValidator({
      hostname: '123',
      port: 'abcde',
      timeout: 100,
      auth: {
        client_id,
        client_secret,
        username,
        password,
      },
    });
    validator.expect('hostname').toBe('123');
    validator.expect('hostname').isValid(false);

    // conversion to EMPTY is normal, but still counts as invalid (i.e. this is a required field)
    //  remains invalid, as it's a required field
    validator.expect('port').toBe(null);
    validator.expect('port').isValid(false); // required

    // timeout defaults to a VALID value of 1000 if you specify any _invalid_ value
    validator.expect('timeout').toBe(1000);
    validator.expect('timeout').isValid(true);

    // Each field length is not valid
    validator
      .expect('auth')
      .child('client_id')
      .toBe(client_id);
    validator
      .expect('auth')
      .child('client_id')
      .isValid(false);

    validator
      .expect('auth')
      .child('client_secret')
      .toBe(client_secret);
    validator
      .expect('auth')
      .child('client_secret')
      .isValid(false);

    validator
      .expect('auth')
      .child('username')
      .toBe(username);
    validator
      .expect('auth')
      .child('username')
      .isValid(false);

    validator
      .expect('auth')
      .child('password')
      .toBe(password);
    validator
      .expect('auth')
      .child('password')
      .isValid(false);
  });

  it('Validates _conditional_ vm endpoint data port and timeout', () => {
    // STRING that can be converted to INT is VALID
    let validator = endpointValidator({ port: '500', timeout: '1500', auth: { client_id: '' } });
    validator.expect('port').toBe(500); // forced to null (default) if invalid
    validator.expect('port').isValid(true);
    validator.expect('timeout').toBe(1500);
    validator.expect('timeout').isValid(true);

    // Invalid number converted to empty
    //   remains invalid as it's a required field
    validator = endpointValidator({ port: -500, timeout: -100 });
    validator.expect('port').toBe(null);
    validator.expect('port').isValid(false);
    validator.expect('timeout').toBe(1000); // converted to default valid timeout
    validator.expect('timeout').isValid(true);
  });
});
