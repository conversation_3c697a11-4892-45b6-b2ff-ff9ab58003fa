/* eslint-disable import/no-unresolved */
import { shallowMount } from '@vue/test-utils';
import UCIPEndpoint from '@/components/ConnectorEndpoints/UCIP/index.vue';

import localVue from '../../../helpers/localVue';

describe('views / UCIP Endpoint / index', () => {
  const isProd = false;

  const wrapper = shallowMount(UCIPEndpoint, {
    propsData: {
      isProd,
      endpointIndex: 0,
      editMode: true,
      endpointData: {
        hostname: 'HOST-NAME',
        url: '/URL-HERE',
        port: 4000,
        timeout: 1003,
        auth: {
          username: 'uname',
          password: 'pword',
        },
        subscriberNumberNAI: 2,
        hostIdentity: {
          originNodeType: 'HxC NT',
          originHostName: 'HxC HN',
        },
        prefixes: {
          national: '0',
          country: '27',
          international: '00',
        },
        operatorFormats: [{ prefix: '84', length: 9 }],
      },
    },
    localVue,
  });

  it('Is called UCIPEndpoint', () => {
    expect(UCIPEndpoint.name).toEqual('UCIPEndpoint');
  });

  it('Has computed and variables', () => {
    expect(typeof UCIPEndpoint.computed).toBe('object');
  });

  it('Contains Chrome AUTOFILL Fix inputs', () => {
    expect(
      wrapper
        .find('#chromeAutoFillFix-0')
        .find(`#username-${isProd ? 'prod' : 'preprod'}-0`)
        .exists(),
    ).toBe(true);
    expect(
      wrapper
        .find('#chromeAutoFillFix-0')
        .find(`#password-${isProd ? 'prod' : 'preprod'}-0`)
        .exists(),
    ).toBe(true);
  });

  it('Has the correct Titles, Number of inputs, and respective Labels', () => {
    expect(wrapper.find('#connection-details-title-0').exists()).toBe(true);
    expect(wrapper.find('#host-identity-title-0').exists()).toBe(true);
    expect(wrapper.find('#nai-title-0').exists()).toBe(true);
    expect(wrapper.find('#ucip-auth-title-0').exists()).toBe(true);
    expect(wrapper.find('#standard-prefixes-title-0').exists()).toBe(true);
    expect(wrapper.find('#operator-prefixes-lengths-title-0').exists()).toBe(true);

    const getInputWrappersFor = title => {
      const stubs = wrapper.find(title).findAll('cs-input-stub');
      return {
        hasLength: length => {
          expect(stubs.length === length).toBe(true);
        },
        containsLabels: labels => {
          labels.forEach(label => {
            const labelIndex = stubs.wrappers.findIndex(input => {
              return input.attributes('label') === label;
            });
            expect(labelIndex !== -1).toBe(true);
          });
        },
      };
    };

    const inputs = [
      {
        id: '#connection-detail-inputs-0',
        length: 4,
        labels: ['Hostname or IP', 'URL', 'Port', 'Timeout'],
      },
      {
        id: '#host-identity-inputs-0',
        length: 2,
        labels: ['Origin Node Type', 'Origin Host Name'],
      },
      {
        id: '#nai-input-0',
        length: 1,
        labels: ['Subscriber Number NAI'],
      },
      {
        id: '#ucip-auth-inputs-0',
        length: 2,
        labels: ['Username', 'Password'],
      },
      {
        id: '#operator-prefix-and-length-inputs-0',
        length: 2,
        labels: [], // no labels...
      },
    ];

    inputs.forEach(input => {
      const csInputs = getInputWrappersFor(input.id);
      csInputs.hasLength(input.length);
      csInputs.containsLabels(input.labels);
    });
  });

  it('Correctly populates vm "endpoint" data from endpointData prop', () => {
    const { endpoint } = wrapper.vm;
    expect(endpoint.hostname).toBe('HOST-NAME');
    expect(endpoint.url).toBe('/URL-HERE');
    expect(endpoint.port).toBe(4000);
    expect(endpoint.timeout).toBe(1003);
    expect(endpoint.auth.username).toBe('uname');
    expect(endpoint.auth.password).toBe('pword');
    expect(endpoint.subscriberNumberNAI).toBe(2);
    expect(endpoint.hostIdentity.originNodeType).toBe('HxC NT');
    expect(endpoint.hostIdentity.originHostName).toBe('HxC HN');
    expect(endpoint.prefixes.national).toBe('0');
    expect(endpoint.prefixes.country).toBe('27');
    expect(endpoint.prefixes.international).toBe('00');
    expect(Array.isArray(endpoint.operatorFormats)).toBe(true);
    expect(endpoint.operatorFormats[0].prefix).toBe('84');
    expect(endpoint.operatorFormats[0].length).toBe(9);
  });

  const endpointValidator = endpointData => {
    const mountData = {
      propsData: {
        isProd: false,
        endpointIndex: 0,
        editMode: true,
        endpointData,
      },
      localVue,
    };
    /**
     * NEW _WRAPPER_ CREATED AS WORKAROUND
     *  There is a problem using setProps on existing wrapper -- see https://github.com/vuejs/vue-test-utils/issues/738
     */
    let validationWrapper = shallowMount(UCIPEndpoint, mountData);

    return {
      resetEndpointData: newEndpointData => {
        mountData.propsData.endpointData = newEndpointData;
        validationWrapper = shallowMount(UCIPEndpoint, mountData);
      },
      getWrapper: () => {
        return validationWrapper;
      },
      expect: key => {
        const v = validationWrapper.vm.$v.endpoint[key];
        // console.debug(`${key} .not.toBe(undefined)`);
        expect(typeof v).not.toBe('undefined');
        // console.debug(`validating key '${key}' - it's value:`, v.$model);
        return {
          toBe: value => {
            // console.debug(`expect ${key}.value .toBe ...`);
            expect(v.$model).toBe(value);
          },
          isValid: bool => {
            // console.debug(`${key} .isValid ...`);
            expect(!v.$invalid).toBe(bool);
          },
          child: child => {
            const c = v[child];
            // console.debug(`${child} (as KEY - not value) .not.toBe(undefined)`);
            // console.debug(`validating child '${child}' - it's value:`, c.$model);
            expect(typeof c).not.toBe('undefined');
            return {
              isValid: bool => {
                // console.debug(`${child} .isValid ...`);
                expect(!c.$invalid).toBe(bool);
              },
              toBe: value => {
                // console.debug(`${child} .toBe ...`);
                expect(c.$model).toBe(value);
              },
            };
          },
        };
      },
    };
  };

  it('Validates obvious vm endpoint data (after populating - see previous test)', () => {
    const firstWrapperIsValid = !wrapper.vm.$v.endpoint.$invalid;
    expect(firstWrapperIsValid).toBe(true);

    // ALL INVALID ...
    const validator = endpointValidator({
      hostname: '.invalid',
      url: '',
      port: 'abcde',
      timeout: 100,
      hostIdentity: {
        originNodeType: '',
        originHostName: '',
      },
      subscriberNumberNAI: 3,
      prefixes: {
        national: '',
        country: '',
        international: '',
      },
    });
    validator.expect('hostname').toBe('.invalid');
    validator.expect('hostname').isValid(false);

    validator.expect('url').toBe('');
    validator.expect('url').isValid(false);

    // conversion to EMPTY is normal, but still counts as invalid (i.e. this is a required field)
    //  remains invalid, as it's a required field
    validator.expect('port').toBe(null);
    validator.expect('port').isValid(false); // required

    // timeout defaults to a VALID value of 1000 if you specify any _invalid_ value
    validator.expect('timeout').toBe(1000);
    validator.expect('timeout').isValid(true);

    // Host Identity fields required.... invalid if empty
    validator
      .expect('hostIdentity')
      .child('originNodeType')
      .toBe('');
    validator
      .expect('hostIdentity')
      .child('originNodeType')
      .isValid(false);

    validator
      .expect('hostIdentity')
      .child('originHostName')
      .toBe('');
    validator
      .expect('hostIdentity')
      .child('originHostName')
      .isValid(false);

    validator.expect('subscriberNumberNAI').toBe(null);
    validator.expect('subscriberNumberNAI').isValid(true);

    validator
      .expect('prefixes')
      .child('national')
      .toBe('');
    validator
      .expect('prefixes')
      .child('national')
      .isValid(true);

    validator
      .expect('prefixes')
      .child('country')
      .toBe('');
    validator
      .expect('prefixes')
      .child('country')
      .isValid(true);

    validator
      .expect('prefixes')
      .child('international')
      .toBe('');
    validator
      .expect('prefixes')
      .child('international')
      .isValid(true);
  });

  it('Validates _conditional_ vm endpoint data port and timeout', () => {
    // STRING that can be converted to INT is VALID
    let validator = endpointValidator({ port: '500', timeout: '1500' });
    validator.expect('port').toBe(500);
    validator.expect('port').isValid(true);
    validator.expect('timeout').toBe(1500);
    validator.expect('timeout').isValid(true);

    // Invalid number converted to empty
    //   remains invalid as it's a required field
    validator = endpointValidator({ port: -500, timeout: -100 });
    validator.expect('port').toBe(null);
    validator.expect('port').isValid(false);
    validator.expect('timeout').toBe(1000); // converted to default valid timeout
    validator.expect('timeout').isValid(true);
  });

  it('Validates _conditional_ vm endpoint data NAI, prefixes, and formats', () => {
    let testData = {
      subscriberNumberNAI: null,
      prefixes: {
        national: '',
        country: '',
        international: '',
      },
      operatorFormats: [],
    };
    const validator = endpointValidator(testData);
    const vWrapper = validator.getWrapper();

    // Valid if all empty
    validator.expect('subscriberNumberNAI').isValid(true);
    validator.expect('prefixes').isValid(true);
    validator.expect('operatorFormats').isValid(true);

    testData.prefixes.national = '1';
    validator.resetEndpointData(testData);

    // REQUIRED .... WHEN prefixes/operatorFormats is NOT empty (regardless of prefix/operatorFormat validity)
    validator.expect('subscriberNumberNAI').toBe(null);
    validator.expect('subscriberNumberNAI').isValid(false);

    validator.expect('prefixes').isValid(true); // valid when no prefixes/formats are set
    validator.expect('operatorFormats').isValid(false);

    testData.operatorFormats.push({ prefix: '84', length: 7 });
    validator.resetEndpointData(testData);

    validator.expect('prefixes').isValid(false); // not valid if missing a prefix national or country

    // NOTE: Not setting international - as it is NOT required.
    testData.prefixes.country = '27';
    validator.resetEndpointData(testData);
    validator.expect('prefixes').isValid(true);
    validator.expect('operatorFormats').isValid(true);

    testData = {
      subscriberNumberNAI: null,
      prefixes: {
        national: '',
        country: '',
        international: '',
      },
      operatorFormats: [{ prefix: '84', length: 7 }],
    };
    validator.resetEndpointData(testData);
    validator.expect('prefixes').isValid(false);

    // still invalid when empty
    validator.expect('subscriberNumberNAI').toBe(null);
    validator.expect('subscriberNumberNAI').isValid(false);

    testData.subscriberNumberNAI = '3';
    validator.resetEndpointData(testData);

    validator.expect('subscriberNumberNAI').toBe(null);
    validator.expect('subscriberNumberNAI').isValid(false); // field is required when other formats/prefixes are filled in

    testData.subscriberNumberNAI = 2;
    validator.resetEndpointData(testData);

    validator.expect('subscriberNumberNAI').toBe(2);
    validator.expect('subscriberNumberNAI').isValid(true);

    vWrapper.setData({
      newOperatorFormat: {
        prefix: '84',
        length: 7,
      },
    });

    // Cannot add a duplicate operator prefix/length
    // VUELIDATE NOT IMPORTED PROPERLY --- REQUIRES WORK!
    // expect(vWrapper.find('#operator-format-add').attributes('disabled')).toBe('true');
    const hasDuplicates = !vWrapper.vm.$v.newOperatorFormat.noDuplicates;
    expect(hasDuplicates).toBe(true);

    vWrapper.setData({
      newOperatorFormat: {
        prefix: '85',
        length: 9,
      },
    });

    vWrapper.vm.$nextTick(() => {
      // CAN add non-duplicate
      expect(vWrapper.find('#operator-format-add').attributes('disabled')).toBe(undefined);
      expect(vWrapper.vm.$v.newOperatorFormat.noDuplicates).toBe(true);

      vWrapper.vm.addOperatorFormat();
      vWrapper.vm.$nextTick(() => {
        vWrapper.vm.onValueChange();
        vWrapper.vm.$nextTick(() => {
          const { operatorFormats } = vWrapper.vm.endpoint;
          // Expect HIGHEST LENGTH first...
          expect(operatorFormats[0].length).toBe(9);

          testData.operatorFormats = [];
          testData.prefixes.national = '0';
          validator.resetEndpointData(testData);

          // If empty, then Not valid if ANY prefixes filled in
          validator.expect('operatorFormats').isValid(false);
          testData.prefixes.national = '';
          testData.prefixes.country = '2';
          validator.resetEndpointData(testData);
          // still not valid if missing prefixes
          validator.expect('operatorFormats').isValid(false);
        });
      });
    });
  });
});
