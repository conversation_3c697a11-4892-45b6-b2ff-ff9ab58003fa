const Redis = require('ioredis');

const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
}

class RedisClient {
  constructor(options = { host: redisConfig.host, port: redisConfig.port }) {
    this.client = new Redis(options);
    
    this.client.on('connect', () => {
      console.log('Connected to Redis');
    });

    this.client.on('error', (err) => {
      console.error('Redis connection error:', err);
    });
  }

  getClient() {
    return this.client;
  }

  close() {
    this.client.quit();
    console.log('Redis connection closed');
  }
}

const redisClient = new RedisClient();
module.exports = redisClient.getClient();