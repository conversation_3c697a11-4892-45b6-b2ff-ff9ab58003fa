## Release Summary
| Property     | Value          |
| ------------ | -------------- |
| Product      | [[CoaleSCE]]   | 
| Release Date | TBD |
| Version      | 0.5.2        |
| Build Number | TBD |
| Status       | #draft         |

### ![:star2:](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/1f31f.png) New Features
- None

###  ![:sparkles:](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/2728.png) Enhancements
- Logs relating to external (EXT) connections (ucip/hux/crediverse)
  - Now show duration to 3 decimal (milliseconds) places instead of 10
  - Now include 'endpoint' or _destination url_ for all relevant connections
  - Now report Network errors (timeout, connrefused, etc) and HTTP errors (400, 404, 500, etc) in these log lines
- UCIP CAD Library
  - responseCode 100 and 999 will now cycle AIR connections

###  ![:bug:](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/1f41b.png) Bug Fixes
- UCIP CAD Library
  - XMLRPC Errors now correctly cycle AIR connections
  - Any network errors (timeout, econnrefused, etc) will now cycle AIR connections
- Corrected GUI bug where 'NAI' must be of XML type 'i4' (Integer), but it was being set to 'string'. This bug caused AIR connection cycling to never succeed
- subject:undefined (for EXT logs) now puts the subject of the request (usually subscriber number) correctly in the EXT log lines for UCIP connections

###  ![:wrench:](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/1f527.png) Internal
- CAD Network (axios) Errors are now abstracted to a `CoalesceRequestError` type. The full axios error is retained in the `innerError` parameter of the Error object (promotes improved error handling)

###  ![:warning:](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/26a0-fe0f.png) Advisory
- IMPORTANT: See Developer Notes for mitigating failures inside of CAD components
