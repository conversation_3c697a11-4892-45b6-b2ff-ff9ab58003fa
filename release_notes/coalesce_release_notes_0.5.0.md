## Release Summary
| Property     | Value          |
| ------------ | -------------- |
| Product      | [[CoaleSCE]]   | 
| Release Date | 2023-09-08|
| Version      | 0.5.0      |
| Build Number | 5791176302    |
| Status       | #final      |

### ![:star2:](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/1f31f.png) New Features
- None

###  ![:sparkles:](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/2728.png)Enhancements
- CoaleSCE menu modules can use a component which supports an `unlimitedUSSDResponseStringLength` execution context flag to bypass the response length limit enforcement when the flag is set to `true`. By default the flag is not set.
- The UCIP component supports using the `updateOffer` AIR call from a menu module

###  ![:bug:](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/1f41b.png) Bug Fixes
- None

###  ![:wrench:](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/1f527.png) Internal
- A new component to set the `unlimitedUSSDResponseStringLength` flag  is used in the SSAPI menu module.

###  ![:warning:](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/26a0-fe0f.png) Advisory
- None
