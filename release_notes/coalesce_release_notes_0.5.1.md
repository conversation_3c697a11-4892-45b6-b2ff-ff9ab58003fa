## Release Summary
| Property     | Value          |
| ------------ | -------------- |
| Product      | [[CoaleSCE]]   | 
| Release Date | TBD |
| Version      | 0.5.1        |
| Build Number | TBD |
| Status       | #draft         |

### ![:star2:](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/1f31f.png) New Features
- None

###  ![:sparkles:](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/2728.png)Enhancements
- All entry and exit points for incoming USSD requests in the Menu Server now have INFO logging for all relevant information, including the 'duration' in milliseconds for each USSD request, and each external request made by CoaleSCE Components

###  ![:bug:](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/1f41b.png) Bug Fixes
- Removed manually managed NodeJS garbage collection (which caused performence degredation)
- No longer crashes when changing the log level to an invalid value in the Menu Server

###  ![:wrench:](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/1f527.png) Internal
- Headers and KeepAlive timeouts have returned to default values (5s and 45s respectively)

###  ![:warning:](https://a.slack-edge.com/production-standard-emoji-assets/14.0/apple-medium/26a0-fe0f.png) Advisory
- None

