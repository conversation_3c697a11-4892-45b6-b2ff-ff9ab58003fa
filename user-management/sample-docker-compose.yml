version: '3.4'
services:

  cellid:
    image: "docker.csys.eu.org/user-management:latest"
    container_name: user-management
    entrypoint: npm start
    restart: unless-stopped
    ports:
      - '50001:3000'
    #volumes:
    #  - '/srv/db/cellid:/var/opt/cellid'
    networks:
      default:
        aliases:
          - cellid
    environment:
      - 'DB_URL=mysql://root:sgu9fjexsm2hy9NJQj7SKYadu@mariadb:3306/users'
      - 'HTTP_BASIC_AUTH=test:test123'

networks:
  default:
    driver: bridge
