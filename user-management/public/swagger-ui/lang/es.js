'use strict';

/* jshint quotmark: double */
window.SwaggerTranslator.learn({
    "Warning: Deprecated":"Advertencia: Obsoleto",
    "Implementation Notes":"Notas de implementación",
    "Response Class":"Clase de la Respuesta",
    "Status":"Status",
    "Parameters":"Parámetros",
    "Parameter":"Parámetro",
    "Value":"Valor",
    "Description":"Descripción",
    "Parameter Type":"Tipo del Parámetro",
    "Data Type":"Tipo del Dato",
    "Response Messages":"Mensajes de la Respuesta",
    "HTTP Status Code":"Código de Status HTTP",
    "Reason":"Razón",
    "Response Model":"Modelo de la Respuesta",
    "Request URL":"URL de la Solicitud",
    "Response Body":"Cuerpo de la Respuesta",
    "Response Code":"Código de la Respuesta",
    "Response Headers":"Encabezados de la Respuesta",
    "Hide Response":"Ocultar Respuesta",
    "Try it out!":"<PERSON>ru<PERSON>balo!",
    "Show/Hide":"Mostrar/Ocultar",
    "List Operations":"Listar Operaciones",
    "Expand Operations":"Expandir Operaciones",
    "Raw":"Crudo",
    "can't parse JSON.  Raw result":"no puede parsear el JSON.  Resultado crudo",
    "Example Value":"Valor de Ejemplo",
    "Model Schema":"Esquema del Modelo",
    "Model":"Modelo",
    "apply":"aplicar",
    "Username":"Nombre de usuario",
    "Password":"Contraseña",
    "Terms of service":"Términos de Servicio",
    "Created by":"Creado por",
    "See more at":"Ver más en",
    "Contact the developer":"Contactar al desarrollador",
    "api version":"versión de la api",
    "Response Content Type":"Tipo de Contenido (Content Type) de la Respuesta",
    "fetching resource":"buscando recurso",
    "fetching resource list":"buscando lista del recurso",
    "Explore":"Explorar",
    "Show Swagger Petstore Example Apis":"Mostrar Api Ejemplo de Swagger Petstore",
    "Can't read from server.  It may not have the appropriate access-control-origin settings.":"No se puede leer del servidor. Tal vez no tiene la configuración de control de acceso de origen (access-control-origin) apropiado.",
    "Please specify the protocol for":"Por favor, especificar el protocola para",
    "Can't read swagger JSON from":"No se puede leer el JSON de swagger desde",
    "Finished Loading Resource Information. Rendering Swagger UI":"Finalizada la carga del recurso de Información. Mostrando Swagger UI",
    "Unable to read api":"No se puede leer la api",
    "from path":"desde ruta",
    "server returned":"el servidor retornó"
});
