!function(e,t){function n(e){return"string"==typeof e}function r(e){var t=g.call(arguments,1);return function(){return e.apply(this,t.concat(g.call(arguments)))}}function o(e){return e.replace(/^[^#]*#?(.*)$/,"$1")}function a(e){return e.replace(/(?:^[^?#]*\?([^#]*).*$)?.*/,"$1")}function i(r,o,a,i,c){var u,s,p,h,d;return i!==f?(p=a.match(r?/^([^#]*)\#?(.*)$/:/^([^#?]*)\??([^#]*)(#?.*)/),d=p[3]||"",2===c&&n(i)?s=i.replace(r?R:E,""):(h=l(p[2]),i=n(i)?l[r?A:w](i):i,s=2===c?i:1===c?e.extend({},i,h):e.extend({},h,i),s=b(s),r&&(s=s.replace(m,y))),u=p[1]+(r?"#":s||!p[1]?"?":"")+s+d):u=o(a!==f?a:t[S][q]),u}function c(e,t,r){return t===f||"boolean"==typeof t?(r=t,t=b[e?A:w]()):t=n(t)?t.replace(e?R:E,""):t,l(t,r)}function u(t,r,o,a){return n(o)||"object"==typeof o||(a=o,o=r,r=f),this.each(function(){var n=e(this),i=r||v()[(this.nodeName||"").toLowerCase()]||"",c=i&&n.attr(i)||"";n.attr(i,b[t](c,o,a))})}var f,s,l,p,h,d,v,m,g=Array.prototype.slice,y=decodeURIComponent,b=e.param,$=e.bbq=e.bbq||{},x=e.event.special,j="hashchange",w="querystring",A="fragment",N="elemUrlAttr",S="location",q="href",C="src",E=/^.*\?|#.*$/g,R=/^.*\#/,U={};b[w]=r(i,0,a),b[A]=s=r(i,1,o),s.noEscape=function(t){t=t||"";var n=e.map(t.split(""),encodeURIComponent);m=new RegExp(n.join("|"),"g")},s.noEscape(",/"),e.deparam=l=function(t,n){var r={},o={"true":!0,"false":!1,"null":null};return e.each(t.replace(/\+/g," ").split("&"),function(t,a){var i,c=a.split("="),u=y(c[0]),s=r,l=0,p=u.split("]["),h=p.length-1;if(/\[/.test(p[0])&&/\]$/.test(p[h])?(p[h]=p[h].replace(/\]$/,""),p=p.shift().split("[").concat(p),h=p.length-1):h=0,2===c.length)if(i=y(c[1]),n&&(i=i&&!isNaN(i)?+i:"undefined"===i?f:o[i]!==f?o[i]:i),h)for(;l<=h;l++)u=""===p[l]?s.length:p[l],s=s[u]=l<h?s[u]||(p[l+1]&&isNaN(p[l+1])?{}:[]):i;else e.isArray(r[u])?r[u].push(i):r[u]!==f?r[u]=[r[u],i]:r[u]=i;else u&&(r[u]=n?f:"")}),r},l[w]=r(c,0),l[A]=p=r(c,1),e[N]||(e[N]=function(t){return e.extend(U,t)})({a:q,base:q,iframe:C,img:C,input:C,form:"action",link:q,script:C}),v=e[N],e.fn[w]=r(u,w),e.fn[A]=r(u,A),$.pushState=h=function(e,r){n(e)&&/^#/.test(e)&&r===f&&(r=2);var o=e!==f,a=s(t[S][q],o?e:{},o?r:2);t[S][q]=a+(/#/.test(a)?"":"#")},$.getState=d=function(e,t){return e===f||"boolean"==typeof e?p(e):p(t)[e]},$.removeState=function(t){var n={};t!==f&&(n=d(),e.each(e.isArray(t)?t:arguments,function(e,t){delete n[t]})),h(n,2)},x[j]=e.extend(x[j],{add:function(t){function n(e){var t=e[A]=s();e.getState=function(e,n){return e===f||"boolean"==typeof e?l(t,e):l(t,n)[e]},r.apply(this,arguments)}var r;return e.isFunction(t)?(r=t,n):(r=t.handler,void(t.handler=n))}})}(jQuery,this),function(e,t,n){function r(e){return e=e||t[i][u],e.replace(/^[^#]*#?(.*)$/,"$1")}var o,a=e.event.special,i="location",c="hashchange",u="href",f=e.browser,s=document.documentMode,l=f.msie&&(s===n||s<8),p="on"+c in t&&!l;e[c+"Delay"]=100,a[c]=e.extend(a[c],{setup:function(){return!p&&void e(o.start)},teardown:function(){return!p&&void e(o.stop)}}),o=function(){function n(){f=s=function(e){return e},l&&(a=e('<iframe src="javascript:0"/>').hide().insertAfter("body")[0].contentWindow,s=function(){return r(a.document[i][u])},(f=function(e,t){if(e!==t){var n=a.document;n.open().close(),n[i].hash="#"+e}})(r()))}var o,a,f,s,p={};return p.start=function(){if(!o){var a=r();f||n(),function l(){var n=r(),p=s(a);n!==a?(f(a=n,p),e(t).trigger(c)):p!==a&&(t[i][u]=t[i][u].replace(/#.*/,"")+"#"+p),o=setTimeout(l,e[c+"Delay"])}()}},p.stop=function(){a||(o&&clearTimeout(o),o=0)},p}()}(jQuery,this);