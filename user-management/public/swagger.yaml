swagger: "2.0"
info:
  version: 1.0.0
  title: User Management
  license:
    name: Private
basePath: /
schemes:
  - http
consumes:
  - application/json
produces:
  - application/json
securityDefinitions:
  legacy_auth:
    type: basic
  apikey_auth:
    type: apiKey
    in: query
    name: api-key
paths:
  /uid/{userId}:
    get:
      summary: Info for a specific user
      operationId: getUserById
      tags:
        - user
      parameters:
        - $ref: '#/parameters/userId'
      responses:
        200:
          description: Expected response to a valid request
          schema:
            $ref: '#/definitions/ResponseUser'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/Response'
        default:
          description: unexpected error
          schema:
            $ref: '#/definitions/Error'
    put:
      summary: Update a specific user
      operationId: updateUserById
      tags:
        - user
      security:
        - legacy_auth: []
      parameters:
        - $ref: '#/parameters/userId'
        - $ref: '#/parameters/userInfo'
      responses:
        200:
          description: Expected response to a valid request
          schema:
            $ref: '#/definitions/Response'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/Response'
        default:
          description: unexpected error
          schema:
            $ref: '#/definitions/Error'
    delete:
      summary: Delete a user
      operationId: deleteUserById
      tags:
        - user
      security:
        - legacy_auth: []
      parameters:
        - $ref: '#/parameters/userId'
      responses:
        200:
          description: Expected response to a valid request
          schema:
            $ref: '#/definitions/Response'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/Response'
        default:
          description: Unexpected error
          schema:
            $ref: '#/definitions/Error'
  /user/{userEmail}:
    get:
      summary: Info for a specific user
      operationId: getUserByEmail
      tags:
        - user
      parameters:
        - $ref: '#/parameters/userEmail'
      responses:
        200:
          description: Expected response to a valid request
          schema:
            $ref: '#/definitions/ResponseUser'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/Response'
        default:
          description: unexpected error
          schema:
            $ref: '#/definitions/Error'
    put:
      summary: Update a specific user
      operationId: updateUserByEmail
      tags:
        - user
      security:
        - legacy_auth: []
      parameters:
        - $ref: '#/parameters/userEmail'
        - $ref: '#/parameters/userInfo'
      responses:
        200:
          description: Expected response to a valid request
          schema:
            $ref: '#/definitions/Response'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/Response'
        default:
          description: unexpected error
          schema:
            $ref: '#/definitions/Error'
    delete:
      summary: Delete a user
      operationId: deleteUserByEmail
      tags:
        - user
      security:
        - legacy_auth: []
      parameters:
        - $ref: '#/parameters/userEmail'
      responses:
        200:
          description: Expected response to a valid request
          schema:
            $ref: '#/definitions/Response'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/Response'
        default:
          description: Unexpected error
          schema:
            $ref: '#/definitions/Error'
  /user:
    post:
      summary: Invite a user.
      tags:
        - user
      security:
        - legacy_auth: []
      consumes:
        - application/json
      parameters:
        - in: body
          name: user
          description: The user to create.
          schema:
            $ref: '#/definitions/InviteUserRequest'
      responses:
        200:
          description: Expected response to a valid request
          schema:
            $ref: '#/definitions/Response'
        401:
          description: Authorization headers is missing
          schema:
            $ref: '#/definitions/Error'
        403:
          description: Access forbidden. Invalid credentials
          schema:
            $ref: '#/definitions/Error'
parameters:
  userId:
    name: userId
    in: path
    description: 'The id of the user, i.e. 123456789'
    required: true
    type: integer
    format: int64
  userEmail:
    name: userEmail
    in: path
    description: 'The email of the user, i.e. <EMAIL>'
    required: true
    type: string
  userInfo:
    name: userInfo
    in: body
    description: The user to create.
    required: true
    schema:
      $ref: '#/definitions/UpdateUserRequest'

definitions:

  UserId:
    properties:
      id:
        type: integer
        format: int64
        example: 123456789

  UserEmail:
    properties:
      email:
        type: string
        example: '<EMAIL>'

  UserPassword:
    properties:
      password:
        type: string
        example: 'mysecretpassword'

  UserFirstName:
    properties:
      firstName:
        type: string
        example: 'John'

  UserLastName:
    properties:
      lastName:
        type: string
        example: 'Smith'

  UserCommon:
    required:
      - id
      - email
    properties:
      id:
        type: integer
        format: int64
        example: 123456789
      email:
        type: string
        example: '<EMAIL>'
 
  User:
    required:
      - id
      - email
    properties:
      id:
        type: integer
        format: int32
        example: 123456789
      email:
        type: string
        example: '<EMAIL>'
      firstname:
        type: string
        example: 'John'
      lastname:
        type: string
        example: 'Smith'
      role:
        type: integer
        format: int32
      active:
        type: boolean
      position:
        type: string
        example: 'Administrator'
      mobile_code:
        type: string
        example: '+359'
      mobile_number:
        type: string
        example: '888123456'
 
  ResponseHeader:
    required:
      - code
      - message
    properties:
      code:
        type: integer
        format: int32
      message:
        type: string

  Response:
    required:
      - code
      - message
      - user
    properties:
      code:
        type: integer
        format: int32
      message:
        type: string
      user:
        $ref: '#/definitions/UserCommon'

  ResponseUser:
    required:
      - code
      - message
      - user
    properties:
      code:
        type: integer
        format: int32
      message:
        type: string
      user:
        $ref: '#/definitions/User'

  UpdateUserRequest:
    type: object
    properties:
      firstname:
        type: string
        example: 'John'
      lastname:
        type: string
        example: 'Smith'
      password:
        type: string
        example: 'mysecretpassword'
      role:
        type: integer
        format: int32
      active:
        type: boolean
      position:
        type: string
        example: 'Administrator'
      mobile_code:
        type: string
        example: '+359'
      mobile_number:
        type: string
        example: '888123456'

  InviteUserRequest:
    type: object
    required:
      - email
    properties:
      email:
        type: string
        example: '<EMAIL>'
      firstname:
        type: string
        example: 'John'
      lastname:
        type: string
        example: 'Smith'
      password:
        type: string
        example: 'mysecretpassword'
      role:
        type: integer
        format: int32
      active:
        type: boolean
      position:
        type: string
        example: 'Administrator'
      mobile_code:
        type: string
        example: '+359'
      mobile_number:
        type: string
        example: '888123456'

  Error:
    required:
      - code
      - message
    properties:
      code:
        type: integer
        format: int32
      message:
        type: string
