swagger: "2.0"
info:
  version: 1.0.0
  title: User Management
  license:
    name: Private
basePath: /
schemes:
  - http
consumes:
  - application/json
produces:
  - application/json
securityDefinitions:
  legacy_auth:
    type: basic
  apikey_auth:
    type: apiKey
    in: query
    name: api-key
paths:
  /uid/{userId}:
    get:
      summary: Info for a specific user
      operationId: getUserById
      tags:
        - user
      parameters:
        - $ref: '#/parameters/userId'
      responses:
        200:
          description: Expected response to a valid request
          schema:
            $ref: '#/definitions/ResponseUser'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/Response'
        default:
          description: unexpected error
          schema:
            $ref: '#/definitions/Error'
    put:
      summary: Update a specific user
      operationId: updateUserById
      tags:
        - user
      security:
        - legacy_auth: []
      parameters:
        - $ref: '#/parameters/userId'
        - $ref: '#/parameters/userInfo'
      responses:
        200:
          description: Expected response to a valid request
          schema:
            $ref: '#/definitions/Response'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/Response'
        default:
          description: unexpected error
          schema:
            $ref: '#/definitions/Error'
    delete:
      summary: Delete a user
      operationId: deleteUserById
      tags:
        - user
      security:
        - legacy_auth: []
      parameters:
        - $ref: '#/parameters/userId'
      responses:
        200:
          description: Expected response to a valid request
          schema:
            $ref: '#/definitions/Response'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/Response'
        default:
          description: Unexpected error
          schema:
            $ref: '#/definitions/Error'
  /user/{userUri}:
    get:
      summary: Info for a specific user
      operationId: getUserByUri
      tags:
        - user
      parameters:
        - $ref: '#/parameters/userUri'
      responses:
        200:
          description: Expected response to a valid request
          schema:
            $ref: '#/definitions/ResponseUser'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/Response'
        default:
          description: unexpected error
          schema:
            $ref: '#/definitions/Error'
    put:
      summary: Update a specific user
      operationId: updateUserByUri
      tags:
        - user
      security:
        - legacy_auth: []
      parameters:
        - $ref: '#/parameters/userUri'
        - $ref: '#/parameters/userInfo'
      responses:
        200:
          description: Expected response to a valid request
          schema:
            $ref: '#/definitions/Response'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/Response'
        default:
          description: unexpected error
          schema:
            $ref: '#/definitions/Error'
    delete:
      summary: Delete a user
      operationId: deleteUserByUri
      tags:
        - user
      security:
        - legacy_auth: []
      parameters:
        - $ref: '#/parameters/userUri'
      responses:
        200:
          description: Expected response to a valid request
          schema:
            $ref: '#/definitions/Response'
        404:
          description: User not found
          schema:
            $ref: '#/definitions/Response'
        default:
          description: Unexpected error
          schema:
            $ref: '#/definitions/Error'
  /user:
    post:
      summary: Invite a user.
      tags:
        - user
      security:
        - legacy_auth: []
      consumes:
        - application/json
      parameters:
        - in: body
          name: user
          description: The user to create.
          schema:
            $ref: '#/definitions/InviteUserRequest'
      responses:
        200:
          description: Expected response to a valid request
          schema:
            $ref: '#/definitions/Response'
        401:
          description: Authorization headers is missing
          schema:
            $ref: '#/definitions/Error'
        403:
          description: Access forbidden. Invalid credentials
          schema:
            $ref: '#/definitions/Error'
parameters:
  userId:
    name: userId
    in: path
    description: 'The id of the user, i.e. 123456789'
    required: true
    type: integer
    format: int64
  userUri:
    name: userUri
    in: path
    description: 'The URI of the user, i.e. <EMAIL>'
    required: true
    type: string
  userInfo:
    name: userInfo
    in: body
    description: The user to create.
    required: true
    schema:
      $ref: '#/definitions/UpdateUserRequest'

definitions:

  UserId:
    properties:
      id:
        type: integer
        format: int64
        example: 123456789

  UserUri:
    properties:
      uri:
        type: string
        example: '<EMAIL>'

  UserPassword:
    properties:
      password:
        type: string
        example: 'mysecretpassword'

  UserName:
    properties:
      name:
        type: string
        example: 'John Smith'

  UserCommon:
    required:
      - id
      - uri
    allOf:
      - $ref: '#/definitions/UserId'
      - $ref: '#/definitions/UserUri'

  UserInfo:
    allOf:
      - $ref: '#/definitions/UserName'
    properties:
      role:
        type: integer
        format: int32
      active:
        type: boolean
  
  User:
    allOf:
      - $ref: '#/definitions/UserCommon'
      - $ref: '#/definitions/UserInfo'
 
  ResponseHeader:
    required:
      - code
      - message
    properties:
      code:
        type: integer
        format: int32
      message:
        type: string

  Response:
    required:
      - user
    allOf:
      - $ref: '#/definitions/ResponseHeader'
    properties:
      user:
        $ref: '#/definitions/UserCommon'

  ResponseUser:
    required:
      - user
    allOf:
      - $ref: '#/definitions/ResponseHeader'
    properties:
      user:
        $ref: '#/definitions/User'

  UpdateUserRequest:
    type: object
    allOf:
      - $ref: '#/definitions/UserName'
      - $ref: '#/definitions/UserPassword'
    properties:
      role:
        type: integer
        format: int32
      active:
        type: boolean

  InviteUserRequest:
    type: object
    required:
      - uri
    allOf:
      - $ref: '#/definitions/UserUri'
      - $ref: '#/definitions/UpdateUserRequest'

  Error:
    allOf:
      - $ref: '#/definitions/ResponseHeader'
