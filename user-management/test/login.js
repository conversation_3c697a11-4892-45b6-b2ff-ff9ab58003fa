#!/usr/bin/env node

const grpc = require('grpc');
const protoLoader = require('@grpc/proto-loader');
const path = require('path');
const jwt = require('jwt-simple');

const PROTO_PATH = path.join(__dirname, '../server/proto/spec.proto');
const localConfig = require(path.join(__dirname, '../server/config/local.json'));

const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
  keepCase: true,
  longs: Number,
  enums: Number,
  defaults: false,
  oneofs: true,
});

const proto = grpc.loadPackageDefinition(packageDefinition);
const client = new proto.UserManagement('127.0.0.1:5001', grpc.credentials.createInsecure());

const EMAIL = '<EMAIL>';
const PASS = 'er1c$$on';
const KEY = Buffer.from(localConfig.grpc.bekey, 'hex');

console.log(KEY.toString('hex'));

function login(email, password, callback) {
  const req = { email, password };
  console.debug('Sending get reuqets\n', req);

  const credentials = jwt.encode(req, KEY);
  console.log(credentials);

  client.login({ credentials: { email: EMAIL, password: PASS } }, function (err, r) {
    if (r.status > 0) {
      console.error('Failed to create user');
      grpc.closeClient(client);
      return;
    }

    console.debug('Login user response:\n', r);
    if (callback) callback(r);
  });
}

//create();
login(EMAIL, PASS);

module.exports = { login };
