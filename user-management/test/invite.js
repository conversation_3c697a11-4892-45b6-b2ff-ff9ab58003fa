#!/usr/bin/env node


const fs = require('fs');
const path = require('path');
const lib  = require(path.join(__dirname, 'client'));



const EMAIL = '<EMAIL>';
const PASS = 'er1c$$on';



async function test(email, password) {
	const client = new lib.Client();

	const r = await client.login(email, password);
	
	const user = await client.invite({ 
		email: '<EMAIL>' }, r.token);
	console.log(user);
}

test(EMAIL, PASS);