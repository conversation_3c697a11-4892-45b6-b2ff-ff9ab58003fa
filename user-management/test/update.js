#!/usr/bin/env node

const path = require('path');
const lib  = require(path.join(__dirname, 'client'));

const EMAIL = '<EMAIL>';
const PASS = 'er1c$$on';


async function test(email, passwd) {
	const client = new lib.Client();

	const r = await client.login(email, passwd);
	
	//const user = await client.update({ email, password: '123456789', current_password: passwd }, r.token);
	const user = await client.update({ email, state: 'Inactive', info: '{ "a": 123, "b": 666 }' }, r.token);
	console.log(user);
}

test(EMAIL, PASS);
