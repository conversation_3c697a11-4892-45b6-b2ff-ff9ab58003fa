#!/usr/bin/env node

const grpc = require('grpc');
const protoLoader = require('@grpc/proto-loader');
const path = require('path');

const PROTO_PATH = path.join(__dirname, '../server/proto/spec.proto');

const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
  keepCase: true,
  longs: Number,
  enums: String,
  defaults: false,
  oneofs: true,
});

const proto = grpc.loadPackageDefinition(packageDefinition);

class Client {
  constructor() {
    this.client = new proto.UserManagement('127.0.0.1:5001', grpc.credentials.createInsecure());
  }

  async login(email, password) {
    return new Promise((resolve, reject) => {
      this.client.login({ credentials: { email, password } }, (err, r) => {
        if (r.status > 0) {
          console.error('Failed to create user');
          grpc.closeClient(client);
          reject(err);
          return;
        }

        console.log('LOGIN response\n', r);
        resolve(r);
      });
    });
  }

  async get(email, token) {
    return new Promise((resolve, reject) => {
      this.client.get({ token, user: { email } }, (err, r) => {
        if (r.status > 0) {
          console.error('Failed to get user');
          grpc.closeClient(client);
          reject(err);
          return;
        }

        resolve(r);
      });
    });
  }

  async update(user, token) {
    return new Promise((resolve, reject) => {
      this.client.update({ token, user }, (err, r) => {
        if (r.status > 0) {
          console.error('Failed to update user');
          grpc.closeClient(client);
          reject(err);
          return;
        }

        resolve(r);
      });
    });
  }

  async invite(user, token) {
    return new Promise((resolve, reject) => {
      this.client.create({ token, user }, (err, r) => {
        if (r.status > 0) {
          console.error('Failed to invite user');
          grpc.closeClient(client);
          reject(err);
          return;
        }

        resolve(r);
      });
    });
  }
}

module.exports = { Client };
