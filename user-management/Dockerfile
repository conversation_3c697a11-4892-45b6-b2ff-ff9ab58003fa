FROM node:14.17.1-alpine3.11

# Change working directory
WORKDIR "/app"

RUN apk add --update --no-cache git \
	nodejs-npm \
	python \
	make \
	g++

# Install npm production packages
COPY package.json /app/
RUN cd /app; npm install --production

COPY . /app
RUN cd /app; if [ -f .env.ci ]; then cat .env.ci >> .env; fi;

ENV NODE_ENV production
ENV PORT 5000

ENV GRPC_PORT 5001
ENV DB_URL mysql://root:<EMAIL>/users

EXPOSE 3000

CMD ["npm", "start"]
