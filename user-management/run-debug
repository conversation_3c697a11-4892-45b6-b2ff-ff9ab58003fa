#!/bin/bash

# Simple shell script to run debug mode. If using 6.2 or below use `--debug` otherwise use `--inspect`

CURRENT_NPM_VERSION=$(node -v)
CURRENT_NPM_VERSION=$(node -e "console.log(parseFloat(\"$CURRENT_NPM_VERSION\".replace(\"v\", \" \")).toFixed(1))")
SUPPORT_NPM_INSPECT=$(echo "$CURRENT_NPM_VERSION > 6.2" | bc -l)

if [ "$SUPPORT_NPM_INSPECT" == "1" ]; then
	node --inspect=0.0.0.0:9229 server/server.js
else
	node --debug=0.0.0.0:5858 server/server.js
fi
