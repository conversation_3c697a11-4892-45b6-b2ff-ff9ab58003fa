{"name": "usermanagement", "version": "1.0.0", "description": "A generated IBM Cloud application", "private": true, "engines": {"node": "^14.17.1"}, "config": {"entrypoint": "server/server.js"}, "scripts": {"start": "node $npm_package_config_entrypoint", "start-nodb": "MEMORY_DATABASE=1 node $npm_package_config_entrypoint", "debug": "node --inspect=0.0.0.0:9229 $npm_package_config_entrypoint", "debug:brk": "node --inspect-brk=0.0.0.0:9229 $npm_package_config_entrypoint", "debug:legacy": "node --debug=0.0.0.0:5858 $npm_package_config_entrypoint", "test": "echo \"Error: no test specified\" && exit 0", "dev": "nodemon $npm_package_config_entrypoint", "start:cluster": "sl-run server/server.js", "build": "npm run build:idt", "idt:build": "node idt.js build", "idt:test": "node idt.js test", "idt:debug": "node idt.js debug", "idt:run": "node idt.js run", "idt:deploy": "node idt.js deploy", "idt:install": "node idt.js install"}, "nodemonConfig": {"env": {"NODE_HEAPDUMP_OPTIONS": "nosignal"}}, "dependencies": {"@grpc/proto-loader": "^0.6.2", "body-parser": "^1.19.0", "dotenv": "^16.3.1", "express": "^4.17.1", "grpc": "^1.24.10", "jwt-simple": "^0.5.6", "log4js": "^6.3.0", "mariadb": "^2.5.3"}, "devDependencies": {"@semantic-release/gitlab-config": "^8.0.0", "babel-eslint": "^10.1.0", "eslint": "^7.18.0", "eslint-config-prettier": "^8.3.0", "prettier": "^2.3.1", "semantic-release": "^17.4.4"}}