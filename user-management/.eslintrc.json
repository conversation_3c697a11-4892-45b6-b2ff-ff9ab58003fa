{"env": {"browser": true, "commonjs": true, "es6": true}, "extends": ["prettier"], "plugins": ["prettier"], "globals": {"Atomics": "readonly", "SharedArrayBuffer": "readonly"}, "parserOptions": {"ecmaVersion": 11}, "rules": {"no-debugger": "warn", "comma-dangle": "off", "prettier/prettier": "error", "no-console": "off", "no-use-before-define": "off", "object-shorthand": "off", "no-async-promise-executor": "off", "arrow-body-style": "off", "operator-linebreak": "off", "max-len": ["warn", {"code": 150}], "arrow-parens": "off", "no-restricted-syntax": "off", "no-prototype-builtins": "off", "object-curly-newline": "off", "guard-for-in": "off", "prefer-promise-reject-errors": "off", "no-extend-native": "off", "no-restricted-globals": "off", "no-plusplus": "off", "no-await-in-loop": "off", "func-names": "warn", "prefer-destructuring": "warn", "no-else-return": "off", "no-fallthrough": "warn", "eqeqeq": "warn", "vue/html-self-closing": "off", "vue/max-attributes-per-line": "off", "vue/singleline-html-element-content-newline": "off", "vue/attributes-order": "off", "vue/require-default-prop": "warn", "vue/attribute-hyphenation": "warn", "no-bitwise": "warn", "no-underscore-dangle": "off", "vue/html-closing-bracket-newline": "off", "vue/html-indent": "off", "no-param-reassign": ["warn", {"props": true, "ignorePropertyModificationsFor": ["state"]}], "no-shadow": ["error", {"allow": ["state"]}]}}