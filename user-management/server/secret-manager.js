#!/usr/bin/env node

const crypto = require('crypto');
class secretManager {
  constructor() {
    /*
			EXAMPLE OUTPUT --- SETTING A DB PASSWORD
		let secret = 'er1c$$on';
		let storedSecret = this.createStoredSecret(secret);
		console.log("stored secret: ", storedSecret)
		*/
  }

  salt() {
    let rb = crypto.randomBytes(16);
    return rb.toString('hex').substr(0, 12);
  }

  isValidSecret(plainTextSecret, storedSecret) {
    let [, salt] = storedSecret.split('.');
    // stored should be 'hash' and 'salt' combined... so let's create that from the plaintext... and compare
    let hashedSecret = this.secretHash(plainTextSecret, salt);
    let result = hashedSecret + '.' + salt;

    if (result === storedSecret) return true;

    return false;
  }

  // ---- Throws on error
  secretHash(secret, salt) {
    // eslint-disable-next-line no-param-reassign
    salt = String(salt);
    let keylen = 128;
    let cryptoKey = crypto.pbkdf2Sync(secret, salt, 4096, keylen, 'sha256');
    return cryptoKey.toString('hex');
  }

  createStoredSecret(secret) {
    let salt = this.salt();

    let hashedSecret = this.secretHash(secret, salt);
    let storedSecret = hashedSecret + '.' + salt;

    return storedSecret;
  }
}

module.exports = {
  secretManager,
};

if (require.main === module) {
  test();
}

async function test() {
  const plainText = 'old-secret';
  const sm = new secretManager();

  let storedSecret = sm.createStoredSecret(plainText);

  console.log('Validation Test (is true) ? ', sm.isValidSecret(plainText, storedSecret));
  console.log('Stored Secret: ', storedSecret);
}
