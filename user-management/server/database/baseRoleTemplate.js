//
//  BASE ROLES
//  ----
//  These roles are the 'first-time' roles set into the system, they are used to verify all actions in the Coalesce GUI
//
//  NOTE: Theoretically this is the 'User Management MICRO SERVICE' -- and these permissions should be injected into the service on FIRST START
//        So that this MICRO SERVICE can be populated by any system and re-used anywhere. Of course that's not the case here.
//
//        This MICRO SERVICE is directly linked to the Coalesce GUI and cannot be used separately from it.
//

const baseRoles = [
  {
    name: 'CoaleSCE Admin',
    description: 'Can manage users and "power" user actions - like launching service to production',
    permission: [
      { group: 'user', permissions: ['list', 'invite', 'delete', 'update'] },
      { group: 'permission', permissions: ['list'] },
      { group: 'role', permissions: ['list', 'create', 'delete', 'update'] },
      { group: 'endpoint', permissions: ['list', 'create', 'delete', 'update'] },
      { group: 'component', permissions: ['list', 'create', 'delete', 'update'] },
      { group: 'menu', permissions: ['list', 'create', 'deleteunpushed', 'delete', 'update', 'rename'] },
      { group: 'preprodservice', permissions: ['launch', 'codeedit'] },
      { group: 'prodservice', permissions: ['launch', 'codeedit'] },
      { group: 'globalsettings', permissions: ['list-messages', 'edit-messages', 'edit-general-settings', 'list-general-settings'] },
    ],
  },
  {
    name: 'CoaleSCE App Developer (CAD)',
    description: 'Can manage menus and components and connectors',
    permission: [
      {
        group: 'endpoint',
        permissions: ['list', 'create'],
      },
      {
        group: 'component',
        permissions: ['list', 'create', 'delete', 'update'],
      },
      {
        group: 'menu',
        permissions: ['list', 'create', 'deleteunpushed', 'update', 'rename'],
      },
      {
        group: 'preprodservice',
        permissions: ['launch', 'codeedit'],
      },
      {
        group: 'globalsettings',
        permissions: ['list-messages'],
      },
    ],
  },
  /*
  {
    name: 'everything',
    fullName: 'Everything',
    description: 'Can do anything',
    permission: [
      { group: 'user', permissions: ['list', 'invite', 'delete', 'update'] },
      { group: 'permission', permissions: ['list'] },
      { group: 'role', permissions: ['list', 'create', 'delete', 'update'] },
      { group: 'endpoint', permissions: ['list', 'create', 'delete', 'update'] },
      { group: 'component', permissions: ['list', 'create', 'delete', 'update'] },
      { group: 'menu', permissions: ['list', 'create', 'deleteunpushed', 'delete', 'update', 'rename'] },
      { group: 'preprodservice', permissions: ['launch', 'codeedit'] },
      { group: 'prodservice', permissions: ['launch', 'codeedit'] },
      { group: 'globalsettings', permissions: ['list-messages', 'edit-messages', 'edit-general-settings', 'list-general-settings'] },
    ],
  },
  */
  /*
  {
    name: 'readonly',
    description: 'Can view/list everything, but not update/delete.',
    permission: [
      { group: 'connector', permissions: ['list'] },
      { group: 'component', permissions: ['list'] },
      { group: 'menu', permissions: ['list'] },
      { group: 'user', permissions: ['list'] },
      { group: 'role', permissions: ['list'] },
      { group: 'permission', permissions: ['list'] },
    ],
  },
  */
];

module.exports = {
  baseRoles,
};
