//
//  BASE PERMISSIONS
//  ----
//  These permissions are the 'first-time' permissions set into the system, they are used to verify all actions in the Coalesce GUI
//
//  NOTE: Theoretically this is the 'User Management MICRO SERVICE' -- and these permissions should be injected into the service on FIRST START
//        So that this MICRO SERVICE can be populated by any system and re-used anywhere. Of course that's not the case here.
//
//        This MICRO SERVICE is directly linked to the Coalesce GUI and cannot be used separately from it.
//

const basePermissionGroups = [
  {
    name: 'user',
    fullName: 'User',
    permissions: [
      {
        name: 'list',
        description: 'Can list users',
      },
      {
        name: 'invite',
        description: 'Can invite users that do not already exist',
      },
      {
        name: 'delete',
        description: 'Can delete existing users',
      },
      {
        name: 'update',
        description: 'Can modify other existing users',
      },
    ],
  },
  {
    name: 'role',
    fullName: 'Role',
    permissions: [
      {
        name: 'list',
        description: 'Can list roles',
      },
      {
        name: 'create',
        description: 'Can create new roles with permissions',
      },
      {
        name: 'delete',
        description: 'Can delete roles',
      },
      {
        name: 'update',
        description: 'Can modify roles',
      },
    ],
  },
  {
    name: 'permission',
    fullName: 'Permission',
    permissions: [
      {
        name: 'list',
        description: 'Can list permissions and their groups',
      },
    ],
  },
  {
    name: 'globalsettings',
    fullName: 'Global Settings',
    permissions: [
      {
        name: 'edit-general-settings',
        description: 'Can make changes to global settings',
      },
      {
        name: 'list-general-settings',
        description: 'Can list the global settings',
      },
      {
        name: 'edit-messages',
        description: 'Can make changes to messages',
      },
      {
        name: 'list-messages',
        description: 'Can list the messages',
      },
    ],
  },
  {
    name: 'menu',
    fullName: 'Menu Modules',
    permissions: [
      {
        name: 'list',
        description: 'Can list/view menus',
      },
      {
        name: 'create',
        description: 'Can create a menu module',
      },
      {
        name: 'delete',
        description: 'Can delete a menu module - regardless of whether it is pushed to staging',
      },
      {
        name: 'deleteunpushed',
        description: 'Can delete a menu module that has NOT been pushed to staging',
      },
      {
        name: 'rename',
        description: 'Can rename but not modify menu modules',
      },
      {
        name: 'update',
        description: 'Can modify menu modules',
      },
    ],
  },
  {
    name: 'endpoint',
    fullName: 'Endpoint',
    permissions: [
      {
        name: 'list',
        description: 'Can list/view Connector Endpoints',
      },
      {
        name: 'create',
        description: 'Can create Connector Endpoints',
      },
      {
        name: 'delete',
        description: 'Can delete Connector Endpoints',
      },
      {
        name: 'update',
        description: 'Can modify Cconnector Endpoints',
      },
    ],
  },
  {
    name: 'component',
    fullName: 'Component',
    permissions: [
      {
        name: 'list',
        description: 'Can list/view components',
      },
      {
        name: 'create',
        description: 'Can create a component',
      },
      {
        name: 'delete',
        description: 'Can delete a component',
      },
      {
        name: 'update',
        description: 'Can modify components',
      },
    ],
  },
  {
    name: 'preprodservice',
    fullName: 'Pre-Production Service',
    permissions: [
      {
        name: 'launch',
        description: 'Can push/launch a module to Pre Production',
      },
      {
        name: 'codeedit',
        description: 'Can create, edit, or delete a ussd code for Pre Production modules',
      },
    ],
  },
  {
    name: 'prodservice',
    fullName: 'Production Service',
    permissions: [
      {
        name: 'launch',
        description: 'Can Launch a module to Production',
      },
      {
        name: 'codeedit',
        description: 'Can create, edit, or delete a ussd code for Production modules',
      },
    ],
  },
];

module.exports = {
  basePermissionGroups,
};
