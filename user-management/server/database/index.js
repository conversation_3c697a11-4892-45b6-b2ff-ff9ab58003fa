#!/usr/bin/env node

const mariadb = require('mariadb');
const path = require('path');
const sm = require(path.join(__dirname, '../secret-manager.js'));

const { basePermissionGroups } = require(path.join(__dirname, 'basePermissionTemplate.js'));
const { baseRoles } = require(path.join(__dirname, 'baseRoleTemplate.js'));

const MARIADB_ACQUIRE_TIMEOUT = 5000;
const MARIADB_CONNECTION_LIMIT = 2;

class Database {
  constructor() {
    this.dbname = 'users';
    this.secretManager = new sm.secretManager();

    this.jsonUserFields = ['role', 'info'];
    this.jsonRoleFields = ['permission'];
    this.permissionGroups = [];

    this.basePermissionGroups = basePermissionGroups;
    this.baseRoles = baseRoles;

    // default if no pool is created for the MariaDB connection
    this.pool = { getConnection: () => Promise.reject('No Connection Pool Available!') };
  }

  getValidRoles() {
    return this.baseRoles.map((role) => {
      return role.name;
    });
  }

  async connection() {
    return this.pool.getConnection();
  }

  async query(sql, params) {
    let conn;
    let r;

    try {
      conn = await this.connection();
      r = await conn.query(sql, params);
      conn.release();
    } catch (e) {
      if (conn) conn.release();
      throw e;
    }

    return r;
  }

  async initUsers() {
    const userTable =
      "\
        id int primary key not null auto_increment \
  			, email varchar(128) unique not null \
  			, firstname varchar(64) default '' \
  			, lastname varchar(64) default '' \
  			, password varchar(286) default '88ce7affb4ff1834a019092061da7a5f85b9bd04' \
  			, role varchar(1024) not null \
  			, state enum('Invited', 'Active', 'Inactive', 'Locked') default 'Invited' \
  			, position varchar(64) \
  			, mobile_code varchar(8) \
  			, mobile_number varchar(16) \
  			, picture longtext \
  			, info varchar(4096) default '{\"attempts\":0}' \
  			";
    const userRoles = JSON.stringify([baseRoles[0].name]); // ["CoaleSCE Admin"]

    await this.query(`create table if not exists ${this.dbname}.users(${userTable})`);
    // NOTE:	The password here is 'er1c$$on' - INTENTIONALLY UNHASHED.
    //			Internally, a check is performed for an unhashed password DURING LOGIN, it will be automatically hashed (on successful-login),
    //      Thus, plaintext HERE is for readability :)
    await this.query(
      `insert ignore into ${this.dbname}.users ` +
        `(email,firstname,lastname,password,state,role) ` +
        `values(` +
        `'<EMAIL>',` +
        `'Admin','Coalesce','er1c$$on',` +
        `'Active','${userRoles}')`,
    );
    console.info("Created users table with initial 'Coalesce Admin' user");
  }

  async isUpgrade() {
    let tables = await this.query(`SELECT table_name FROM information_schema.tables WHERE table_schema='${this.dbname}'`);

    const isUpgrade = tables.length > 0;
    if (isUpgrade) {
      console.info('== Performing Upgrade Operations (as needed) ==');
    }
    return isUpgrade;
  }

  async initPermissions(isUpgrade) {
    await this.query(`drop table if exists ${this.dbname}.permission`);

    const permissionTable = (
      'id int primary key not null auto_increment' +
      ', `group` varchar(64) not null' +
      ', name varchar(64) not null' +
      ", description varchar(256) default ''" +
      ', UNIQUE (`group`,name)'
    ).replace(/\s+/, ' ');

    await this.query(`create table ${this.dbname}.permission(${permissionTable})`);

    let permissionData = [];
    this.basePermissionGroups.forEach((group) => {
      const groupName = group.name;
      group.permissions.forEach((permission) => {
        let { name, description } = permission;
        permissionData.push(`('${groupName}','${name}','${description}')`);
      });
    });
    permissionData = permissionData.join(',').replace(/\s+/, ' ');

    await this.query(`insert ignore into ${this.dbname}.permission ` + '(`group`,name,description) ' + `values ${permissionData}`);

    if (isUpgrade) {
      console.debug(`Recreated _base_ permission table (default action for startup)`);
    } else {
      console.info(`Created permission table`);
    }

    console.trace('Filled permission table with relevant _base_ permissions');
  }

  async initRoles() {
    // await this.query(`drop table if exists ${this.dbname}.role`);
    const roleTable =
      "\
			id int primary key not null auto_increment \
			, name varchar(64) unique not null \
			, description varchar(256) default '' \
			, permission varchar(4096) not null \
      ";

    let roleData = [];
    // ---- make SQL insert-able roleData from this.baseRoles
    this.baseRoles.forEach((role, idx) => {
      this.baseRoles[idx].permission = JSON.stringify(role.permission);
      roleData.push(`('${role.name}','${role.description}','${role.permission}')`);
    });
    roleData = roleData.join(',').replace(/\s+/, ' ');

    await this.query(`create table ${this.dbname}.role(${roleTable})`);
    await this.query(`insert ignore into ${this.dbname}.role ` + `(name,description,permission) values ${roleData}`);
    console.info('Created role table');
  }

  getDBSettingsFromURL(url) {
    let settings = {};
    const query = new URL(url);

    [
      //
      settings.host,
      settings.user,
      settings.port,
      settings.password,
    ] = [
      //
      query.hostname,
      query.username,
      parseInt(query.port, 10) || 3306,
      query.password,
    ];

    return settings;
  }

  getDatabaseNameFromURL(url) {
    const query = new URL(url);
    return query.pathname.slice(1);
  }

  createDbPool(settings) {
    this.pool = mariadb.createPool(
      //
      Object.assign(
        {
          acquireTimeout: MARIADB_ACQUIRE_TIMEOUT,
          connectionLimit: MARIADB_CONNECTION_LIMIT,
        },
        settings,
      ),
    );
  }

  async init(url) {
    console.debug('MariaDB initialization');

    const settings = this.getDBSettingsFromURL(url);
    this.dbname = this.getDatabaseNameFromURL(url);
    console.debug('DB credentials\n', { ...settings, password: '*****' });
    console.log('DB name: ', this.dbname);

    this.createDbPool(settings);

    console.info('');
    console.info('==== Database Init ====');

    await this.query(`create database if not exists ${this.dbname}`);

    const isUpgrade = await this.isUpgrade();

    await this.initPermissions(isUpgrade);

    if (!isUpgrade) {
      await this.initUsers();
      await this.initRoles();
      console.info('==== Completed Database Initialization ====');
    } else {
      await this.performUpgradeOperations();
      console.info('==== Upgrade Completed successfully ====');
    }

    console.info('');

    this.permissionGroups = await this.query(`select distinct \`group\` from ${this.dbname}.permission`);
  }

  async performRoleUpgrades() {
    const replaceRoleNames = [
      { old: 'admin', new: 'CoaleSCE Admin' },
      { old: 'developer', new: 'CoaleSCE App Developer (CAD)' },
    ];

    const roleUpdatePromises = [];
    const logs = [];

    replaceRoleNames.forEach((roleReplacement) => {
      const oldRole = roleReplacement.old;
      const newRole = roleReplacement.new;

      logs.push(`Replaced __COUNT__ role(s) '["${oldRole}"]' with newer '["${newRole}"]'`);
      const userPromise = this.query(`UPDATE ${this.dbname}.users SET role=? WHERE role=?`, [
        `\["${roleReplacement.new}"\]`,
        `\["${roleReplacement.old}"\]`,
      ]);
      roleUpdatePromises.push(userPromise);
    });

    await Promise.all(roleUpdatePromises).then((results) => {
      results.forEach((result) => {
        const { affectedRows } = result;
        if (affectedRows > 0) {
          const log = logs.shift();
          console.info(log.replace(/__COUNT__/, affectedRows));
        }
      });
    });
  }

  async replaceInvalidRoles() {
    const validRoles = this.getValidRoles();

    const sqlForFindingInvalidRoles = validRoles.map((validRole) => {
      // Role is LITERALLY stored as a JSON ARRAY ==> ["role_here"]
      //  This is so that multiple roles can be supported in future (array of roles PER USER)
      return `role != '["${validRole}"]'`;
    });

    let sqlUpdateInvalidRolesToCAD = `UPDATE ${this.dbname}.users SET role=? WHERE ${sqlForFindingInvalidRoles.join(' AND ')}`;

    console.trace(`Replacing INVALID roles .... The SQL: ${sqlUpdateInvalidRolesToCAD}`);

    const substituteForInvalid = '["CoaleSCE App Developer (CAD)"]';

    await this.query(sqlUpdateInvalidRolesToCAD, [substituteForInvalid]).then((result) => {
      const { affectedRows } = result;
      if (affectedRows > 0) {
        console.info(`Replaced ${affectedRows} invalid role(s) with ${substituteForInvalid}`);
      }
    });
  }

  async replaceCADPermissions() {
    const row = await this.query(`SELECT permission FROM ${this.dbname}.role WHERE name='CoaleSCE App Developer (CAD)'`);
    const cadPermissions = row[0].permission;

    const cadRole = this.baseRoles.find((role) => role.name === 'CoaleSCE App Developer (CAD)');
    const cadRoleAsString = JSON.stringify(cadRole.permission);

    if (cadPermissions !== cadRoleAsString) {
      console.info('Found inconsistent permissionf or CAD, replacing');
      console.debug('Existing Permissions: ', cadPermissions);
      console.debug('Expected Permissions: ', cadRoleAsString);

      const updateSql = `UPDATE ${this.dbname}.role SET permission='${cadRoleAsString}' WHERE name='CoaleSCE App Developer (CAD)' `;
      console.debug('Performing database replacement of CAD role:: ', JSON.stringify(updateSql, null, 2));
      await this.query(updateSql);
    }
  }

  async performUpgradeOperations() {
    try {
      await this.performRoleUpgrades();
      await this.replaceInvalidRoles();
      await this.replaceCADPermissions();
    } catch (e) {
      console.error('UPGRADE FAILED!! .... Manual intervention required :(');
      console.error('Failure reason ... ');
      console.error(e);
      process.exit(-1);
    }
  }

  ungroupedToGrouped(ungroupedPermissions) {
    // if old exists... replace with new
    const explicitGroupConversion = [{ old: 'service', new: 'preprodservice' }];

    // ---- We must convert from legacy roles to grouped roles in order to insert legacy values into the new database structure
    const { permissionGroups } = this;
    let newPermission = {};
    ungroupedPermissions.forEach((permission, idx) => {
      // explicit group conversion from old to new
      explicitGroupConversion.forEach((group) => {
        const startsWithOldGroup = new RegExp('^' + group.old);
        if (permission.match(startsWithOldGroup)) {
          permission = permission.replace(startsWithOldGroup, group.new);
        }
      });

      let { group } = permissionGroups.find((permissionGroup) => {
        // eslint-disable-next-line no-shadow
        let { group } = permissionGroup;

        const startsWithGroup = new RegExp('^' + group);
        return permission.match(startsWithGroup);
      });

      if (!newPermission[group]) {
        newPermission[group] = [];
      }

      const startsWithGroup = new RegExp('^' + group);

      let permissionOnly = permission.replace(startsWithGroup, '');

      newPermission[group].push(permissionOnly);
    });

    let groupedPermissions = [];

    for (var group in newPermission) {
      groupedPermissions.push({ group, permissions: newPermission[group] });
    }

    return groupedPermissions;
  }

  async createGroupedRole(groupedRole) {
    let roles = await this.listRoleNames();

    if (roles.findIndex((role) => role.name === groupedRole.name) !== -1) {
      throw new Error('Role name already exists!');
    }

    if (!groupedRole.permission || !Array.isArray(groupedRole.permission) || groupedRole.permission.length <= 0) {
      throw new Error('Missing/Invalid permission');
    }

    if (!groupedRole.name || !groupedRole.description || groupedRole.name.length <= 0 || groupedRole.description.length <= 0) {
      throw new Error('Missing/Empty name or description fields');
    }

    await this.areValidGroupedPermissions(groupedRole.permission);

    let duplicates = [];
    groupedRole.permission.forEach((permissionGroup) => {
      if (!duplicates.includes(permissionGroup.group)) {
        duplicates.push(permissionGroup.group);
      } else {
        throw new Error(`Attempting to create a grouped role with duplicate permission groups! (duplicate group: ${permissionGroup.group})`);
      }
    });

    groupedRole.permission = JSON.stringify(groupedRole.permission);

    // create an array of question marks equal in length to the values being inserted into the database
    const placeholder = [];
    const keys = [];
    Object.keys(groupedRole).map((key) => {
      keys.push(key);
      placeholder.push('?');
    });

    return this.query(`insert into ${this.dbname}.role ` + `(${keys.join(',')}) values (${placeholder.join(',')})`, [...Object.values(groupedRole)]);
  }

  async createRole(role) {
    await this.areValidPermissions(role.permission);

    // for backward compatibility .... convert ungrouped content to grouped content before inserting into the database
    role.permission = this.ungroupedToGrouped(role.permission);

    return this.createGroupedRole(role);
  }

  async updateRole(role) {
    await this.areValidPermissions(role.permission);

    // for backward compatibility .... convert ungrouped content to grouped content before performing the update
    role.permission = this.ungroupedToGrouped(role.permission);

    return this.updateGroupedRole(role);
  }

  async deleteRole(role) {
    if (!role || !role.name) {
      throw new Error('No role specified?');
    }
    return this.query(`delete from ${this.dbname}.role where name=?`, [role.name]);
  }

  async updateGroupedRole(groupedRole) {
    // ---- Invalid type?  Then make empty... will cause 'exists' check to fail
    if (typeof groupedRole.name !== 'string') {
      groupedRole.name = '';
    }

    let roles = await this.listRoleNames();
    if (roles.findIndex((role) => role.name.toLowerCase() === groupedRole.name.toLowerCase()) === -1) {
      throw new Error('Role does not exist?');
    }

    if (!groupedRole.permission || !Array.isArray(groupedRole.permission) || groupedRole.permission.length <= 0) {
      throw new Error('Missing/Invalid permission');
    }

    if (!groupedRole.description || groupedRole.description.length <= 0) {
      throw new Error('Missing/Empty name or description fields');
    }

    // ---- Throws on error... accepts array of permissions to check against DB perms
    await this.areValidGroupedPermissions(groupedRole.permission);

    groupedRole.permission = JSON.stringify(groupedRole.permission);

    let { name } = groupedRole;
    delete groupedRole.name; // exclude if from the updated keys in the SQL

    const keys = Object.keys(groupedRole)
      .map((k) => k + '=?')
      .join(',');
    return this.query(`update ${this.dbname}.role set ${keys} where name=?`, [...Object.values(groupedRole), name]);
  }

  async areValidGroupedPermissions(permissionGroupArray) {
    let validPermissionGroups = await this.listGroupedPermissions();
    let isValid = false;

    permissionGroupArray.forEach((permissionGroup) => {
      const { group, permissions } = permissionGroup;
      isValid = validPermissionGroups.findIndex((validGroup) => validGroup.group === group) >= 0;
      if (!isValid) {
        throw new Error("Invalid 'permission group' specified: " + name);
      }

      let validPermissions = validPermissionGroups.filter((validPermissionGroup) => validPermissionGroup.group === group)[0].permissions;

      permissions.forEach((permission) => {
        isValid = validPermissions.findIndex((validPermission) => validPermission.name === permission) >= 0;
        if (!isValid) {
          throw new Error(`Invalid 'permission' specified (for group: ${group}): ` + permission);
        }
      });
    });
  }

  async areValidPermissions(permissionArray) {
    let validPermissions = await this.listPermissions();
    permissionArray.forEach((value, index) => {
      let isValid = false;
      validPermissions.forEach((perm) => {
        if (perm.name === value) isValid = true;
      });
      if (!isValid) {
        throw new Error('Invlid permission specified: ' + value);
      }
    });
  }

  async listGroupedPermissions() {
    const groupResult = await this.query(`select distinct \`group\` from ${this.dbname}.permission`);
    if (!groupResult || !Array.isArray(groupResult) || groupResult.length === 0) return undefined;

    let permissionGroupPromises = [];

    let permissionGroups = [];
    groupResult.forEach((arrayItem) => {
      permissionGroupPromises.push(
        this.query(`SELECT \`group\`, name, description FROM ${this.dbname}.permission WHERE \`group\`=?`, [arrayItem.group]),
      );
      permissionGroups.push(arrayItem.group);
    });

    permissionGroups = await Promise.all(permissionGroupPromises);
    let wipResult = {};

    permissionGroups.forEach((permissionGroup) => {
      permissionGroup.forEach((permissionRow) => {
        let { name, description } = permissionRow;
        if (!wipResult[permissionRow.group]) {
          wipResult[permissionRow.group] = [];
        }

        wipResult[permissionRow.group].push({ name, description });
      });
    });

    let result = [];
    for (var group in wipResult) {
      result.push({ group, permissions: wipResult[group] });
    }

    return result;
  }

  async listPermissions() {
    const groupResult = await this.listGroupedPermissions();
    //const result = await this.query(`select name, description from ${this.dbname}.permission`);
    if (!groupResult || !Array.isArray(groupResult) || groupResult.length === 0) return undefined;

    /* BACKWARD COMPATIBILITY --- change NEW layout to show as OLD layout */
    let result = [];
    groupResult.forEach((group) => {
      group.permissions.forEach((permission) => {
        result.push({ name: group.group + permission.name, description: permission.description });
      });
    });

    return result;
  }

  // ignores permission field
  async listRoleNames() {
    const roles = await this.query(`select name, description from ${this.dbname}.role`);
    if (!roles || !Array.isArray(roles) || roles.length === 0) return undefined;

    return roles;
  }

  async listGroupedRoles() {
    const roles = await this.listRoleNames();
    if (typeof roles === 'undefined') {
      return undefined;
    }

    let result = [];
    await Promise.all(
      roles.map(async (role) => {
        let { name } = role;
        let { description } = role;
        let permission = await this.getGroupedRolesPermissionList([name]);
        result.push({ name, description, permission });
      }),
    );

    return result;
  }

  async listRoles() {
    let groupedListResult = await this.listGroupedRoles();
    if (!groupedListResult || !Array.isArray(groupedListResult) || groupedListResult.length === 0) return undefined;

    let result = [];
    groupedListResult.forEach((role) => {
      let { name, description } = role;
      let permission = [];
      role.permission.forEach((p) => {
        p.permissions = p.permissions.forEach((innerPermission) => {
          permission.push(p.group + innerPermission);
        });
      });
      result.push({ name, description, permission });
    });

    return result;
  }

  async getRolesPermissionList(roleArray) {
    let groupedPermissionList = await this.getGroupedRolesPermissionList(roleArray);

    /* BACKWARD COMPATIBILITY --- change NEW layout to show as OLD layout */
    let result = [];
    groupedPermissionList.forEach((permissionObject) => {
      let groupName = permissionObject.group;
      permissionObject.permissions.forEach((permission) => {
        result.push(groupName + permission);
      });
    });

    return result;
  }

  async getGroupedRolesPermissionList(roleArray) {
    let placeholder = [];

    roleArray = [...new Set(roleArray)]; // ---- remove duplicates ;)

    // ---- Use the size of the roleArray to create the placeholder array of '?' characters
    roleArray.map(() => {
      placeholder.push('?');
    });
    // ---- placeholder should look like this now (?,?,...,?)
    //		where the number of ? characters matches the array length
    placeholder = `(${placeholder.join(',')})`;

    const roleResult = await this.query(`select role.permission from ${this.dbname}.role where name in ${placeholder}`, roleArray);

    // ---- failure? undefined response
    if (!roleResult || !Array.isArray(roleResult)) return undefined;

    // ----	success - but empty role set? ... return empty permission array
    if (roleResult.length <= 0) return [];

    //	----	The returned array should look like this
    //        (IMPORTANT, the value of the arrayItemObject.permission is a STRING, should be JSON.parse'ed):
    //      [
    //        "[{"group":"user","permissions":["list","invite","delete","update"]}, .... ],
    //        "[{"group":"permission","permissions":["list"]}, .... ],
    //        "[{"group":"user","permissions":["list"]}, .... ]                 // example of possible duplicate
    //      ]
    //

    // ---- make sure all results JSON content are parsed;
    roleResult.map((r) => (r.permission = JSON.parse(r.permission)));

    // ---- Let's remove duplicates across entire ALL the SETS of permissions that are returned
    let groups = [];
    roleResult.forEach((row) => {
      row.permission.forEach((permission) => groups.push(permission.group));
    });
    groups = [...new Set([...groups])]; // remove duplicates

    // setup object of grouped Permissions
    let groupedPermissions = {};
    groups.forEach((group) => {
      groupedPermissions[group] = [];
    });

    roleResult.forEach((roleRow) => {
      roleRow.permission.forEach((permissionObject) => {
        let self = groupedPermissions[permissionObject.group];
        groupedPermissions[permissionObject.group] = [...new Set([...self, ...permissionObject.permissions])];
      });
    });

    let result = [];

    for (var group in groupedPermissions) result.push({ group, permissions: groupedPermissions[group] });

    return result;
  }

  async list(limitFields) {
    if (!Array.isArray(limitFields)) limitFields = [];

    limitFields = [...new Set(limitFields)]; // ---- remove duplicates ;)

    let fields = ['id', 'email', 'firstname', 'lastname', 'role', 'state', 'position', 'mobile_code', 'mobile_number', 'picture', 'info'];

    // ---- Remove invalid field names
    limitFields.forEach((value, index) => {
      if (!fields.includes(value)) delete limitFields[index];
    });

    // ---- If we are not explicitly requesting the 'picture' ....
    //      Then - by default - it should NOT be returned - makes for far better efficiency when "listing" users
    //
    if (!limitFields.includes('picture'))
      fields = fields.filter((v) => {
        return v !== 'picture';
      });

    if (limitFields.length !== 0) {
      console.info("Intetional limiting of the fields in 'user.list' request: ", fields);
      fields = limitFields;
    }

    const result = await this.query(`select ${fields.join(',')} from ${this.dbname}.users`);
    if (!result || !Array.isArray(result) || result.length === 0) return undefined;

    // ---- Because specific fields return as JSON, but are stored stringified...
    //      We explicitly parse them into JSON before returning.
    result.forEach((user) => {
      this.jsonUserFields.forEach((element) => {
        user[element] = JSON.parse(user[element]);
      });
    });

    return result;
  }

  async get(id) {
    const result = await this.query(
      `select id` +
        `, email` +
        `, firstname` +
        `, lastname` +
        `, password` +
        `, role` +
        `, state` +
        `, position` +
        `, mobile_code` +
        `, mobile_number` +
        `, picture` +
        `, info` +
        ` from ${this.dbname}.users where id=?`,
      [id],
    );
    if (!result || !Array.isArray(result) || result.length === 0) return undefined;

    // ---- Because specific fields return as JSON, but are stored stringified...
    //      We explicitly parse them into JSON before returning.
    this.jsonUserFields.forEach((element) => {
      result[0][element] = JSON.parse(result[0][element]);
    });

    return result[0];
  }

  async getByEmail(uri) {
    const result = await this.query(
      `select ` +
        `id` +
        `, email` +
        `, firstname` +
        `, lastname` +
        `, password` +
        `, role` +
        `, state` +
        `, position` +
        `, mobile_code` +
        `, mobile_number` +
        `, picture` +
        `, info` +
        ` from ${this.dbname}.users where email=?`,
      [uri],
    );
    if (!result || !Array.isArray(result) || result.length === 0) return undefined;

    // ---- Because specific fields return as JSON, but are stored stringified...
    //      We explicitly parse them into JSON before returning.
    this.jsonUserFields.forEach((element) => {
      result[0][element] = JSON.parse(result[0][element]);
    });

    return result[0];
  }

  async delete(id) {
    return this.query(`delete from ${this.dbname}.users where id=?`, [id]);
  }

  async deleteByEmail(uri) {
    return this.query(`delete from ${this.dbname}.users where email=?`, [uri]);
  }

  async update(id, user) {
    // ---- These types arrive as objects, but must be stored stringified
    this.jsonUserFields.forEach((element) => {
      if (user[element]) user[element] = JSON.stringify(user[element]);
    });

    if (user.password) {
      const passwd = String(user.current_password);

      if (!passwd) {
        throw new Error('Invalid credentials');
      }

      delete user.current_password;
      let dbUser = await this.get(id);

      if (!dbUser) {
        throw new Error('Server Error');
      }

      if (!this.secretManager.isValidSecret(passwd, dbUser.password)) {
        throw new Error('Invalid Password');
      }

      // ---- passwd in plain text validates... so this can be converted to the stored password so the WHERE can succeed
      passwd = dbUser.password;

      user.password = this.secretManager.createStoredSecret(user.password);

      // ---- internal json fields must be stored stringified
      this.jsonUserFields.forEach((element) => {
        if (user[element]) user[element] = JSON.stringify(user[element]);
      });

      const keys = Object.keys(user)
        .map((k) => k + '=?')
        .join(',');

      const r = await this.query(`update ${this.dbname}.users set ${keys} where id=? and password=?`, [...Object.values(user), id, passwd]);

      if (r.affectedRows === 0) {
        throw new Error('Invalid password');
      }
    }

    const keys = Object.keys(user)
      .map((k) => k + '=?')
      .join(',');
    return this.query(`update ${this.dbname}.users set ${keys} where id=?`, [...Object.values(user), id]);
  }

  async updateByEmail(uri, user) {
    // ---- These types arrive as objects, but must be stored stringified
    this.jsonUserFields.forEach((element) => {
      if (user[element]) user[element] = JSON.stringify(user[element]);
    });

    // ---- Updating password? Handle with care...
    if (user.password) {
      let previousPasswd = String(user.current_password);

      if (!previousPasswd) {
        throw new Error('Invalid credentials');
      }

      delete user.current_password;

      let dbUser = await this.getByEmail(uri);
      if (!dbUser) {
        throw new Error('Server Error');
      }

      // ---- if they match as UNHASHED TEXT.... then we FORCE the new password change
      if (previousPasswd === dbUser.password) {
        dbUser.password = this.secretManager.createStoredSecret(user.password);
      }

      if (
        this.secretManager.isValidSecret(previousPasswd, dbUser.password) ||
        (dbUser.info.forgotPassToken && previousPasswd === dbUser.info.forgotPassToken)
      ) {
        // logging in with 'forgot' OTP ? --- then remove it from the DB
        if (dbUser.info.forgotPassToken && previousPasswd === dbUser.info.forgotPassToken) {
          delete dbUser.info.forgotPassToken;
          delete dbUser.info.forgotPassTokenExpiry;
        }

        // Either
        //    forgot password 'forgotPassToken' was used to update the password.....
        //  OR
        //    the previous password validates against the DB password

        // If old and new do not match ..... hash the new password for updating
        if (previousPasswd !== user.password) {
          user.password = this.secretManager.createStoredSecret(user.password);
        } else {
          //  if they match.... do nothing to update the database
          user.password = dbUser.password;
        }
      } else {
        throw new Error('Invalid Password');
      }

      user.info = dbUser.info;

      //      Always update the password -- even if it matches. prevents failures with "plaintext" password updates
      //
      //      if ( user.password !== dbUser.password )
      //      {
      const keys = Object.keys(user)
        .map((k) => k + '=?')
        .join(',');

      const r = await this.query(`update ${this.dbname}.users set ${keys} where email=?`, [...Object.values(user), uri]);
      //console.debug(r);
      //        if (r.affectedRows == 0)
      //throw new Error("Server Error - update ineffective");
      //      }

      let result = await this.query(`select * from ${this.dbname}.users where email=?`, [uri]);

      return result;
    }

    const keys = Object.keys(user)
      .map((k) => k + '=?')
      .join(',');
    return this.query(`update ${this.dbname}.users set ${keys} where email=?`, [...Object.values(user), uri]);
  }

  async insert(newUser) {
    const conn = await this.connection();
    let result;
    let user;

    //console.debug('DB insert:\n', newUser);

    try {
      // ---- convert plain text password to stored hashed.salted db password
      newUser.password = this.secretManager.createStoredSecret(newUser.password);

      this.jsonUserFields.forEach((element) => {
        if (newUser[element]) newUser[element] = JSON.stringify(newUser[element]);
      });

      const placeholders = [];
      Object.keys(newUser).map(() => {
        placeholders.push('?');
      });

      result = await conn.query(
        `insert into ${this.dbname}.users (${Object.keys(newUser).join(',')}) values (${placeholders.join(',')})`,
        Object.values(newUser),
      );

      const id = result.insertId;
      result = await conn.query(
        'select id, email, firstname, lastname, password, role, state, ' +
          `position, mobile_code, mobile_number, picture, info from ${this.dbname}.users where id=?`,
        [id],
      );
      [user] = result;
    } catch (e) {
      console.error('DB: error\n', e);
    }

    conn.release();
    return user;
  }

  async close() {
    return this.pool.end();
  }
}

class TestDatabase {
  constructor() {
    this.users = new Map();
    this.uris = new Map();

    this.insert({
      email: '<EMAIL>',
      firstname: 'Ivan',
      lastname: 'Invanov',
      password: '123',
      role: '["admin"]',
      state: 'Active',
      position: 'Fake user',
      mobile_code: '+100',
      mobile_number: '2233445566',
    });
    this.insert({
      email: '<EMAIL>',
      firstname: 'John',
      lastname: 'Smith',
      password: '123',
      role: '["readonly"]',
      state: 'Active',
      position: 'Test user',
      mobile_code: '+200',
      mobile_number: '2233445577',
    });
    this.insert({
      email: '<EMAIL>',
      firstname: 'John',
      lastname: 'Doe',
      password: '123',
      role: '["readonly"]',
      state: 'Inactive',
      position: 'Test user',
      mobile_code: '+200',
      mobile_number: '2233445577',
    });
  }

  async init() {}

  async get(id) {
    return this.users.get(id);
  }

  async getByEmail(uri) {
    const id = this.uris.get(uri);
    console.info('DB: ', id);
    return id ? this.users.get(id) : null;
  }

  async insert(info) {
    if (this.uris.get(info.email)) return undefined;

    const id = 100001000 + Math.floor(Math.random() * 1000);
    this.uris.set(info.email, id);

    //console.debug('User info: ', info);
    const user = Object.assign({}, info, { id });
    //console.debug('User created: ', user);
    this.users.set(id, user);

    return user;
  }

  async update(id, info) {
    const user = this.users.get(id);
    if (user) {
      Object.assign(user, info);
    }

    return user;
  }

  async updateByEmail(uri, info) {
    const user = await this.getByEmail(uri);
    if (user) {
      //console.debug('DB: user update\n', info);
      //console.debug('DB: user found\n', user);
      Object.assign(user, info);
      //console.debug('DB: user changed\n', user);
      return true;
    }

    return false;
  }
}

module.exports = {
  Database,
  TestDatabase,
};

if (require.main === module) {
  test();
}

async function test() {
  const email = '<EMAIL>';
  const db = new Database();

  await db.init('mysql://root:ussdgw@172.17.0.1/users');
  const u = await db.insert({ email });
  if (u) console.debug('New user:\n', u);

  await db.updateByEmail(email, { firstname: 'Ivan', lastname: 'Ivanov' });

  const user = await db.getByEmail(email);
  console.debug(user);
}
