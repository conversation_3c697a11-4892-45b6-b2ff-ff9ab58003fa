/* eslint-disable class-methods-use-this */
const log4js = require('log4js');
const path = require('path');

log4js.configure(path.join(__dirname, '../config/log4js.json'));

const logger = log4js.getLogger();

const ACCEPTED_LOG_LEVELS = ['debug', 'info', 'warn', 'error', 'fatal'];

Object.defineProperty(console, 'level', {
  set: (newLevel) => {
    const levelUpper = String(newLevel).toUpperCase();
    const levelLower = String(newLevel).toLowerCase();
    if (ACCEPTED_LOG_LEVELS.includes(levelLower)) {
      logger.level = levelUpper;
      logger[levelLower](`Setting Log Level: ${levelUpper}`);
    }
  },
  get: () => {
    return logger.level.levelStr;
  },
});

console.level = 'debug';

console.trace = (...args) => logger.trace(...args);
console.debug = (...args) => logger.debug(...args);
console.log = (...args) => logger.info(...args);
console.info = (...args) => logger.info(...args);
console.warn = (...args) => logger.warn(...args);
console.error = (...args) => logger.error(...args);
console.fatal = (...args) => logger.fatal(...args);

module.exports = console;
