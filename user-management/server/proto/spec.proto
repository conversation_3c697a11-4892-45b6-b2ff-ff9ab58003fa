syntax = "proto3"; 

service UserManagement {
		// ---- USER methods
	rpc create(Request) returns (Response);
	rpc get(Request) returns (Response);
	rpc update(Request) returns (Response);
	rpc delete(Request) returns (Response);
	rpc list(Request) returns (ListResponse);

	rpc setOTP(OtpRequest) returns (Response);
	rpc login(LoginRequest) returns (LoginResponse);

		// ---- ROLE methods
	rpc createRole(RoleRequest) returns (RoleResponse);
	rpc updateRole(RoleRequest) returns (RoleResponse);
	rpc deleteRole(RoleRequest) returns (RoleResponse);	// can delete a GROUPED or NORMAL role - does not return 'role' in the RoleResponse
	rpc listRoles(TokenOnlyRequest) returns (RoleListResponse);
	
	rpc createGroupedRole(GroupedRoleRequest) returns (GroupedRoleResponse);
	rpc updateGroupedRole(GroupedRoleRequest) returns (GroupedRoleResponse);
	rpc listGroupedRoles(TokenOnlyRequest) returns (GroupedRoleListResponse);

		// ---- PERMISSION methods
	rpc listPermissions(TokenOnlyRequest) returns (PermissionListResponse);
	rpc listGroupedPermissions(TokenOnlyRequest) returns (GroupedPermissionListResponse);

	rpc getVersion(Empty) returns (VersionResponse);
}

message Empty {}

message VersionResponse {
	int32 code = 1;
	string message = 2;
	Version version = 3;
}

message Version {
	int32 major = 1;	// grpc sequence numbers, not version numbers ;)
	int32 minor = 2;
}

message TokenOnlyRequest {
	string token = 1;
}

	// ---- role request / response with associated permissions
message RoleRequest {
	string token = 1;
	RoleInfo role = 2;
}
message RoleResponse {
	int32 code = 1;
	string message = 2;
	RoleInfo role = 3;
}
message RoleListResponse {
	int32 code = 1;
	string message = 2;
	repeated RoleInfo roles = 3;
}
message PermissionListResponse {
	int32 code = 1;
	string message = 2;
	repeated Permission permissions = 3;
}
message Permission {
	string name = 1;
	string description = 2;
}

message RoleInfo {
	required string name = 1;	// name and description of role
	string description = 2;
	repeated string permission = 3;
}


message GroupedRoleRequest {
	string token = 1;
	GroupedRoleInfo role = 2;
}
message GroupedRoleResponse {
	int32 code = 1;
	string message = 2;
	GroupedRoleInfo role = 3;
}

message GroupedPermissionListResponse {
	int32 code = 1;
	string message = 2;
	repeated GroupedDescribedPermission permissionGroup = 3;
}
message GroupedRoleListResponse {
	int32 code = 1;
	string message = 2;
	repeated GroupedRoleInfo roles = 3;
}
message GroupedRoleInfo {
	required string name = 1;	// name and description of role
	string description = 2;
	repeated GroupedPermission permission = 3;
}
message GroupedPermission {
	string group = 1;
	repeated string permissions = 2;
}
message GroupedDescribedPermission {
	string group = 1;
	repeated Permission permissions = 2;
}

	// ---- user request / response / list / login
enum State {
	Invited = 1;
	Active = 2;
	Inactive = 3;
	Locked = 4;	

		// ---- This is NOT stored in the database
		//		It is a state used to direct users to the "choose-password" page if they have logged in using a 'password-reset' token
	forgotPassToken = 5;
}

message UserInfo {
	uint32 attempts = 1;
	string forgotPassToken = 2;
	string forgotPassTokenExpiry = 3;
	string more = 4;
}

message UserData {
	uint32 id = 1;
	string email = 2;
	string firstname = 3;
	string lastname = 4;
	string password = 5;
	repeated string role = 6;
	repeated string permission = 7;
	State state = 8;
	string position = 9;
	string mobile_code = 10;
	string mobile_number = 11;
	string picture = 12;
	string current_password = 13;
	UserInfo info = 14;
	repeated GroupedPermission groupedPermission = 15;
}

message ListResponse {
	int32 code = 1;
	string message = 2;
	repeated UserData users = 3;
}

message OtpRequest {
	string email = 1;
}

message Request {
	required string token = 1;
	UserData user = 2;
	string email = 3;
	repeated string fields = 4;	// this is used only when the request is a LIST request.
}

message Response {
	int32 code = 1;
	string message = 2;
	UserData user = 3;
}

message Credentials {
	string email = 1;
	string password = 2;
	bool remember = 3;
}

message LoginRequest {
	string token = 1;
	Credentials credentials = 2;
}

message LoginResponse {
	int32 code = 1;
	string message = 2;
	string token = 3;
	string rememberToken = 4;
	uint64 expires = 5;
	UserData user = 6;
}
