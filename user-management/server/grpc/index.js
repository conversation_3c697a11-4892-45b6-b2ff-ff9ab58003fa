#!/usr/bin/env node

/* eslint-disable no-param-reassign */

const path = require('path');
const grpc = require('grpc');
const protoLoader = require('@grpc/proto-loader');
const jwt = require('jwt-simple');
const crypto = require('crypto');
const { randomUUID } = crypto;

const localConfig = require('../config/local.json');
const PROTO_PATH = path.join(__dirname, '../proto/spec.proto');

const sm = require(path.join(__dirname, '../secret-manager.js'));

const PORT = 5001;

const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
  keepCase: true,
  longs: Number,
  enums: String,
  defaults: false,
  oneofs: true,
});

const protoDescriptor = grpc.loadPackageDefinition(packageDefinition);
const proto = protoDescriptor.UserManagement;

class Server {
  constructor(db, keys) {
    this.userAllowedStates = ['Active', 'Invited'];
    this.db = db;
    this.secretManager = new sm.secretManager();

    // FIXME - see below comment

    const createHex = (len) => crypto.randomBytes(len).toString('hex').slice(0, len);
    this.loginKey = Buffer.from(createHex(64), 'hex');
    this.rememberKey = Buffer.from(createHex(64), 'hex');

    /**
     * FIXME ... the tokens never get replaced. That's bad.
     * Means restarting services allows you to continue without logging in again
     *
     * AS SOON AS WE ARE READY TO MERGE THIS:
     *  (The Coalesce GUI axios requests must LOGOUT if they receive 401)
     *  Thereafter remove this comment ...
     *  AND
     *  Delete the NEXT 2 LINES below this comment (they are created correctly above)
     * 
     *  and TEST thoroughly ...
     */
    this.loginKey = Buffer.from(keys.loginKey, 'hex');
    this.rememberKey = Buffer.from(keys.rememberKey, 'hex');
    /*  */


    this.users = new Map();
    this.server = new grpc.Server();
    this.server.addService(proto.service, {
      list: (call, callback) => this.list(call, callback),
      create: (call, callback) => this.create(call, callback),
      get: (call, callback) => this.get(call, callback),
      update: (call, callback) => this.update(call, callback),
      delete: (call, callback) => this.delete(call, callback),

      setOtp: (call, callback) => this.setOtp(call, callback),
      login: (call, callback) => this.login(call, callback),

      // FIXME -- it appears these 4 are not needed, they are duplicated lower down...
      listGroupedPermissions: (call, callback) => this.listGroupedPermissions(call, callback),
      listGroupedRoles: (call, callback) => this.listGroupedRoles(call, callback),
      updateGroupedRole: (call, callback) => this.updateGroupedRole(call, callback),
      createGroupedRole: (call, callback) => this.createGroupedRole(call, callback),

      listPermissions: (call, callback) => this.listPermissions(call, callback),
      listGroupedPermissions: (call, callback) => this.listGroupedPermissions(call, callback),

      listGroupedRoles: (call, callback) => this.listGroupedRoles(call, callback),
      updateGroupedRole: (call, callback) => this.updateGroupedRole(call, callback),
      createGroupedRole: (call, callback) => this.createGroupedRole(call, callback),

      listRoles: (call, callback) => this.listRoles(call, callback),
      updateRole: (call, callback) => this.updateRole(call, callback),
      createRole: (call, callback) => this.createRole(call, callback),
      deleteRole: (call, callback) => this.deleteRole(call, callback),

      getVersion: (call, callback) => callback(null, { code: 20000, message: 'Success', version: localConfig.version }),
    });
  }

  start(port) {
    console.log(`gRPC proto version: ${localConfig.version.major}.${localConfig.version.minor}`);
    console.log('Starting gRPC server on: ' + port);

    this.server.bind('0.0.0.0:' + port, grpc.ServerCredentials.createInsecure());
    this.server.start();
  }

  // -----------------------------------------------------------
  //
  // ---- Permission validation
  //
  // -----------------------------------------------------------

  async userHasPermission(permissionObject, group, permission) {
    if (typeof permissionObject !== 'object' || (!permissionObject.groupedPermission && !permissionObject.permission))
      throw new Error('No permissions for validation in permissionObject!');

    if (!permissionObject.groupedPermission) permissionObject.groupedPermission = this.db.ungroupedToGrouped(permissionObject.permission);

    let permissionGroups = permissionObject.groupedPermission;

    let hasPermission = permissionGroups.find((permissionGroup) => {
      let validGroup = permissionGroup.group;
      let { permissions } = permissionGroup;
      if (group !== validGroup || !permissions.includes(permission)) return false;

      return true;
    });

    if (!hasPermission) throw new Error('User does not have permission to perform this action');
  }

  // -----------------------------------------------------------
  //
  // ---- ROLES
  //
  // -----------------------------------------------------------

  async deleteRole(call, callback) {
    const r = call.request;

    console.debug('Request to DELETE Role\n', r);

    const { token, role } = r;

    if (!token) {
      console.error('Failed to delete Role - No token in request');
      callback(null, { code: 10, message: 'No token in request' });
      //call.end();
      return;
    }

    let user;
    try {
      user = await this.decodeAndValidateToken(token);
    } catch (e) {
      console.error('Failed to delete Role. Invalid token');
      callback(null, { code: 8, message: 'Invalid token' });
      return;
    }

    await this.userHasPermission(user, 'role', 'delete');

    if (user.id) {
      try {
        let result = await this.db.deleteRole(role).catch((error) => {
          throw new Error(error.message);
        });
        console.log('result', result);
        if (result.affectedRows <= 0) throw new Error('Could not delete role');
      } catch (e) {
        callback(null, { code: 12, message: e.message, role: {} });
        return;
      }

      callback(null, {
        code: 20000,
        message: 'Role deleted',
        role,
      });
    }
  }

  async updateGroupedRole(call, callback) {
    return await this.roleUpdating(call, callback, 'updateGroupedRole');
  }

  async updateRole(call, callback) {
    return await this.roleUpdating(call, callback, 'updateRole');
  }

  async roleUpdating(call, callback, which) {
    const r = call.request;

    console.debug('Request to UPDATE Role\n', r);

    const { token, role } = r;

    if (!token) {
      console.error('Failed to update Role - No token in request');
      callback(null, { code: 10, message: 'No token in request' });
      //call.end();
      return;
    }

    let user;
    try {
      user = await this.decodeAndValidateToken(token);
    } catch (e) {
      console.error('Failed to update Role. Invalid token');
      callback(null, { code: 8, message: 'Invalid token' });
      return;
    }

    await this.userHasPermission(user, 'role', 'update');

    if (user.id) {
      try {
        let result = await this.db[which](role).catch((error) => {
          throw new Error(error.message);
        });

        if (!result || result <= 0) throw new Error('Could not update role');

        // ---- permissions were stringified when stored in the DB... (see this.db.createRole)
        //      because of "pass by reference"... we must parse them again when returning
        //      This is due to GRPC proto expecting an ARRAY in the place of the 'permission' variable
        role.permission = JSON.parse(role.permission);
      } catch (e) {
        callback(null, { code: 12, message: e.message, role: {} });
        return;
      }

      callback(null, {
        code: 20000,
        message: 'Role Updated',
        role,
      });
    }
  }

  async createGroupedRole(call, callback) {
    return await this.roleCreation(call, callback, 'createGroupedRole');
  }

  async createRole(call, callback) {
    return await this.roleCreation(call, callback, 'createRole');
  }

  async roleCreation(call, callback, which) {
    const r = call.request;

    console.debug('Request to CREATE Role\n', r);

    const { token, role } = r;
    if (!token) {
      console.error('Failed to create Role - No token in request');
      callback(null, { code: 10, message: 'No token in request' });
      //call.end();
      return;
    }

    let user;
    try {
      user = await this.decodeAndValidateToken(token);
    } catch (e) {
      console.error('Failed to create Role. Invalid token');
      callback(null, { code: 8, message: 'Invalid token' });
      return;
    }

    await this.userHasPermission(user, 'role', 'create');

    if (user.id) {
      let result = {};
      try {
        result = await this.db[which](role).catch((error) => {
          throw new Error(error.message);
        });
        if (!result) throw new Error('Unable to create role');

        // ---- permissions were stringified when stored in the DB... (see this.db.createRole)
        //      because of "pass by reference"... we must parse them again when returning
        //      This is due to GRPC proto expecting an ARRAY in the place of the 'permission' variable
        role.permission = JSON.parse(role.permission);
      } catch (e) {
        callback(null, { code: 12, message: e.message, role: {} });
        return;
      }
      callback(null, {
        code: 20000,
        message: 'Role Created',
        role,
      });
    }
  }

  async listGroupedRoles(call, callback) {
    return await this.roleListing(call, callback, 'listGroupedRoles');
  }

  async listRoles(call, callback) {
    return await this.roleListing(call, callback, 'listRoles');
  }

  async roleListing(call, callback, which) {
    const r = call.request;

    console.debug('Request to list Roles\n', r);

    const { token } = r;
    if (!token) {
      console.error('Failed to list Roles - No token in request');
      callback(null, { code: 10, message: 'No token in request' });
      //call.end();
      return;
    }

    let user;
    try {
      user = await this.decodeAndValidateToken(token);
    } catch (e) {
      console.error('Failed to list Roles. Invalid token');
      callback(null, { code: 8, message: 'Invalid token' });
      return;
    }

    await this.userHasPermission(user, 'role', 'list');

    if (user.id) {
      let list = await this.db[which]();
      if (!list) {
        console.log('Unable to retrieve roles');
        callback(null, {
          code: 12,
          message: 'Unable to retrieve roles',
          role: [],
        });
        return;
      }
      callback(null, {
        code: 20000,
        message: 'Listed Roles',
        roles: list,
      });
    }
  }

  // -----------------------------------------------------------
  //
  // ---- PERMISSIONS
  //
  // -----------------------------------------------------------

  async listGroupedPermissions(call, callback) {
    return await this.permissionListing(call, callback, 'listGroupedPermissions');
  }

  async listPermissions(call, callback) {
    return await this.permissionListing(call, callback, 'listPermissions');
  }

  async permissionListing(call, callback, which) {
    const r = call.request;

    console.debug('Request to list Roles\n', r);

    const { token } = r;
    if (!token) {
      console.error('Failed to list Permissions - No token in request');
      callback(null, { code: 10, message: 'No token in request' });
      //call.end();
      return;
    }

    let user;
    try {
      user = await this.decodeAndValidateToken(token);
    } catch (e) {
      console.error('Failed to list Permissions. Invalid token');
      callback(null, { code: 8, message: 'Invalid token' });
      return;
    }

    await this.userHasPermission(user, 'user', 'list');

    //
    // the proto definition changes between the following 'which' type
    // as such, we must provide the correct proto-definition-response to match the call...
    //
    let callbackDataIdentifierName = which === 'listGroupedPermissions' ? 'permissionGroup' : 'permissions';

    if (user.id) {
      let list = await this.db[which]();
      if (!list) {
        console.log('Unable to retrieve permissions');

        callback(null, {
          code: 12,
          message: 'Unable to retrieve permissions',
          [callbackDataIdentifierName]: [],
        });
        return;
      }
      callback(null, {
        code: 20000,
        message: 'Listed permissions',
        [callbackDataIdentifierName]: list,
      });
    }
  }

  // -----------------------------------------------------------
  //
  // ---- USERS
  //
  // -----------------------------------------------------------

  async list(call, callback) {
    const r = call.request;

    console.debug('Get request to list\n', r);

    const { token } = r;
    if (!token) {
      console.error('Failed to list users. No token in request');
      callback(null, { code: 10, message: 'No token in request' });
      //call.end();
      return;
    }

    let user;
    try {
      user = await this.decodeAndValidateToken(token);
    } catch (e) {
      console.error('Failed to list users. Invalid token');
      callback(null, { code: 8, message: 'Invalid token' });
      return;
    }

    if (user.permission.includes('userlist')) {
      if (user.id) {
        let list = await this.db.list();
        if (!list) {
          console.log('Unable to get users list');
          callback(null, {
            code: 11,
            message: 'Unable to get users list',
            users: [],
          });
          return;
        }
        callback(null, {
          code: 20000,
          message: 'Success',
          users: list,
        });
      }
    } else {
      callback(null, { code: 11, message: 'Insufficient permissions' });
      return;
    }
  }

  async setOtp(call, callback) {
    call.request.email = String(call.request.email).trim().toLowerCase();
    const { email } = call.request;

    let dbUser = await this.db.getByEmail(email);
    if (!dbUser) {
      console.error('Failed to set Otp. Invalid email');
      callback(null, { code: 9, message: 'Invalid credentials' });
      return;
    }

    if (!this.userAllowedStates.includes(dbUser.state)) {
      // ---- return 23 for locked, 22 for ANY OTHER non-Active state
      let code = dbUser.state === 'Locked' ? 23 : 22;
      let message = dbUser.state === 'Locked' ? 'Account Locked' : "Not 'Active' or 'Invited'";

      callback(null, { code, message });
      return;
    }

    try {
      let otp = await generateOtp();

      // ---- Valid for a predetermined time (1 hour for now)
      const d = new Date();
      d.setHours(d.getHours() + 1);
      const forgotPassTokenExpiry = d.getTime();

      let updateData = {
        email: dbUser.email,
        info: dbUser.info,
      };

      updateData.info.forgotPassToken = otp;
      updateData.info.forgotPassTokenExpiry = forgotPassTokenExpiry;

      // ---- any failures will throw an error
      await this.db.updateByEmail(dbUser.email, updateData);

      const userData = { password: otp, firstname: dbUser.firstname, lastname: dbUser.lastname };

      //      console.log("Forgot password request successful - OTP: ", otp, forgotPassTokenExpiry);
      // ----------
      // ---- IMPORTANT NOTE:   We ASSUME that this gRPC service is NOT accessible externally, so we can sent the OTP back to the GUI Back-end
      //                        We also ASSUME that this password WILL be remove and NOT returned in the Back-End (BE) after procesing.
      //                        The sole purpose for sending it to the BE, is so we can EMAIL the user their OTP, then delete it from our records.
      //
      callback(null, { code: 0, message: "'Forgot password' request successful. Please check your email.", user: userData });
    } catch (e) {
      console.error('Forgotten password setOTP request failed.... error: ', e.message || e);
      callback(null, { code: 23, message: 'Could not set OTP. Please contact your network administrator' });
    }

    return;
  }

  async login(call, callback) {
    const r = call.request;

    let { token, credentials } = r;

    if (token) {
      let decodedRememberToken = undefined;

      try {
        decodedRememberToken = jwt.decode(token, this.rememberKey);
        // ---- no throw? - Great! It's a valid remember token
        console.log('REMEMBER TOKEN LOGIN');

        let updatedUser = await this.db.getByEmail(decodedRememberToken.email);

        if (!updatedUser || !this.userAllowedStates.includes(updatedUser.state)) {
          console.log('User is no longer active! Forcing token expiry');
          // ---- Although this user is no longer "Active" - for security reasons, we do not indicate that in the error
          callback(null, { code: 8, message: "Invalid 'remember' token" });
          return;
        }

        // ---- ok... valid remember token... valid user... let's give him a new token, and we can authenticate
        let [tokens, expires] = await this.createToken(
          updatedUser.id,
          updatedUser.email,
          updatedUser.role,
          true /* force 'remember' as we want to maintain a remember token */,
        );

        // ---- Assign a new token so that we can login
        token = tokens.login;
      } catch (e) {
        // spurious logging -- only necessary for debugging
        //console.debug("Could not validate remember token: ", e);
      }

      console.log('TOKEN RENEWAL');

      let user;
      try {
        user = await this.decodeAndValidateToken(token);
      } catch (e) {
        console.error('Failed to login. Invalid token', e);
        callback(null, { code: 8, message: 'Invalid token' });
        return;
      }

      // ---- if we are performing the `login()` method ... with a token
      //      Then we are EXPLICITLY performing a token refresh (this is how we renew the token _right before it expires_)
      let [tokens, expires] = await this.createToken(
        user.id,
        user.email,
        user.role,
        decodedRememberToken || user.hasRemember ? true : false /* only if from a remember token... OR ... 'remember me' is selected */,
      );
      user.token = tokens.login;
      user.expires = expires;

      let callbackData = {
        code: 0,
        message: 'Success',
        token: user.token,
        expires: user.expires,
      };
      if (user.hasRemember && tokens.remember) callbackData.rememberToken = tokens.remember;

      callback(null, callbackData);

      return;
    }

    let updateData = {};

    if (!credentials || !credentials.email) {
      console.error('Failed to login. Credentials missing');
      callback(null, { code: 7, message: 'Credentials missing' });
      return;
    }

    // Sanitization/
    credentials.email = String(credentials.email).trim().toLowerCase();

    let dbUser = await this.db.getByEmail(credentials.email);
    if (!dbUser) {
      console.error('Failed to login. Invalid email');
      callback(null, { code: 9, message: 'Invalid credentials' });
      return;
    }

    if (!this.userAllowedStates.includes(dbUser.state)) {
      // ---- NOTE: We log it, but we don't return a comprehensive reason for failure
      //            We may not wish the user to know WHY they are being prevented from logging in
      console.log('User is not Active/Invited!');
      // ---- return 23 for locked, 22 for ANY OTHER non-Active state
      let code = dbUser.state === 'Locked' ? 23 : 22;
      let message = dbUser.state === 'Locked' ? 'Account Locked' : 'Invalid credentials';
      callback(null, { code, message });
      return;
    }

    //
    // ---- At this stage, we know this is an attempt to login... by an 'Existing' and 'Active' user, who does not currently have a token.
    //

    console.log('CREDENTIAL LOGIN');

    // ---- This shows we are performing a login with a FORGOT PASSWORD token... (forgot password OTP sent via email -- see setOtp() method)
    //      Let's check... It hasn't expired AND the user is 'Active'
    //
    if (credentials.password === dbUser.info.forgotPassToken) {
      console.log("Attempting to login with 'forgot password' OTP");
      try {
        if (!dbUser.info.forgotPassTokenExpiry) throw new Error('Problem checking expiry');

        let d = new Date();
        const nowEpoch = d.getTime();
        const { forgotPassTokenExpiry } = dbUser.info;

        // using "String" so it copies the text exclusively - and does not pass by reference (BECAUSE WE DELETE THE ORIGINAL).
        const forgotPassToken = String(dbUser.info.forgotPassToken);

        if (isNaN(forgotPassTokenExpiry)) throw new Error("Expiry value for 'forgot password' token is invalid");

        updateData = {
          email: dbUser.email,
          info: dbUser.info,
        };

        let forgotTokenHasExpired = forgotPassTokenExpiry < nowEpoch ? true : false;

        if (forgotTokenHasExpired) {
          callback(null, { code: 26, message: 'Forgot password token expired' });
          return;
        }

        console.log("Successful login with forgot password 'OTP' token");
        // ---- clear login attempts, we are reseting the password after all.
        updateData.info.attempts = 0;

        await this.db.updateByEmail(dbUser.email, updateData);

        // retrieve latest information --- this allows the request to continue
        dbUser = await this.db.getByEmail(credentials.email);

        delete dbUser.password;

        // ---- NOTE: This state is not "strictly" valid (can't be stored in the ENUM in the DB)... however, it is only a temporary ONE TIME response.
        //            It is only used for this specific forgot password request and it is NOT SAVED, and not a valid internal state.
        dbUser.state = 'forgotPassToken';

        const [tokens, expires] = await this.createToken(
          dbUser.id,
          dbUser.email,
          dbUser.role,
          false /* otp login - force no remember me when logging in with 'forgotPassToken' */,
        );

        callback(null, {
          code: 0,
          message: 'Success',
          token: tokens.login, // No 'remember' token - explicitly disabled during 'forgot password' OTP login
          expires,
          user: dbUser,
        });
      } catch (e) {
        console.error('Failed to login with forgot password token -- Server error: ', e.message || e);
        callback(null, { code: 25, message: 'Failed attempt to login with forgot password token - Server Error' });
      }

      return;
    }

    // ---- This shows their password in the DB is not hashed yet!!!
    //		  Let's force a "passwords match UNHASHED" updateByEmail request....
    if (credentials.password === dbUser.password) {
      updateData = {
        password: dbUser.password,
        current_password: credentials.password,
      };
      await this.db.updateByEmail(dbUser.email, updateData);
      // ---- no throw? Then it succeeded and we can continue with normal validation...
      // ---- let's retrieve the new user data WITH NOW HASH'd secret
      dbUser = await this.db.getByEmail(dbUser.email);
      if (!dbUser) {
        console.error('Server Error');
        callback(null, { code: 13, message: 'Server Error' });
        return;
      }
    }

    let updateUser = {
      email: dbUser.email,
      info: dbUser.info,
    };

    if (!this.secretManager.isValidSecret(/* plaintext secret         */ credentials.password, /* stored hash.salt secret  */ dbUser.password)) {
      let { attempts } = dbUser.info;

      // ---- Prevent lockout for COALESCE ADMIN user
      //      Only increment if the user is Active
      //        Attempts should not increment if users are Inactive/Locked, as attempts are not reset when they are re-activated
      if (dbUser.id !== 1) {
        if (attempts < localConfig.lockout_attempts) {
          updateUser.info.attempts++;
          await this.db.updateByEmail(dbUser.email, updateUser);
        } else {
          updateUser.state = 'Locked';
          await this.db.updateByEmail(dbUser.email, updateUser);
          callback(null, { code: 11, message: 'User Locked Out' });
          return;
        }
      }

      console.error('Failed to login. Wrong password');
      callback(null, { code: 12, message: 'Invalid credentials' });
      return;
    }

    // ---- if a successful login has happened, we want to remove any 'forgot password' Tokens assigned to the account and clear 'attempts'
    updateUser.info.attempts = 0;
    delete updateUser.info.forgotPassToken;
    delete updateUser.info.forgotPassTokenExpiry;
    await this.db.updateByEmail(dbUser.email, updateUser);

    delete dbUser.password;

    const [tokens, expires] = await this.createToken(
      dbUser.id,
      dbUser.email,
      dbUser.role,
      credentials.remember ? true : false /* only added to the token if remember is checked */,
    );

    let callbackData = {
      code: 0,
      message: 'Success',
      token: tokens.login,
      expires,
      user: dbUser,
    };

    if (credentials.remember) callbackData.rememberToken = tokens.remember;

    callback(null, callbackData);
  }

  async create(call, callback) {
    console.debug('Create request');

    const r = call.request;
    const { token } = r;

    if (!token) {
      console.error('create: Failed to create user. No token in request');
      callback(null, { code: 10, message: 'No token in request' });
      //call.end();
      return;
    }

    let tokenInfo;
    try {
      tokenInfo = await this.decodeAndValidateToken(token);
    } catch (e) {
      console.error('create: Failed to create user. Invalid token');
      callback(null, { code: 8, message: 'Invalid token' });
      return;
    }

    if (!tokenInfo.permission.includes('userinvite')) {
      callback(null, { code: 11, message: 'Insufficient permissions' });
      return;
    }

    const { user } = r;
    console.log(user);
    // todo check user

    let { email } = user;

    // Sanitization
    email = String(email).trim().toLowerCase();

    if (!email) {
      console.error('Failed to create user. Invalid email');
      callback(null, { code: 1, message: 'Invalid email' });
      //call.end();
      return;
    }

    if (!user.password) throw new Error('NO PASSWORD!');

    const newUser = await this.db.insert(user);
    if (!newUser) {
      console.error('Failed to create user. URI already exists');
      callback(null, { code: 2, message: 'User already exists' });
      //call.end();
      return;
    }

    callback(null, {
      code: 0,
      message: 'Success',
      user: {
        id: newUser.id,
        email,
      },
    });
  }

  async get(call, callback) {
    const r = call.request;

    if (!r.token) console.debug('Get request\n', r);

    const { token } = r;

    let tokenInfo;
    try {
      tokenInfo = await this.decodeAndValidateToken(token);
    } catch (e) {
      console.error('get: Failed to get user. Invalid token');
      callback(null, { code: 8, message: 'Invalid token' });
      return;
    }

    const requestUser = r.user;
    // Sanitize
    requestUser.email = String(requestUser.email).trim().toLowerCase();

    let user;
    if (requestUser.id) {
      if (requestUser.id !== tokenInfo.id) {
        console.error('Failed to get user. Invalid user id');
        callback(null, { code: 8, message: 'Invalid user id' });
        return;
      }

      user = await this.db.get(r.id);
    } else if (requestUser.email) {
      if (requestUser.email !== tokenInfo.email) {
        console.error('Failed to get user. Invalid user email');
        callback(null, { code: 8, message: 'Invalid user email' });
        return;
      }

      user = await this.db.getByEmail(requestUser.email);
      //console.debug(user);
    } else {
      console.error('Failed to get user. Id or email required');
      callback(null, { code: 3, message: 'Id or email required' });
      //call.end();
      return;
    }

    // ---- Add the explicitly retrieved permissions to the result
    user.permission = tokenInfo.permission;
    user.groupedPermission = tokenInfo.groupedPermission;
    delete user.password;
    callback(null, {
      code: 0,
      message: 'Success',
      user,
    });
  }

  async update(call, callback) {
    const r = call.request;
    const { token } = r;

    let tokenInfo;
    try {
      tokenInfo = await this.decodeAndValidateToken(token);
    } catch (e) {
      console.error('update: Failed to update user. Invalid token');
      callback(null, { code: 8, message: 'Invalid token' });
      return;
    }

    const requestUser = r.user;
    requestUser.email = String(requestUser.email).trim().toLowerCase();
    const { id, email } = requestUser;

    if (id) {
      // ---- If attempting to update _another_ user, and don't have permissions, then invalid.
      if (id !== tokenInfo.id && !tokenInfo.permission.includes('userupdate')) {
        console.error('Insufficient permissions, cannot update other users');
        callback(null, { code: 9, message: 'Insufficient permission' });
        return;
      }

      delete requestUser.id;
      try {
        await this.db.update(id, requestUser);
        let userResponseData = { id };
        if (requestUser.state) userResponseData.state = requestUser.state;
        callback(null, { code: 0, message: 'Success', user: userResponseData });
      } catch (e) {
        callback(null, { code: 1, message: e, user: { id } });
      }
    } else if (email) {
      if (email !== tokenInfo.email && !tokenInfo.permission.includes('userupdate')) {
        console.error('Insufficient permissions, cannot update other users');
        callback(null, { code: 9, message: 'Insufficient permission' });
        return;
      }

      delete requestUser.email;

      try {
        let feedback = await this.db.updateByEmail(email, requestUser);

        let userResponseData = { email };
        if (requestUser.state) userResponseData.state = requestUser.state;
        callback(null, {
          code: 0,
          message: 'Success',
          user: userResponseData,
        });
      } catch (e) {
        callback(null, { code: 1, message: e, user: { email } });
      }
    } else {
      callback(null, { code: 3, message: 'Id or URI required' });
      //call.end();
    }
  }

  async delete(call, callback) {
    const u = call.request.user;
    const { token } = call.request;

    let tokenInfo;
    try {
      tokenInfo = this.decodeAndValidateToken(token);
    } catch (e) {
      console.error('delete: Failed to delete user. Invalid token');
      callback(null, { code: 8, message: e });
      return;
    }

    u.email = String(u.email).trim().toLowerCase();
    const { id, email } = u;

    if (id) {
      if (id === tokenInfo.id) {
        console.error('Cannot delete self');
        callback(null, { code: 9, message: 'Delete Self' });
        return;
      }

      delete u.id;
      try {
        await this.db.delete(id);
        callback(null, { code: 0, message: 'Successfully deleted user', user: { id } });
      } catch (e) {
        callback(null, { code: 1, message: e, user: { id } });
      }
    } else if (email) {
      if (email === tokenInfo.email) {
        console.error('Cannot delete self');
        callback(null, { code: 9, message: 'Delete Self' });
        return;
      }

      delete u.email;
      try {
        await this.db.deleteByEmail(email);
        callback(null, { code: 0, message: 'Successfully deleted user', user: { email } });
      } catch (e) {
        callback(null, { code: 1, message: e, user: { email } });
      }
    } else {
      callback(null, { code: 3, message: 'Id or URI required' });
      //call.end();
    }
  }

  generateSeqno() {
    return Math.floor(Math.random() * 1000000000) + 1000000000;
  }

  async createToken(id, email, role, rememberMe) {
    const loginExpiryDate = new Date();
    loginExpiryDate.setHours(loginExpiryDate.getHours() + 1);
    // ---- Testing
    //loginExpiryDate.setSeconds(loginExpiryDate.getSeconds() + 120);
    const expires = loginExpiryDate.getTime();

    let permission = await this.db.getRolesPermissionList(role);
    let groupedPermission = await this.db.getGroupedRolesPermissionList(role);

    let loginTokenData = {
      id,
      email,
      role,
      permission,
      groupedPermission,
      expires,
      hasRemember: rememberMe,
    };

    let tokens = {};

    tokens.login = jwt.encode(loginTokenData, this.loginKey);

    if (rememberMe) {
      let rememberTokenData = { id, email };
      const rememberToken = jwt.encode(loginTokenData, this.rememberKey);
      tokens.remember = rememberToken;
    }

    return [tokens, expires];
  }

  async decodeAndValidateToken(token) {
    const o = jwt.decode(token, this.loginKey);

    console.log('decoded token\n', o);

    // ---- if we haven't expired... return the old decoded token
    if (hasExpired(o)) throw new Error('Token expired');

    let updatedUser = await this.db.getByEmail(o.email);

    // ---- Although this user is no longer "Active" - for security reasons, we do not indicate that from the error returned
    if (!this.userAllowedStates.includes(updatedUser.state)) {
      console.log('User is no longer active! Forcing token expiry');
      throw new Error('Token expired');
    }

    // ---- Update permission during token renewal
    o.role = updatedUser.role;
    o.permission = await this.db.getRolesPermissionList(o.role);
    //      console.trace("Token updated permissions and roles ... \n", o);
    return o;
  }
}

async function generateOtp(length = 32) {
  if (isNaN(length) || length <= 5 || length > 64) length = 64;

  // -- partial break to nice cryptographic randomness --- but not serious, this is an OTP, not a long life password
  //    The / and + characters (because the resulting string is base 64) are replaced with 'a' characters ---
  //    Keeps the password clean, can be removed if necessary at a later stage
  //
  return [...Array(length)]
    .map(() => crypto.randomBytes(8).toString('base64')[2])
    .join('')
    .toLowerCase()
    .replace(/[\/\+]/g, 'a');
}

function hasExpired(data) {
  return Date.now() > data.expires;
}

module.exports = {
  Server,
};

if (require.main === module) {
  const db = require(path.join(__dirname, '../database'));

  const srv = new Server(new db.TestDatabase());

  srv.start();
}
