const http = require('http');
const express = require('express');
const localConfig = require('./config/local.json');
const path = require('path');
require('dotenv').config()
require('./logger');

console.info("CI / CD Build anv Version Information");
console.info("CI_BUILD_DATETIME: ", process.env.CI_BUILD_DATETIME);
console.info("CI_BUILD_NUMBER: ", process.env.CI_BUILD_NUMBER);
console.info("CI_COMMIT_REF: ", process.env.CI_COMMIT_REF);
console.info("CI_GITHUB_TAG: ", process.env.CI_GITHUB_TAG);
console.info("CI_BRANCH_NAME: ", process.env.CI_BRANCH_NAME);
console.info("CI_DOCKER_TAG: ", process.env.CI_DOCKER_TAG);

const grpc = require(path.join(__dirname, 'grpc'));
const db = require(path.join(__dirname, 'database'));

const app = express();

const server = http.createServer(app);
const database = process.env.MEMORY_DATABASE ? new db.TestDatabase() : new db.Database();
const dburl = process.env.DB_URL || localConfig.db;

app.use(express.json());

const serviceManager = require('./services/service-manager');
require('./services/index')(app);
require('./routers/index')(app, server, database);

const port = process.env.PORT || localConfig.port;
const portgrpc = process.env.GRPC_PORT || localConfig.grpc.port;
// JWT keys
const loginKey = process.env.GRPC_KEY || localConfig.grpc.loginKey;
const rememberKey = process.env.GRPC_REMEMBER_KEY || localConfig.grpc.rememberKey;
const servergrpc = new grpc.Server(database, { loginKey, rememberKey });

app.use((req, res, next) => {
  res.sendFile(path.join(__dirname, '../public', '404.html'));
});

app.use((err, req, res, next) => {
  res.sendFile(path.join(__dirname, '../public', '500.html'));
});

async function run() {
  try {
    await database.init(dburl);
  } catch (e) {
    console.error('Failed to initilize database', e);
    return;
  }

  servergrpc.start(portgrpc);
  server.listen(port, () => {
    console.debug(`OpenAPI (Swagger) spec is available at http://localhost:${port}/swagger/api`);
    console.debug(`Swagger UI is available at http://localhost:${port}/explorer`);
  });
}

run();

module.exports = server;
