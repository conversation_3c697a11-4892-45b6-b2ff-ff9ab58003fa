const express = require('express');
const URL = require('url');
const querystring = require('querystring');
const bodyParser = require('body-parser');

module.exports = function(app, basepath, db) {
  const router = express.Router();
  //app.use(bodyParser.urlencoded({extended: false})); 
  //app.use(bodyParser.json());

  router.get('/user/:userEmail', function (req, res, next) {
    const email = getEmail(req.url);
    //console.debug('GET email request: ', email);

    db.getByEmail(email)
      .then(r => {
        //console.debug(r);
        if (r) {
          //r.picture = r.picture.toString();
          res.json({ code: 0, message: 'success', user: r });
        }
        else
          res.status(404).json({ code: 1, message: 'not found', user: { email } });
      });
     
  })

  router.put('/user/:userEmail', function (req, res, next) {
    console.debug('User PUT: ');
    const email = getEmail(req.url);
    const info = req.body;

    db.updateByEmail(email, info)
      .then(r => {
        if (r) 
          res.json({ code: 0, message: 'success', user: { email } });
        else
          res.status(404).json({ code: 1, message: 'not found', user: { email } });
      });
  })

  router.delete('/user/:userEmail', function (req, res, next) {
    res.json({});
  })

  router.post('/user', function (req, res, next) {
    console.debug('User POST: ', req.body);

    const info = req.body;
    if (!info.email) {
      res.status(400).json({ code: 2, message: 'email missing', user: {} });
      return;
    }

    info.email = querystring.unescape(info.email);

    db.insert(info)
      .then(r => {
        if (r)
          res.json({ code: 0, message: 'success', user: r });
        else
          res.status(404).json({ code: 3, message: 'already exists', user: { email: info.email } });
      });
  })

  app.use(basepath, router);
}

function getEmail(url) {
  const query = URL.parse(url, true);
  return querystring.unescape(query.pathname.split('/').slice(-1));
}
