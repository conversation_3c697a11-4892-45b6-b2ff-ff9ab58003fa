const express = require('express');
const URL = require('url');

module.exports = (app, basepath, db) => {
  const router = express.Router();

  router.get('/uid/:userId', (req, res, next) => {
    const id = getId(req.url);
    //console.debug('GET uid request: ', id);

    db.get(id).then((r) => {
      //console.debug(r);
      if (r) {
        //r.picture = r.picture.toString();
        res.json({ code: 0, message: 'success', user: r });
      } else res.status(404).json({ code: 1, message: 'not found', user: { id } });
    });
  });

  router.put('/uid/:userId', (req, res, next) => {
    const id = getId(req.url);
    const info = req.body;
    //console.debug('GET email request: ', email);

    db.update(id, info).then((r) => {
      console.debug(r);
      if (r) {
        //r.picture = r.picture.toString();
        res.json({ code: 0, message: 'success', user: { id } });
      } else res.status(404).json({ code: 1, message: 'not found', user: { id } });
    });
  });

  router.delete('/uid/:userId', (req, res, next) => {
    res.json({});
  });

  app.use(basepath, router);
};

function getId(url) {
  //console.debug('URL: ', url);
  const query = URL.parse(url, true);
  const path = query.pathname.split('/');
  return Number(path[path.length - 1]);
}
