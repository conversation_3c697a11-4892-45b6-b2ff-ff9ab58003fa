{"appenders": {"stdout": {"type": "console", "layout": {"type": "pattern", "pattern": "[%d] [%p]  %m"}}, "consoleAir": {"type": "console", "layout": {"type": "pattern", "pattern": "%d{ABSOLUTE} %[[%p]:%] %m"}}, "consoleApi": {"type": "console", "layout": {"type": "pattern", "pattern": "%d{ABSOLUTE} %c %[[%p]:%] %m"}}}, "categories": {"default": {"appenders": ["stdout"], "level": "trace", "layout": {"type": "colored"}}, "api": {"appenders": ["consoleApi"], "level": "debug", "layout": {"type": "colored"}}, "www": {"appenders": ["consoleApi"], "level": "debug", "layout": {"type": "colored"}}}, "replaceConsole": true}