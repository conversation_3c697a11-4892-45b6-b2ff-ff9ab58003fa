.auto-test:
  image: "node:12.16.1-alpine3.11"

.auto-unit-test:
  extends: .auto-test
  stage: test_unit
  script:
    - npm install
    - npm test
  artifacts:
    paths:
      - junit.xml
      - test-report.html
    reports:
      junit: junit.xml
  
Unit Tests:
  extends: .auto-unit-test
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: '$CI_COMMIT_BRANCH == "qa"'

Unit Tests for Tag:
  extends: .auto-unit-test
  rules:
    - if: '$CI_COMMIT_TAG'

Integration Tests:
  stage: test_integration
  script: 
    - echo "Integration Tests"
  cache: {}
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
