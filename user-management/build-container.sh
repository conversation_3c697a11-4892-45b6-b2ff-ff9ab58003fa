#!/bin/bash

REGISTRY='docker.csys.eu.org'
MICROSERVICE_NAME='user-management'
VERSION='1.0.0-rc-1'

DOCKER=`which docker`

$DOCKER build -t $REGISTRY/$MICROSERVICE_NAME:latest .
if [ $? -eq 1 ]
then
  exit 1 
fi

$DOCKER tag $REGISTRY/$MICROSERVICE_NAME:latest $REGISTRY/$MICROSERVICE_NAME:$VERSION
if [ $? -eq 1 ]
then
  exit 1 
fi

$DOCKER push $REGISTRY/$MICROSERVICE_NAME:latest
if [ $? -eq 1 ]
then
  exit 1 
fi

$DOCKER push $REGISTRY/$MICROSERVICE_NAME:$VERSION
if [ $? -eq 1 ]
then
  exit 1 
fi

